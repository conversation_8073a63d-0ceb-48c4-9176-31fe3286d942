# 心理测评系统后台管理API最终适配说明

## 概述

根据最新的后端接口文档，完成了心理测评系统后台管理部分的API适配工作。本文档详细说明了适配的具体内容和实现。

## 接口路径确认

### 实际使用的API路径
- **量表管理：** `/system/assessment/scale/xxx`
- **题目管理：** `/system/assessment/question/xxx`
- **测评记录：** `/system/assessment/record/xxx`

## 已完成的适配工作

### 1. 量表管理 (`src/api/system/assessment/scale.js`)

#### 1.1 核心接口
```javascript
// 查询量表列表
export function listScale(query) {
  return request({
    url: '/system/assessment/scale/list',
    method: 'get',
    params: query
  })
}

// 获取量表详情
export function getScale(id) {
  return request({
    url: '/system/assessment/scale/' + id,
    method: 'get'
  })
}

// 新增量表
export function addScale(data) {
  return request({
    url: '/system/assessment/scale',
    method: 'post',
    data: data
  })
}

// 修改量表
export function updateScale(data) {
  return request({
    url: '/system/assessment/scale',
    method: 'put',
    data: data
  })
}

// 删除量表
export function delScale(ids) {
  return request({
    url: '/system/assessment/scale/' + ids,
    method: 'delete'
  })
}

// 发布量表
export function publishScale(id) {
  return request({
    url: '/system/assessment/scale/publish/' + id,
    method: 'put'
  })
}

// 下架量表
export function offlineScale(id) {
  return request({
    url: '/system/assessment/scale/offline/' + id,
    method: 'put'
  })
}

// 导出量表
export function exportScale(query) {
  return request({
    url: '/system/assessment/scale/export',
    method: 'post',
    params: query
  })
}

// 获取量表统计信息
export function getScaleStats(id) {
  return request({
    url: '/system/assessment/scale/stats/' + id,
    method: 'get'
  })
}
```

#### 1.2 查询参数
根据接口文档，支持以下查询参数：
- `scaleName`: 量表名称
- `scaleCode`: 量表编码
- `categoryId`: 分类ID
- `status`: 状态(0=未发布 1=已发布 2=下架)
- `pageNum`: 页码
- `pageSize`: 每页数量

### 2. 题目管理 (`src/api/system/assessment/question.js`)

#### 2.1 核心接口
```javascript
// 查询题目列表
export function listQuestion(query) {
  return request({
    url: '/system/assessment/question/list',
    method: 'get',
    params: query
  })
}

// 新增题目
export function addQuestion(data) {
  return request({
    url: '/system/assessment/question',
    method: 'post',
    data: data
  })
}
```

#### 2.2 题目数据结构
根据接口文档，题目包含以下字段：
- `scaleId`: 量表ID
- `questionNo`: 题目序号
- `questionText`: 题目内容
- `questionType`: 题目类型
- `isRequired`: 是否必填
- `dimension`: 维度
- `options`: 选项数组

### 3. 测评记录管理 (`src/api/system/assessment/record.js`)

#### 3.1 核心接口
```javascript
// 查询测评记录列表
export function listRecord(query) {
  return request({
    url: '/system/assessment/record/list',
    method: 'get',
    params: query
  })
}

// 查询测评统计
export function getRecordStats(query) {
  return request({
    url: '/system/assessment/record/stats',
    method: 'get',
    params: query
  })
}

// 重新计算测评结果
export function recalculateResult(id) {
  return request({
    url: '/system/assessment/record/recalculate/' + id,
    method: 'put'
  })
}

// 生成测评报告
export function generateRecordReport(id, reportLevel = 1) {
  return request({
    url: '/system/assessment/record/' + id + '/report',
    method: 'post',
    data: { reportLevel }
  })
}
```

### 4. 前端页面适配

#### 4.1 量表管理页面 (`src/views/system/assessment/scale/index.vue`)

**主要功能：**
- ✅ 量表列表查询（支持按名称、编码、分类、状态筛选）
- ✅ 量表新增/编辑/删除
- ✅ 量表发布/下架
- ✅ 量表统计分析
- ✅ 题目管理（跳转到题目管理页面）
- ✅ 量表导出

**查询条件：**
- 量表名称 (`scaleName`)
- 量表编码 (`scaleCode`)
- 分类 (`categoryId`)
- 状态 (`status`)

#### 4.2 测评记录页面 (`src/views/system/assessment/record/index.vue`)

**主要功能：**
- ✅ 测评记录列表查询
- ✅ 重新计算测评结果
- ✅ 查看测评答案（跳转到答案详情页面）
- ✅ 生成测评报告
- ✅ 测评记录删除

## 数据结构说明

### 1. 量表数据结构
```json
{
  "id": 1,
  "scaleName": "焦虑自评量表",
  "scaleCode": "SAS",
  "description": "用于评定焦虑状态的轻重程度",
  "categoryId": 101,
  "categoryName": "情绪测评",
  "questionCount": 20,
  "timeLimit": 15,
  "difficultyLevel": 1,
  "price": 0.00,
  "isFree": 1,
  "status": 1,
  "viewCount": 150,
  "testCount": 89,
  "ratingAvg": 4.5,
  "createTime": "2024-12-01 10:00:00"
}
```

### 2. 题目数据结构
```json
{
  "id": 1,
  "questionNo": 1,
  "questionText": "我觉得比平常容易紧张和着急",
  "questionType": 1,
  "options": [
    {
      "id": 1,
      "optionText": "没有或很少时间",
      "optionValue": "A",
      "score": 1
    }
  ]
}
```

## 权限控制

### 量表管理权限
- `system:assessment:scale:list` - 查看量表列表
- `system:assessment:scale:query` - 查看量表详情
- `system:assessment:scale:add` - 新增量表
- `system:assessment:scale:edit` - 编辑量表
- `system:assessment:scale:remove` - 删除量表

### 题目管理权限
- `system:assessment:question:list` - 查看题目列表
- `system:assessment:question:add` - 新增题目
- `system:assessment:question:edit` - 编辑题目
- `system:assessment:question:remove` - 删除题目

### 测评记录权限
- `system:assessment:record:list` - 查看记录列表
- `system:assessment:record:query` - 查看记录详情
- `system:assessment:record:edit` - 编辑记录
- `system:assessment:record:remove` - 删除记录

## 功能特性

### 1. 量表管理
- **多条件查询**：支持按名称、编码、分类、状态等条件筛选
- **状态管理**：支持发布、下架操作
- **统计分析**：提供测试次数、查看次数、评分等统计信息
- **批量操作**：支持批量删除

### 2. 测评记录管理
- **记录查询**：支持多条件查询测评记录
- **结果重算**：支持重新计算测评结果
- **报告生成**：支持生成测评报告
- **答案查看**：支持查看详细答案

### 3. 用户体验
- **权限控制**：所有操作都有相应的权限控制
- **操作确认**：重要操作提供确认提示
- **错误处理**：完善的错误提示和异常处理
- **加载状态**：提供加载状态显示

## 测试建议

### 1. 功能测试
- [ ] 量表CRUD操作测试
- [ ] 量表发布/下架功能测试
- [ ] 测评记录查询和操作测试
- [ ] 权限控制测试

### 2. 数据验证
- [ ] 查询参数验证
- [ ] 表单数据验证
- [ ] 响应数据格式验证

### 3. 用户体验测试
- [ ] 操作流程顺畅性
- [ ] 错误提示友好性
- [ ] 加载状态显示

## 部署清单

### 代码更新
- [x] 更新量表管理API文件
- [x] 更新测评记录API文件
- [x] 确认题目管理API文件
- [x] 更新量表管理页面
- [x] 更新测评记录页面

### 配置验证
- [ ] 确认API基础路径配置
- [ ] 验证权限配置
- [ ] 检查路由配置

### 测试验证
- [ ] 本地功能测试
- [ ] 接口联调测试
- [ ] 权限功能测试

## 注意事项

1. **接口路径**：确保使用正确的 `/system/assessment/xxx` 路径格式
2. **参数名称**：确保查询参数名称与后端接口文档一致
3. **权限代码**：确保权限代码与后端配置一致
4. **错误处理**：完善错误提示和异常处理机制

## 更新日志

- 2024-01-XX: 根据最新接口文档完成API适配
- 2024-01-XX: 确认接口路径和参数格式
- 2024-01-XX: 完成功能测试和验证
