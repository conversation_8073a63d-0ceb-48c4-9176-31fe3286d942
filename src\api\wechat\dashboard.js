import request from '@/utils/request'

// 获取仪表板统计数据
export function getDashboardStats() {
  return request({
    url: '/system/dashboard/stats',
    method: 'get'
  })
}

// 获取今日排班
export function getTodaySchedule() {
  return request({
    url: '/system/dashboard/todaySchedule',
    method: 'get'
  })
}

// 获取今日预约
export function getTodayAppointments() {
  return request({
    url: '/system/dashboard/todayAppointments',
    method: 'get'
  })
}

// 获取预约趋势数据
export function getAppointmentTrend(days = 7) {
  return request({
    url: '/system/dashboard/appointmentTrend',
    method: 'get',
    params: { days }
  })
}

// 获取咨询师工作量统计
export function getCounselorWorkload() {
  return request({
    url: '/system/dashboard/counselorWorkload',
    method: 'get'
  })
}
