<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="量表" prop="scaleId">
        <el-select
          v-model="queryParams.scaleId"
          placeholder="请选择量表"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="scale in scaleOptions"
            :key="scale.id"
            :label="scale.scaleName"
            :value="scale.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="题目内容" prop="questionText">
        <el-input
          v-model="queryParams.questionText"
          placeholder="请输入题目内容"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="题目类型" prop="questionType">
        <el-select
          v-model="queryParams.questionType"
          placeholder="请选择题目类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in question_type_options"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:assessment:question:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:assessment:question:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:assessment:question:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Sort"
          @click="handleSort"
          v-hasPermi="['system:assessment:question:edit']"
        >排序</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:assessment:question:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="questionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="题号" align="center" prop="questionNo" width="80" />
      <el-table-column label="题目内容" align="center" prop="questionText" :show-overflow-tooltip="true" />
      <el-table-column label="题目类型" align="center" prop="questionType" width="100">
        <template #default="scope">
          <dict-tag :options="question_type_options" :value="scope.row.questionType" />
        </template>
      </el-table-column>
      <el-table-column label="是否必答" align="center" prop="isRequired" width="80">
        <template #default="scope">
          <dict-tag :options="yes_no_options" :value="scope.row.isRequired" />
        </template>
      </el-table-column>
      <el-table-column label="维度" align="center" prop="dimension" width="100" />
      <el-table-column label="选项数量" align="center" prop="optionCount" width="80" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleViewOptions(scope.row)" v-hasPermi="['system:assessment:question:query']">选项</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:assessment:question:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:assessment:question:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改题目对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="questionRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属量表" prop="scaleId">
              <el-select v-model="form.scaleId" placeholder="请选择量表" style="width: 100%">
                <el-option
                  v-for="scale in scaleOptions"
                  :key="scale.id"
                  :label="scale.scaleName"
                  :value="scale.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="题号" prop="questionNo">
              <el-input-number v-model="form.questionNo" :min="1" :max="999" placeholder="请输入题号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="题目内容" prop="questionText">
          <el-input v-model="form.questionText" type="textarea" placeholder="请输入题目内容" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="题目类型" prop="questionType">
              <el-select v-model="form.questionType" placeholder="请选择题目类型">
                <el-option
                  v-for="dict in question_type_options"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否必答" prop="isRequired">
              <el-radio-group v-model="form.isRequired">
                <el-radio
                  v-for="dict in yes_no_options"
                  :key="dict.value"
                  :value="parseInt(dict.value)"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="维度" prop="dimension">
          <el-input v-model="form.dimension" placeholder="请输入维度" />
        </el-form-item>
        
        <!-- 选项部分 -->
        <el-divider content-position="center">选项设置</el-divider>
        <el-form-item v-for="(option, index) in form.options" :key="index" :label="'选项' + (index + 1)">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input v-model="option.optionText" placeholder="请输入选项文本" />
            </el-col>
            <el-col :span="4">
              <el-input v-model="option.optionValue" placeholder="选项值" />
            </el-col>
            <el-col :span="4">
              <el-input-number v-model="option.score" :min="0" placeholder="分值" />
            </el-col>
            <el-col :span="4">
              <el-button type="danger" icon="Delete" @click.prevent="removeOption(index)">删除</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addOption">添加选项</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选项查看对话框 -->
    <el-dialog title="选项详情" v-model="optionsOpen" width="800px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="题目内容">{{ currentQuestion.questionText }}</el-descriptions-item>
      </el-descriptions>
      <el-table :data="optionsList" style="margin-top: 15px;">
        <el-table-column label="选项文本" prop="optionText" />
        <el-table-column label="选项值" prop="optionValue" width="100" />
        <el-table-column label="分值" prop="score" width="100" />
        <el-table-column label="排序" prop="orderNum" width="100" />
      </el-table>
    </el-dialog>

    <!-- 题目排序对话框 -->
    <el-dialog title="题目排序" v-model="sortOpen" width="800px" append-to-body>
      <el-form :model="sortForm" label-width="100px">
        <el-form-item label="所属量表">
          <el-select v-model="sortForm.scaleId" placeholder="请选择量表" @change="getSortQuestions" style="width: 100%">
            <el-option
              v-for="scale in scaleOptions"
              :key="scale.id"
              :label="scale.scaleName"
              :value="scale.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <el-table v-if="sortForm.scaleId" :data="sortQuestions" row-key="id">
        <el-table-column label="题号" width="80">
          <template #default="scope">
            <el-input-number v-model="scope.row.questionNo" :min="1" :max="999" />
          </template>
        </el-table-column>
        <el-table-column label="题目内容" prop="questionText" :show-overflow-tooltip="true" />
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Top" @click="moveUp(scope.$index)" :disabled="scope.$index === 0">上移</el-button>
            <el-button link type="primary" icon="Bottom" @click="moveDown(scope.$index)" :disabled="scope.$index === sortQuestions.length - 1">下移</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSort">保存排序</el-button>
          <el-button @click="sortOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Question">
import { listQuestion, getQuestion, delQuestion, addQuestion, updateQuestion, exportQuestion, getQuestionsByScale, updateQuestionOrder } from "@/api/system/assessment/question";
import { getOptionsByQuestion } from "@/api/system/assessment/option";
import { listScale } from "@/api/system/assessment/scale";

const { proxy } = getCurrentInstance();
const { question_type_options, yes_no_options } = proxy.useDict('question_type', 'sys_yes_no');

const questionList = ref([]);
const scaleOptions = ref([]);
const optionsList = ref([]);
const sortQuestions = ref([]);
const open = ref(false);
const optionsOpen = ref(false);
const sortOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const currentQuestion = ref({});

const data = reactive({
  form: {
    options: []
  },
  sortForm: {
    scaleId: null
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    scaleId: null,
    questionText: null,
    questionType: null
  },
  rules: {
    scaleId: [
      { required: true, message: "所属量表不能为空", trigger: "change" }
    ],
    questionNo: [
      { required: true, message: "题号不能为空", trigger: "blur" }
    ],
    questionText: [
      { required: true, message: "题目内容不能为空", trigger: "blur" }
    ],
    questionType: [
      { required: true, message: "题目类型不能为空", trigger: "change" }
    ],
    isRequired: [
      { required: true, message: "是否必答不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, sortForm, rules } = toRefs(data);

/** 查询题目列表 */
function getList() {
  loading.value = true;
  // 从URL参数中获取scaleId
  const urlParams = new URLSearchParams(window.location.search);
  const scaleIdParam = urlParams.get('scaleId');
  if (scaleIdParam && !queryParams.value.scaleId) {
    queryParams.value.scaleId = parseInt(scaleIdParam);
  }
  
  listQuestion(queryParams.value).then(response => {
    questionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询量表列表 */
function getScaleList() {
  listScale().then(response => {
    scaleOptions.value = response.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    scaleId: null,
    questionNo: null,
    questionText: null,
    questionType: 1,
    isRequired: 1,
    dimension: null,
    options: []
  };
  
  // 从URL参数中获取scaleId
  const urlParams = new URLSearchParams(window.location.search);
  const scaleIdParam = urlParams.get('scaleId');
  if (scaleIdParam) {
    form.value.scaleId = parseInt(scaleIdParam);
  }
  
  proxy.resetForm("questionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 保留URL中的scaleId参数
  const urlParams = new URLSearchParams(window.location.search);
  const scaleIdParam = urlParams.get('scaleId');
  if (scaleIdParam) {
    queryParams.value.scaleId = parseInt(scaleIdParam);
  }
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 添加默认选项
  addDefaultOptions();
  open.value = true;
  title.value = "添加题目";
}

/** 添加默认选项 */
function addDefaultOptions() {
  form.value.options = [
    { optionText: "选项A", optionValue: "A", score: 1, orderNum: 1 },
    { optionText: "选项B", optionValue: "B", score: 2, orderNum: 2 },
    { optionText: "选项C", optionValue: "C", score: 3, orderNum: 3 },
    { optionText: "选项D", optionValue: "D", score: 4, orderNum: 4 }
  ];
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getQuestion(_id).then(response => {
    form.value = response.data;
    // 获取选项
    getOptionsByQuestion(_id).then(res => {
      form.value.options = res.data || [];
    });
    open.value = true;
    title.value = "修改题目";
  });
}

/** 查看选项 */
function handleViewOptions(row) {
  currentQuestion.value = row;
  getOptionsByQuestion(row.id).then(response => {
    optionsList.value = response.data || [];
    optionsOpen.value = true;
  });
}

/** 添加选项 */
function addOption() {
  const newOrder = form.value.options.length + 1;
  const newOption = {
    optionText: "",
    optionValue: String.fromCharCode(64 + newOrder), // A, B, C...
    score: newOrder,
    orderNum: newOrder
  };
  form.value.options.push(newOption);
}

/** 删除选项 */
function removeOption(index) {
  form.value.options.splice(index, 1);
  // 重新排序
  form.value.options.forEach((item, idx) => {
    item.orderNum = idx + 1;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["questionRef"].validate(valid => {
    if (valid) {
      // 验证选项
      if (form.value.options.length === 0) {
        proxy.$modal.msgError("请至少添加一个选项");
        return;
      }
      
      // 验证选项内容
      for (let i = 0; i < form.value.options.length; i++) {
        if (!form.value.options[i].optionText) {
          proxy.$modal.msgError(`选项${i+1}的文本不能为空`);
          return;
        }
      }
      
      if (form.value.id != null) {
        updateQuestion(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addQuestion(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除题目编号为"' + _ids + '"的数据项？').then(function() {
    return delQuestion(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/assessment/question/export', {
    ...queryParams.value
  }, `question_${new Date().getTime()}.xlsx`)
}

/** 排序按钮操作 */
function handleSort() {
  sortForm.value.scaleId = null;
  sortQuestions.value = [];
  sortOpen.value = true;
}

/** 获取排序题目 */
function getSortQuestions() {
  if (!sortForm.value.scaleId) return;
  
  getQuestionsByScale(sortForm.value.scaleId).then(response => {
    sortQuestions.value = response.data || [];
    // 按题号排序
    sortQuestions.value.sort((a, b) => a.questionNo - b.questionNo);
  });
}

/** 上移题目 */
function moveUp(index) {
  if (index === 0) return;
  const temp = sortQuestions.value[index];
  sortQuestions.value[index] = sortQuestions.value[index - 1];
  sortQuestions.value[index - 1] = temp;
  // 更新题号
  updateQuestionNumbers();
}

/** 下移题目 */
function moveDown(index) {
  if (index === sortQuestions.value.length - 1) return;
  const temp = sortQuestions.value[index];
  sortQuestions.value[index] = sortQuestions.value[index + 1];
  sortQuestions.value[index + 1] = temp;
  // 更新题号
  updateQuestionNumbers();
}

/** 更新题号 */
function updateQuestionNumbers() {
  sortQuestions.value.forEach((item, index) => {
    item.questionNo = index + 1;
  });
}

/** 提交排序 */
function submitSort() {
  const data = {
    scaleId: sortForm.value.scaleId,
    questions: sortQuestions.value.map(item => ({
      id: item.id,
      questionNo: item.questionNo
    }))
  };
  
  updateQuestionOrder(data).then(() => {
    proxy.$modal.msgSuccess("排序保存成功");
    sortOpen.value = false;
    getList();
  });
}

onMounted(() => {
  getList();
  getScaleList();
});
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}
</style>
