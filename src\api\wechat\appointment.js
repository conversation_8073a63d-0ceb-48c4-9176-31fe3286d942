import request from '@/utils/request'

// 查询预约列表
export function listAppointment(query) {
  return request({
    url: '/system/appointment/list',
    method: 'get',
    params: query
  })
}

// 查询预约详细信息
export function getAppointment(id) {
  return request({
    url: `/system/appointment/${id}`,
    method: 'get'
  })
}

// 确认预约
export function confirmAppointment(id) {
  return request({
    url: `/system/appointment/${id}/confirm`,
    method: 'put'
  })
}

// 取消预约
export function cancelAppointment(id, cancelReason) {
  return request({
    url: `/system/appointment/${id}/cancel`,
    method: 'put',
    data: { cancelReason }
  })
}

// 批量确认预约
export function batchConfirmAppointment(ids) {
  return request({
    url: '/system/appointment/batchConfirm',
    method: 'put',
    data: ids
  })
}

// 批量取消预约
export function batchCancelAppointment(ids, cancelReason) {
  return request({
    url: '/system/appointment/batchCancel',
    method: 'put',
    data: { ids, cancelReason }
  })
}

// 导出预约数据
export function exportAppointment(query) {
  return request({
    url: '/system/appointment/export',
    method: 'post',
    params: query
  })
}
