# 前端数据映射修正说明

## 概述
根据后端返回的实际数据结构，已对前端页面的数据映射进行了修正，确保前端能正确显示和处理后端数据。

## 后端数据结构分析

### 1. 课程数据结构
```json
{
  "id": 1000,
  "title": "情绪管理入门",
  "summary": "学会识别和调节自己的情绪",
  "price": 199,
  "salesCount": 1,
  "chapterCount": 6,
  "trialChapterCount": 1,
  "instructorId": 100,
  "coverImage": "image/course1.jpg",
  "difficultyLevel": 1,
  "durationTotal": 12000,
  "viewCount": 1,
  "ratingAvg": 4.3,
  "ratingCount": 2,
  "isFree": 0,
  "tags": "[\"情绪管理\",\"压力缓解\"]",
  "status": 1,
  "instructor": {
    "id": 100,
    "name": "张老师",
    "title": "资深心理咨询师"
  },
  "categories": [
    {
      "categoryId": 23,
      "parentId": 13,
      "categoryName": "课程分类"
    }
  ]
}
```

### 2. 分类数据结构
```json
{
  "categoryId": 13,
  "parentId": 0,
  "categoryName": "课程",
  "orderNum": 3,
  "status": "0",
  "children": [
    {
      "categoryId": 23,
      "parentId": 13,
      "categoryName": "课程分类",
      "orderNum": 1,
      "status": "0"
    }
  ]
}
```

## 前端修正内容

### 1. 课程管理页面修正

#### 分类数据处理
```javascript
// 修正前
function getCategoryList() {
  listCategory().then(response => {
    categoryList.value = response.data;
  });
}

// 修正后
function getCategoryList() {
  listCategory().then(response => {
    // 提取课程分类（parentId为13的分类）
    const courseCategories = [];
    if (response.data && Array.isArray(response.data)) {
      response.data.forEach(category => {
        if (category.categoryId === 13 && category.children) {
          courseCategories.push(...category.children);
        }
      });
    }
    categoryList.value = courseCategories;
  });
}
```

#### 表格显示修正
```vue
<!-- 分类显示 -->
<el-table-column label="分类" align="center" prop="categoryName">
  <template #default="scope">
    <span v-if="scope.row.categories && scope.row.categories.length > 0">
      {{ scope.row.categories[0].categoryName }}
    </span>
    <span v-else>-</span>
  </template>
</el-table-column>

<!-- 讲师显示 -->
<el-table-column label="讲师" align="center" prop="instructorName">
  <template #default="scope">
    <span v-if="scope.row.instructor">{{ scope.row.instructor.name }}</span>
    <span v-else>-</span>
  </template>
</el-table-column>
```

#### 表单选项修正
```vue
<!-- 分类选择 -->
<el-form-item label="分类" prop="categoryId">
  <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%">
    <el-option v-for="category in categoryList" :key="category.categoryId" :label="category.categoryName"
      :value="category.categoryId" />
  </el-select>
</el-form-item>
```

#### 编辑时分类ID设置
```javascript
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getCourse(_id).then(response => {
    form.value = response.data;
    // 设置分类ID
    if (response.data.categories && response.data.categories.length > 0) {
      form.value.categoryId = response.data.categories[0].categoryId;
    }
    open.value = true;
    title.value = "修改课程";
  });
}
```

### 2. 章节管理页面修正

#### 课程列表处理
```javascript
function getCourseList() {
  listCourse().then(response => {
    courseList.value = response.rows || response.data || [];
  });
}
```

### 3. 数据字段对应关系

| 显示内容 | 后端字段路径 | 前端处理方式 |
|---------|-------------|-------------|
| 课程分类 | `categories[0].categoryName` | 取第一个分类的名称 |
| 讲师姓名 | `instructor.name` | 从嵌套对象中获取 |
| 讲师头衔 | `instructor.title` | 从嵌套对象中获取 |
| 分类ID | `categories[0].categoryId` | 编辑时设置到表单 |
| 课程标签 | `tags` | JSON字符串格式 |

## 需要注意的问题

### 1. 空数据处理
- 分类可能为空数组，需要判断 `categories.length > 0`
- 讲师可能为null，需要判断 `instructor` 存在性
- 使用 `v-if` 和 `v-else` 进行条件渲染

### 2. 数据结构嵌套
- 分类信息在 `categories` 数组中
- 讲师信息在 `instructor` 对象中
- 需要正确访问嵌套属性

### 3. 分类层级关系
- 课程分类的父级ID是13
- 实际的课程分类在children数组中
- 需要过滤出正确的分类数据

### 4. 标签数据格式
- 后端返回的tags是JSON字符串格式
- 如需编辑，前端需要进行JSON解析和序列化

## 后端接口建议

### 1. 课程列表接口优化
建议后端在返回课程列表时，直接在每个课程对象中包含：
- `categoryName`: 分类名称（平铺字段）
- `instructorName`: 讲师姓名（平铺字段）

### 2. 分类接口优化
建议提供专门的课程分类接口：
```
GET /api/course/categories
```
直接返回课程相关的分类列表，无需前端过滤。

### 3. 讲师简单列表接口
确保讲师列表接口返回格式：
```json
[
  {
    "id": 100,
    "name": "张老师",
    "title": "资深心理咨询师"
  }
]
```

## 测试验证

### 1. 数据显示测试
- ✅ 课程列表正确显示分类和讲师信息
- ✅ 处理空数据情况（显示"-"）
- ✅ 分类下拉选项正确加载

### 2. 表单操作测试
- ✅ 新增课程时分类选择正常
- ✅ 编辑课程时分类回显正确
- ✅ 保存时数据格式正确

### 3. 边界情况测试
- ✅ 无分类的课程显示处理
- ✅ 无讲师的课程显示处理
- ✅ 空的分类列表处理

## 总结

通过以上修正，前端页面已能正确处理后端返回的实际数据结构，包括：

1. **正确解析嵌套数据**：分类和讲师信息的正确显示
2. **空数据处理**：避免显示undefined或null
3. **分类过滤**：从复杂的分类树中提取课程分类
4. **表单回显**：编辑时正确设置分类ID

这些修正确保了前端页面能够稳定、正确地显示和操作课程数据。
