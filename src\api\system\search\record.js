import request from '@/utils/request'

// 查询搜索记录列表
export function listSearchRecord(query) {
  return request({
    url: '/system/search/record/list',
    method: 'get',
    params: query
  })
}

// 查询搜索记录详细
export function getSearchRecord(id) {
  return request({
    url: '/system/search/record/' + id,
    method: 'get'
  })
}

// 新增搜索记录
export function addSearchRecord(data) {
  return request({
    url: '/system/search/record',
    method: 'post',
    data: data
  })
}

// 修改搜索记录
export function updateSearchRecord(data) {
  return request({
    url: '/system/search/record',
    method: 'put',
    data: data
  })
}

// 删除搜索记录
export function delSearchRecord(ids) {
  return request({
    url: '/system/search/record/' + ids,
    method: 'delete'
  })
}

// 导出搜索记录
export function exportSearchRecord(query) {
  return request({
    url: '/system/search/record/export',
    method: 'post',
    params: query
  })
}
