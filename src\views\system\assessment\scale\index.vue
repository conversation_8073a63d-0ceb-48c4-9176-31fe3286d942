<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="量表名称" prop="scaleName">
        <el-input v-model="queryParams.scaleName" placeholder="请输入量表名称" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="量表编码" prop="scaleCode">
        <el-input v-model="queryParams.scaleCode" placeholder="请输入量表编码" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择分类" clearable style="width: 240px">
          <el-option v-for="category in categoryOptions" :key="category.categoryId" :label="category.categoryName"
            :value="category.categoryId" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option v-for="dict in scale_status_options" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:assessment:scale:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:assessment:scale:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:assessment:scale:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:assessment:scale:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scaleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="量表名称" align="center" prop="scaleName" :show-overflow-tooltip="true" />
      <el-table-column label="量表编码" align="center" prop="scaleCode" width="100" />
      <el-table-column label="分类" align="center" prop="categoryName" width="100" />
      <el-table-column label="题目数量" align="center" prop="questionCount" width="80" />
      <el-table-column label="时间限制" align="center" prop="timeLimit" width="80">
        <template #default="scope">
          <span>{{ scope.row.timeLimit }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="难度等级" align="center" prop="difficultyLevel" width="80">
        <template #default="scope">
          <dict-tag :options="difficulty_level_options" :value="scope.row.difficultyLevel" />
        </template>
      </el-table-column>
      <el-table-column label="价格" align="center" prop="price" width="80">
        <template #default="scope">
          <span v-if="scope.row.isFree === 1">免费</span>
          <span v-else class="text-red-500">¥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="测试次数" align="center" prop="testCount" width="80" />
      <el-table-column label="评分" align="center" prop="ratingAvg" width="80">
        <template #default="scope">
          <el-rate v-model="scope.row.ratingAvg" disabled show-score text-color="#ff9900" score-template="{value}" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <dict-tag :options="scale_status_options" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:assessment:scale:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:assessment:scale:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="scope.row.status === 0 && checkPermi(['system:assessment:scale:edit'])"
                  command="publish" icon="Check">发布</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status === 1 && checkPermi(['system:assessment:scale:edit'])"
                  command="offline" icon="Close">下架</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:question:list'])" command="questions"
                  icon="List">题目管理</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:scale:query'])" command="stats"
                  icon="DataAnalysis">统计分析</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:scale:remove'])" command="delete"
                  icon="Delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改量表对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="scaleRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="量表名称" prop="scaleName">
              <el-input v-model="form.scaleName" placeholder="请输入量表名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="量表编码" prop="scaleCode">
              <el-input v-model="form.scaleCode" placeholder="请输入量表编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%">
                <el-option v-for="category in categoryOptions" :key="category.categoryId" :label="category.categoryName"
                  :value="category.categoryId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="作者" prop="author">
              <el-input v-model="form.author" placeholder="请输入作者" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="时间限制" prop="timeLimit">
              <el-input-number v-model="form.timeLimit" :min="1" :max="120" placeholder="请输入时间限制(分钟)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="难度等级" prop="difficultyLevel">
              <el-select v-model="form.difficultyLevel" placeholder="请选择难度等级">
                <el-option v-for="dict in difficulty_level_options" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否免费" prop="isFree">
              <el-radio-group v-model="form.isFree">
                <el-radio :value="1">免费</el-radio>
                <el-radio :value="0">付费</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.isFree === 0">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="form.price" :min="0" :precision="2" :step="0.01" placeholder="请输入价格" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="量表描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入量表描述" />
        </el-form-item>
        <el-form-item label="测评说明" prop="instruction">
          <el-input v-model="form.instruction" type="textarea" placeholder="请输入测评说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 量表详情对话框 -->
    <el-dialog title="量表详情" v-model="detailOpen" width="1000px" append-to-body>
      <el-tabs v-model="activeDetailTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="量表名称">{{ detailForm.scaleName }}</el-descriptions-item>
            <el-descriptions-item label="量表编码">{{ detailForm.scaleCode }}</el-descriptions-item>
            <el-descriptions-item label="分类">{{ detailForm.categoryName }}</el-descriptions-item>
            <el-descriptions-item label="作者">{{ detailForm.author }}</el-descriptions-item>
            <el-descriptions-item label="题目数量">{{ detailForm.questionCount }}</el-descriptions-item>
            <el-descriptions-item label="时间限制">{{ detailForm.timeLimit }}分钟</el-descriptions-item>
            <el-descriptions-item label="难度等级">
              <dict-tag :options="difficulty_level_options" :value="detailForm.difficultyLevel" />
            </el-descriptions-item>
            <el-descriptions-item label="价格">
              <span v-if="detailForm.isFree === 1">免费</span>
              <span v-else class="text-red-500">¥{{ detailForm.price }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="测试次数">{{ detailForm.testCount }}</el-descriptions-item>
            <el-descriptions-item label="查看次数">{{ detailForm.viewCount }}</el-descriptions-item>
            <el-descriptions-item label="评分">
              <el-rate v-model="detailForm.ratingAvg" disabled show-score text-color="#ff9900" score-template="{value}" />
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <dict-tag :options="scale_status_options" :value="detailForm.status" />
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="量表描述" :span="2">{{ detailForm.description }}</el-descriptions-item>
            <el-descriptions-item label="测评说明" :span="2">{{ detailForm.instruction }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <!-- 题目信息 -->
        <el-tab-pane label="题目信息" name="questions">
          <div v-if="detailForm.questions && detailForm.questions.length > 0">
            <div v-for="question in detailForm.questions" :key="question.id" class="question-item">
              <div class="question-header">
                <span class="question-no">{{ question.questionNo }}.</span>
                <span class="question-text">{{ question.questionText }}</span>
                <el-tag size="small" :type="getQuestionTypeTag(question.questionType)">
                  {{ getQuestionTypeText(question.questionType) }}
                </el-tag>
              </div>

              <div v-if="question.options && question.options.length > 0" class="question-options">
                <div v-for="option in question.options" :key="option.id" class="option-item">
                  <span class="option-value">{{ option.optionValue }}.</span>
                  <span class="option-text">{{ option.optionText }}</span>
                  <span class="option-score">({{ option.score }}分)</span>
                </div>
              </div>

              <div v-if="question.dimension" class="question-dimension">
                <el-tag size="small" type="info">维度: {{ question.dimension }}</el-tag>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无题目信息" />
        </el-tab-pane>

        <!-- 结果解释 -->
        <el-tab-pane label="结果解释" name="interpretations" v-if="detailForm.interpretations && detailForm.interpretations.length > 0">
          <div class="interpretations-list">
            <div v-for="interpretation in detailForm.interpretations" :key="interpretation.id" class="interpretation-item">
              <div class="interpretation-header">
                <span class="level-name">{{ interpretation.levelName }}</span>
                <span class="score-range">{{ interpretation.minScore }} - {{ interpretation.maxScore }}分</span>
                <el-tag v-if="interpretation.dimension" size="small" type="warning">{{ interpretation.dimension }}</el-tag>
              </div>
              <div class="interpretation-description">{{ interpretation.levelDescription }}</div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog title="统计分析" v-model="statsOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="量表名称">{{ statsForm.scaleName }}</el-descriptions-item>
        <el-descriptions-item label="测试总次数">{{ statsForm.testCount }}</el-descriptions-item>
        <el-descriptions-item label="查看总次数">{{ statsForm.viewCount }}</el-descriptions-item>
        <el-descriptions-item label="平均评分">
          <el-rate v-model="statsForm.ratingAvg" disabled show-score text-color="#ff9900" score-template="{value}" />
        </el-descriptions-item>
        <el-descriptions-item label="评价数量">{{ statsForm.ratingCount }}</el-descriptions-item>
        <el-descriptions-item label="完成率">{{ statsForm.completionRate }}%</el-descriptions-item>
      </el-descriptions>
      <div class="mt-4">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="测试趋势" name="trend">
            <div ref="trendChartRef" style="width: 100%; height: 300px;"></div>
          </el-tab-pane>
          <el-tab-pane label="得分分布" name="score">
            <div ref="scoreChartRef" style="width: 100%; height: 300px;"></div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Scale">
import { listScale, getScale, delScale, addScale, updateScale, exportScale, publishScale, offlineScale, getScaleStats } from "@/api/system/assessment/scale";
import { listCategory } from "@/api/wechat/category";
import { checkPermi } from "@/utils/permission";
import * as echarts from 'echarts';

const { proxy } = getCurrentInstance();
const { scale_status_options, difficulty_level_options } = proxy.useDict('scale_status', 'difficulty_level');

const scaleList = ref([]);
const categoryOptions = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const statsOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref('trend');
const trendChart = ref(null);
const scoreChart = ref(null);
const activeDetailTab = ref("basic");
const trendChartRef = ref(null);
const scoreChartRef = ref(null);

const data = reactive({
  form: {},
  detailForm: {},
  statsForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    scaleName: null,
    scaleCode: null,
    categoryId: null,
    status: null
  },
  rules: {
    scaleName: [
      { required: true, message: "量表名称不能为空", trigger: "blur" }
    ],
    scaleCode: [
      { required: true, message: "量表编码不能为空", trigger: "blur" }
    ],
    categoryId: [
      { required: true, message: "分类不能为空", trigger: "change" }
    ],
    timeLimit: [
      { required: true, message: "时间限制不能为空", trigger: "blur" }
    ],
    difficultyLevel: [
      { required: true, message: "难度等级不能为空", trigger: "change" }
    ],
    isFree: [
      { required: true, message: "是否免费不能为空", trigger: "change" }
    ],
    price: [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (form.value.isFree === 0 && (value === null || value === undefined)) {
            callback(new Error('价格不能为空'));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  }
});

const { queryParams, form, detailForm, statsForm, rules } = toRefs(data);

/** 查询量表列表 */
function getList() {
  loading.value = true;
  listScale(queryParams.value).then(response => {
    scaleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询分类列表 */
function getCategoryList() {
  listCategory().then(response => {
    // 提取心理测评分类
    const assessmentCategories = [];
    if (response.data && Array.isArray(response.data)) {
      response.data.forEach(category => {
        if (category.categoryName === '心理测评' && category.children) {
          assessmentCategories.push(...category.children);
        }
      });
    }
    categoryOptions.value = assessmentCategories;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    scaleName: null,
    scaleCode: null,
    description: null,
    instruction: null,
    categoryId: null,
    author: null,
    questionCount: 0,
    timeLimit: 15,
    difficultyLevel: 1,
    price: 0.00,
    isFree: 1,
    status: "0"
  };
  proxy.resetForm("scaleRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加量表";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getScale(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改量表";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getScale(_id).then(response => {
    detailForm.value = response.data;
    activeDetailTab.value = "basic";
    detailOpen.value = true;
  });
}

/** 获取题目类型标签 */
function getQuestionTypeTag(type) {
  const typeMap = {
    1: 'primary',   // 单选
    2: 'success',   // 多选
    3: 'warning',   // 填空
    4: 'info'       // 其他
  };
  return typeMap[type] || 'info';
}

/** 获取题目类型文本 */
function getQuestionTypeText(type) {
  const typeMap = {
    1: '单选题',
    2: '多选题',
    3: '填空题',
    4: '其他'
  };
  return typeMap[type] || '未知';
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["scaleRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateScale(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addScale(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除量表编号为"' + _ids + '"的数据项？').then(function () {
    return delScale(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/assessment/scale/export', {
    ...queryParams.value
  }, `scale_${new Date().getTime()}.xlsx`)
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'publish':
      handlePublish(row);
      break;
    case 'offline':
      handleOffline(row);
      break;
    case 'questions':
      handleQuestions(row);
      break;
    case 'stats':
      handleStats(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
  }
}

/** 发布量表 */
function handlePublish(row) {
  proxy.$modal.confirm('是否确认发布量表"' + row.scaleName + '"？').then(function () {
    return publishScale(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发布成功");
  }).catch(() => { });
}

/** 下架量表 */
function handleOffline(row) {
  proxy.$modal.confirm('是否确认下架量表"' + row.scaleName + '"？').then(function () {
    return offlineScale(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("下架成功");
  }).catch(() => { });
}

/** 题目管理 */
function handleQuestions(row) {
  const routeUrl = proxy.$router.resolve({
    path: '/assessment-system/question',
    query: { scaleId: row.id }
  });
  window.open(routeUrl.href, '_blank');
}



/** 统计分析 */
function handleStats(row) {
  getScaleStats(row.id).then(response => {
    statsForm.value = response.data;
    statsForm.value.scaleName = row.scaleName;
    statsOpen.value = true;

    // 在下一个DOM更新周期后初始化图表
    nextTick(() => {
      initTrendChart();
      initScoreChart();
    });
  });
}

/** 初始化趋势图表 */
function initTrendChart() {
  if (trendChart.value) {
    trendChart.value.dispose();
  }

  trendChart.value = echarts.init(trendChartRef.value);

  // 模拟数据，实际应从后端获取
  const option = {
    title: {
      text: '测试趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['测试次数', '查看次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '测试次数',
        type: 'line',
        data: [10, 15, 20, 25, 22, 30, 35]
      },
      {
        name: '查看次数',
        type: 'line',
        data: [30, 40, 45, 50, 55, 60, 70]
      }
    ]
  };

  trendChart.value.setOption(option);
}

/** 初始化得分分布图表 */
function initScoreChart() {
  if (scoreChart.value) {
    scoreChart.value.dispose();
  }

  scoreChart.value = echarts.init(scoreChartRef.value);

  // 模拟数据，实际应从后端获取
  const option = {
    title: {
      text: '得分分布'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-20', '21-40', '41-60', '61-80', '81-100']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '人数',
        type: 'bar',
        data: [5, 15, 30, 25, 10]
      }
    ]
  };

  scoreChart.value.setOption(option);
}

// 监听标签页切换
watch(activeTab, (newVal) => {
  nextTick(() => {
    if (newVal === 'trend') {
      initTrendChart();
    } else if (newVal === 'score') {
      initScoreChart();
    }
  });
});

// 监听窗口大小变化，重绘图表
window.addEventListener('resize', () => {
  if (trendChart.value) {
    trendChart.value.resize();
  }
  if (scoreChart.value) {
    scoreChart.value.resize();
  }
});

onMounted(() => {
  getList();
  getCategoryList();
});

onUnmounted(() => {
  window.removeEventListener('resize', () => { });
  if (trendChart.value) {
    trendChart.value.dispose();
  }
  if (scoreChart.value) {
    scoreChart.value.dispose();
  }
});
</script>

<style scoped>
.el-tag+.el-tag {
  margin-left: 10px;
}

.question-item {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.question-no {
  font-weight: bold;
  color: #409eff;
  min-width: 30px;
}

.question-text {
  flex: 1;
  font-weight: 500;
  color: #303133;
}

.question-options {
  margin: 12px 0;
  padding-left: 20px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  padding: 4px 0;
}

.option-value {
  font-weight: bold;
  color: #606266;
  min-width: 20px;
}

.option-text {
  flex: 1;
  color: #606266;
}

.option-score {
  color: #909399;
  font-size: 12px;
}

.question-dimension {
  margin-top: 8px;
}

.interpretations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.interpretation-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.interpretation-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.level-name {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.score-range {
  color: #67c23a;
  font-weight: 500;
}

.interpretation-description {
  color: #606266;
  line-height: 1.6;
}
</style>
