<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="时间段名称" prop="name" label-width="100px">
        <el-input v-model="queryParams.name" placeholder="请输入时间段名称" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="状态" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="状态" clearable style="width: 240px">
          <el-option label="正常" value="0" />
          <el-option label="已删除" value="1" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:timeRange:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:timeRange:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:timeRange:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:timeRange:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Setting" @click="handleInit"
          v-hasPermi="['system:timeRange:init']">初始化默认时间段</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="View" @click="testDataEcho">测试数据回显</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="timeRangeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="时间段名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="图标" align="center" prop="iconUrl" width="80">
        <template #default="scope">
          <el-image v-if="scope.row.iconUrl" :src="scope.row.iconUrl" style="width: 30px; height: 30px;" fit="cover" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startHour">
        <template #default="scope">
          {{ scope.row.startHour }}:00
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endHour">
        <template #default="scope">
          {{ scope.row.endHour }}:00
        </template>
      </el-table-column>
      <el-table-column label="时长" align="center">
        <template #default="scope">
          {{ scope.row.endHour - scope.row.startHour }}小时
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="delFlag">
        <template #default="scope">
          <el-tag :type="scope.row.delFlag === 0 ? 'success' : 'danger'">
            {{ scope.row.delFlag === 0 ? '正常' : '已删除' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:timeRange:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:timeRange:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改时间段对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="timeRangeRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="时间段名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入时间段名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图标URL" prop="iconUrl">
              <el-input v-model="form.iconUrl" placeholder="请输入图标URL" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startHour">
              <el-input-number v-model="form.startHour" :min="0" :max="23" placeholder="请输入开始小时" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endHour">
              <el-input-number v-model="form.endHour" :min="0" :max="23" placeholder="请输入结束小时" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TimeRange">
import { listTimeRange, getTimeRange, delTimeRange, addTimeRange, updateTimeRange, initDefaultTimeRanges } from "@/api/wechat/timeRange";
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue'

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 时间段表格数据
const timeRangeList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  delFlag: undefined
});

// 表单参数
const form = reactive({
  id: undefined,
  name: undefined,
  iconUrl: undefined,
  startHour: undefined,
  endHour: undefined,
  delFlag: 0,
  remark: undefined
});

// 表单校验
const rules = reactive({
  name: [
    { required: true, message: "时间段名称不能为空", trigger: "blur" }
  ],
  startHour: [
    { required: true, message: "开始时间不能为空", trigger: "blur" }
  ],
  endHour: [
    { required: true, message: "结束时间不能为空", trigger: "blur" }
  ]
});

/** 查询时间段列表 */
function getList() {
  loading.value = true;
  listTimeRange(queryParams).then(response => {
    timeRangeList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.id = undefined;
  form.name = undefined;
  form.iconUrl = undefined;
  form.startHour = undefined;
  form.endHour = undefined;
  form.delFlag = 0;
  form.remark = undefined;

  // 延迟重置表单验证，避免干扰数据回显
  nextTick(() => {
    if (proxy.$refs["timeRangeRef"]) {
      proxy.$refs["timeRangeRef"].clearValidate();
    }
  });

  console.log('表单已重置:', form);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加时间段";
}

/** 测试数据回显 */
async function testDataEcho() {
  // 模拟您提供的数据进行测试
  const testData = {
    "id": 20,
    "name": "上午",
    "iconUrl": "https://example.com/icons/morning.png",
    "startHour": 9,
    "endHour": 12,
    "delFlag": 0,
    "remark": "测试备注"
  };

  console.log('开始测试数据回显');
  console.log('测试数据:', testData);

  // 先重置表单
  reset();

  // 等待重置完成
  await nextTick();

  // 设置表单数据
  form.id = testData.id;
  form.name = testData.name;
  form.iconUrl = testData.iconUrl;
  form.startHour = Number(testData.startHour);
  form.endHour = Number(testData.endHour);
  form.delFlag = Number(testData.delFlag);
  form.remark = testData.remark;

  console.log('设置后的表单数据:', {
    id: form.id,
    name: form.name,
    iconUrl: form.iconUrl,
    startHour: form.startHour,
    endHour: form.endHour,
    delFlag: form.delFlag,
    remark: form.remark
  });

  // 打开对话框
  open.value = true;
  title.value = "测试数据回显";

  // 等待DOM更新后再次检查
  await nextTick();
  console.log('DOM更新后的表单数据:', form);

  ElMessage.success('测试数据已设置，请检查表单是否正确显示');
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const timeRangeId = row.id || ids.value[0];

  console.log('准备编辑时间段，ID:', timeRangeId);
  console.log('行数据:', row);

  getTimeRange(timeRangeId).then(response => {
    console.log('API响应原始数据:', response);

    // 根据实际返回数据结构调整
    const data = response.data || response;
    console.log('解析后的数据:', data);

    // 确保数据类型正确
    form.id = data.id;
    form.name = data.name || '';
    form.iconUrl = data.iconUrl || '';
    form.startHour = Number(data.startHour);
    form.endHour = Number(data.endHour);
    form.delFlag = Number(data.delFlag) || 0;
    form.remark = data.remark || '';

    console.log('设置后的表单数据:', {
      id: form.id,
      name: form.name,
      iconUrl: form.iconUrl,
      startHour: form.startHour,
      endHour: form.endHour,
      delFlag: form.delFlag,
      remark: form.remark
    });

    open.value = true;
    title.value = "修改时间段";

    // 延迟检查表单是否正确显示
    setTimeout(() => {
      console.log('延迟检查表单数据:', form);
    }, 100);

  }).catch(error => {
    console.error('获取时间段详情失败:', error);
    ElMessage.error('获取时间段详情失败');
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["timeRangeRef"].validate(valid => {
    if (valid) {
      if (form.id != undefined) {
        updateTimeRange(form).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTimeRange(form).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const timeRangeIds = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除所选时间段数据?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return delTimeRange(timeRangeIds);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => { });
}

/** 时间段状态修改 */
// function handleStatusChange(row) {
//   let text = row.status === "0" ? "启用" : "停用";
//   ElMessageBox.confirm('确认要"' + text + '""' + row.name + '"吗?', "警告", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning"
//   }).then(() => {
//     return updateTimeRange(row);
//   }).then(() => {
//     ElMessage.success(text + "成功");
//   }).catch(() => {
//     row.status = row.status === "0" ? "1" : "0";
//   });
// }

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/timeRange/export", {
    ...queryParams
  }, "timeRange_" + new Date().getTime() + ".xlsx");
}

/** 初始化默认时间段 */
function handleInit() {
  ElMessageBox.confirm('确认要初始化默认时间段吗?', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  }).then(() => {
    return initDefaultTimeRanges();
  }).then((response) => {
    ElMessage.success(response.msg);
    getList();
  }).catch(() => { });
}

onMounted(() => {
  getList();
});
</script>
