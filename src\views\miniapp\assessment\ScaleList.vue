<template>
  <div class="scale-list">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索量表名称或关键词"
        clearable
        @keyup.enter="handleSearch"
        style="width: 300px"
      >
        <template #append>
          <el-button icon="Search" @click="handleSearch" />
        </template>
      </el-input>
      
      <el-select v-model="queryParams.scoringType" placeholder="计分类型" clearable style="width: 150px; margin-left: 10px">
        <el-option label="求和" value="sum" />
        <el-option label="平均" value="average" />
        <el-option label="加权" value="weighted" />
      </el-select>
      
      <el-select v-model="queryParams.payMode" placeholder="付费模式" clearable style="width: 150px; margin-left: 10px">
        <el-option label="免费" :value="0" />
        <el-option label="付费" :value="1" />
        <el-option label="部分付费" :value="2" />
      </el-select>
    </div>

    <!-- 分类标签 -->
    <div class="category-tabs">
      <el-button 
        v-for="tab in categoryTabs" 
        :key="tab.key"
        :type="activeTab === tab.key ? 'primary' : ''"
        @click="switchTab(tab.key)"
      >
        {{ tab.label }}
      </el-button>
    </div>

    <!-- 量表列表 -->
    <div class="scale-grid" v-loading="loading">
      <div 
        v-for="scale in scaleList" 
        :key="scale.id"
        class="scale-card"
        @click="viewScaleDetail(scale)"
      >
        <div class="scale-cover">
          <img :src="scale.coverImage || '/images/default-scale.png'" :alt="scale.name" />
          <div class="scale-badges">
            <el-tag v-if="scale.payMode === 0" type="success" size="small">免费</el-tag>
            <el-tag v-else-if="scale.payMode === 1" type="warning" size="small">付费</el-tag>
            <el-tag v-else type="info" size="small">部分付费</el-tag>
          </div>
        </div>
        
        <div class="scale-info">
          <h3 class="scale-name">{{ scale.name }}</h3>
          <p class="scale-alias" v-if="scale.alias">{{ scale.alias }}</p>
          <p class="scale-description">{{ scale.description }}</p>
          
          <div class="scale-meta">
            <span class="question-count">{{ scale.questionCount }}题</span>
            <span class="time-limit" v-if="scale.timeLimit">{{ formatTime(scale.timeLimit) }}</span>
          </div>
          
          <div class="scale-stats">
            <div class="stat-item">
              <el-icon><View /></el-icon>
              <span>{{ scale.viewCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <el-icon><EditPen /></el-icon>
              <span>{{ scale.testCount || 0 }}</span>
            </div>
            <div class="stat-item" v-if="scale.ratingAvg">
              <el-rate 
                v-model="scale.ratingAvg" 
                disabled 
                show-score 
                text-color="#ff9900"
                score-template="{value}"
                size="small"
              />
            </div>
          </div>
          
          <div class="scale-actions">
            <el-button type="primary" size="small" @click.stop="startAssessment(scale)">
              开始测评
            </el-button>
            <el-button size="small" @click.stop="toggleFavorite(scale)">
              <el-icon><Star /></el-icon>
              {{ scale.isFavorite ? '取消收藏' : '收藏' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { View, EditPen, Star } from '@element-plus/icons-vue'
import {
  listScales,
  searchScales,
  getHotScales,
  getFreeScales,
  getRecommendScales,
  getLatestScales
} from '@/api/miniapp/assessment'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const scaleList = ref([])
const total = ref(0)
const searchKeyword = ref('')
const activeTab = ref('all')

const queryParams = reactive({
  pageNum: 1,
  pageSize: 12,
  scoringType: '',
  payMode: ''
})

const categoryTabs = [
  { key: 'all', label: '全部' },
  { key: 'hot', label: '热门' },
  { key: 'free', label: '免费' },
  { key: 'recommend', label: '推荐' },
  { key: 'latest', label: '最新' }
]

// 方法
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

const getList = async () => {
  try {
    loading.value = true
    let response
    
    switch (activeTab.value) {
      case 'hot':
        response = await getHotScales(queryParams.pageSize)
        scaleList.value = response.data
        total.value = response.data.length
        break
      case 'free':
        response = await getFreeScales()
        scaleList.value = response.data
        total.value = response.data.length
        break
      case 'recommend':
        response = await getRecommendScales(queryParams.pageSize)
        scaleList.value = response.data
        total.value = response.data.length
        break
      case 'latest':
        response = await getLatestScales(queryParams.pageSize)
        scaleList.value = response.data
        total.value = response.data.length
        break
      default:
        if (searchKeyword.value) {
          response = await searchScales({
            keyword: searchKeyword.value,
            ...queryParams
          })
        } else {
          response = await listScales(queryParams)
        }
        scaleList.value = response.rows
        total.value = response.total
    }
  } catch (error) {
    ElMessage.error('加载量表列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.pageNum = 1
  activeTab.value = 'all'
  getList()
}

const switchTab = (tab) => {
  activeTab.value = tab
  queryParams.pageNum = 1
  searchKeyword.value = ''
  getList()
}

const viewScaleDetail = (scale) => {
  router.push(`/miniapp-assessment/scale/${scale.id}`)
}

const startAssessment = (scale) => {
  router.push(`/miniapp-assessment/flow/${scale.id}`)
}

const toggleFavorite = async (scale) => {
  try {
    // 这里需要实现收藏/取消收藏的API
    scale.isFavorite = !scale.isFavorite
    ElMessage.success(scale.isFavorite ? '收藏成功' : '取消收藏成功')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.scale-list {
  padding: 20px;
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.category-tabs {
  margin-bottom: 20px;
}

.category-tabs .el-button {
  margin-right: 10px;
}

.scale-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.scale-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.scale-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.scale-cover {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.scale-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scale-badges {
  position: absolute;
  top: 8px;
  right: 8px;
}

.scale-info {
  padding: 16px;
}

.scale-name {
  font-size: 16px;
  font-weight: bold;
  margin: 0 0 4px 0;
  color: #303133;
}

.scale-alias {
  font-size: 12px;
  color: #909399;
  margin: 0 0 8px 0;
}

.scale-description {
  font-size: 14px;
  color: #606266;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.scale-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 12px;
  color: #909399;
}

.scale-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.scale-actions {
  display: flex;
  gap: 8px;
}

.scale-actions .el-button {
  flex: 1;
}
</style>
