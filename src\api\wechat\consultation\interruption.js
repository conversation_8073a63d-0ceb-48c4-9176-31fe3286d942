import request from '@/utils/request'

// 查询咨询中断记录列表
export function listConsultantInterruption(query) {
  return request({
    url: '/system/consultantInterruption/list',
    method: 'get',
    params: query
  })
}

// 导出咨询中断记录列表
export function exportConsultantInterruption(query) {
  return request({
    url: '/system/consultantInterruption/export',
    method: 'post',
    params: query
  })
}

// 获取咨询中断记录详细信息
export function getConsultantInterruption(id) {
  return request({
    url: `/system/consultantInterruption/${id}`,
    method: 'get'
  })
}

// 新增咨询中断记录
export function addConsultantInterruption(data) {
  return request({
    url: '/system/consultantInterruption',
    method: 'post',
    data: data
  })
}

// 修改咨询中断记录
export function updateConsultantInterruption(data) {
  return request({
    url: '/system/consultantInterruption',
    method: 'put',
    data: data
  })
}

// 删除咨询中断记录
export function delConsultantInterruption(ids) {
  return request({
    url: `/system/consultantInterruption/${ids}`,
    method: 'delete'
  })
}

// 根据咨询记录ID查询中断记录列表
export function getConsultantInterruptionsByRecord(recordId) {
  return request({
    url: `/system/consultantInterruption/record/${recordId}`,
    method: 'get'
  })
}

// 统计咨询记录的中断次数
export function countConsultantInterruptions(recordId) {
  return request({
    url: `/system/consultantInterruption/count/${recordId}`,
    method: 'get'
  })
}

// 统计咨询记录的总中断时长
export function sumConsultantInterruptionDuration(recordId) {
  return request({
    url: `/system/consultantInterruption/duration/${recordId}`,
    method: 'get'
  })
}

// 根据中断类型统计
export function countConsultantInterruptionsByType(recordId, interruptType) {
  return request({
    url: `/system/consultantInterruption/countByType/${recordId}`,
    method: 'get',
    params: {
      interruptType: interruptType
    }
  })
}

// 获取咨询记录的中断统计
export function getConsultantInterruptionStats(recordId) {
  return request({
    url: `/system/consultantInterruption/statistics/${recordId}`,
    method: 'get'
  })
}
