<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="课程名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入课程名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择分类" clearable style="width: 240px">
          <el-option v-for="category in categoryList" :key="category.id" :label="category.categoryName"
            :value="category.categoryId" />
        </el-select>
      </el-form-item>
      <el-form-item label="讲师" prop="instructorId">
        <el-select v-model="queryParams.instructorId" placeholder="请选择讲师" clearable style="width: 240px">
          <el-option v-for="instructor in instructorList" :key="instructor.id" :label="instructor.name"
            :value="instructor.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option label="草稿" value="0" />
          <el-option label="已发布" value="1" />
          <el-option label="已下架" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:course:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:course:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:course:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:course:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="courseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="课程ID" align="center" prop="id" width="80" />
      <el-table-column label="封面" align="center" prop="coverImage" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.coverImage" :width="50" :height="50" v-if="scope.row.coverImage" />
        </template>
      </el-table-column>
      <el-table-column label="课程名称" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="分类" align="center" prop="categoryName">
        <template #default="scope">
          <span v-if="scope.row.categories && scope.row.categories.length > 0">
            {{ scope.row.categories[0].categoryName }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="讲师" align="center" prop="instructorName">
        <template #default="scope">
          <span v-if="scope.row.instructor">{{ scope.row.instructor.name }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="价格" align="center" prop="price" width="100">
        <template #default="scope">
          <span class="text-red-500">¥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销量" align="center" prop="salesCount" width="80" />
      <el-table-column label="评分" align="center" prop="ratingAvg" width="80">
        <template #default="scope">
          <el-rate v-model="scope.row.ratingAvg" disabled show-score text-color="#ff9900" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_course_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:course:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:course:edit']">修改</el-button>
          <el-button link type="primary" icon="List" @click="handleChapterManage(scope.row)"
            v-hasPermi="['system:chapter:list']">章节</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:course:remove']">删除</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="publish" v-if="scope.row.status == 0" icon="VideoPlay">发布</el-dropdown-item>
                <el-dropdown-item command="unpublish" v-if="scope.row.status == 1" icon="VideoPause">下架
                </el-dropdown-item>
                <el-dropdown-item command="updateStats" icon="Refresh">更新统计</el-dropdown-item>
                <el-dropdown-item command="updateRating" icon="Star">更新评分</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改课程对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="courseRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="课程名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入课程名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程简介" prop="summary">
              <el-input v-model="form.summary" placeholder="请输入课程简介" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%">
                <el-option v-for="category in categoryList" :key="category.categoryId" :label="category.categoryName"
                  :value="category.categoryId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="讲师" prop="instructorId">
              <el-select v-model="form.instructorId" placeholder="请选择讲师" style="width: 100%">
                <el-option v-for="instructor in instructorList" :key="instructor.id" :label="instructor.name"
                  :value="instructor.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="form.price" :precision="2" :min="0" :max="99999" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原价" prop="originalPrice">
              <el-input-number v-model="form.originalPrice" :precision="2" :min="0" :max="99999" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="课程封面" prop="coverImage">
          <image-upload v-model="form.coverImage" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="课程时长" prop="durationTotal">
              <el-input-number v-model="form.durationTotal" :min="0" placeholder="秒" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="难度等级" prop="difficultyLevel">
              <el-select v-model="form.difficultyLevel" placeholder="请选择难度等级" style="width: 100%">
                <el-option v-for="item in sys_difficulty_level" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否免费" prop="isFree">
              <el-radio-group v-model="form.isFree">
                <el-radio :value="0">付费</el-radio>
                <el-radio :value="1">免费</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :value="0">未发布</el-radio>
                <el-radio :value="1">已发布</el-radio>
                <el-radio :value="2">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="标签" prop="tags">
          <el-input v-model="form.tags" placeholder="请输入标签，多个标签用逗号分隔" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 课程详情对话框 -->
    <el-dialog title="课程详情" v-model="detailOpen" width="1000px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="课程名称">{{ detailForm.title }}</el-descriptions-item>
        <el-descriptions-item label="副标题">{{ detailForm.subtitle }}</el-descriptions-item>
        <el-descriptions-item label="分类">
          <span v-if="detailForm.categories && detailForm.categories.length > 0">
            {{ detailForm.categories[0].categoryName }}
          </span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="讲师">
          <span v-if="detailForm.instructor">{{ detailForm.instructor.name }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="价格">¥{{ detailForm.price }}</el-descriptions-item>
        <el-descriptions-item label="原价">¥{{ detailForm.originalPrice }}</el-descriptions-item>
        <el-descriptions-item label="学员数">{{ detailForm.studentCount }}</el-descriptions-item>
        <el-descriptions-item label="评分">{{ detailForm.rating }}</el-descriptions-item>
        <el-descriptions-item label="课程时长">{{ detailForm.duration }}分钟</el-descriptions-item>
        <el-descriptions-item label="难度等级">
          <dict-tag :options="sys_difficulty_level" :value="detailForm.difficultyLevel" />
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="sys_course_status" :value="detailForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="课程封面" :span="2">
          <image-preview :src="detailForm.coverImage" :width="200" :height="150" v-if="detailForm.coverImage" />
        </el-descriptions-item>
        <el-descriptions-item label="课程描述" :span="2">
          <div v-html="detailForm.description"></div>
        </el-descriptions-item>
        <el-descriptions-item label="课程详情" :span="2">
          <div v-html="detailForm.content"></div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 章节管理对话框 -->
    <el-dialog :title="chapterDialogTitle" v-model="chapterOpen" width="1200px" append-to-body>
      <div class="chapter-manage-container">
        <!-- 章节操作按钮 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAddChapter"
              v-hasPermi="['system:chapter:add']">新增章节</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="chapterSingle" @click="handleUpdateChapter"
              v-hasPermi="['system:chapter:edit']">修改章节</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="chapterMultiple" @click="handleDeleteChapter"
              v-hasPermi="['system:chapter:remove']">删除章节</el-button>
          </el-col>
        </el-row>

        <!-- 章节列表 -->
        <el-table v-loading="chapterLoading" :data="chapterList" @selection-change="handleChapterSelectionChange"
          row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="章节名称" align="left" prop="chapterTitle" :show-overflow-tooltip="true" />
          <el-table-column label="排序" align="center" prop="chapterOrder" width="100" />
          <!-- <el-table-column label="内容类型" align="center" prop="contentType" width="100">
            <template #default="scope">
              <dict-tag :options="sys_content_type" :value="scope.row.contentType" />
            </template>
          </el-table-column> -->
          <el-table-column label="时长(秒)" align="center" prop="duration" width="100" />
          <el-table-column label="试听" align="center" prop="isTrial" width="80">
            <template #default="scope">
              <dict-tag :options="whether_the_number" :value="scope.row.isTrial" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleUpdateChapter(scope.row)"
                v-hasPermi="['system:chapter:edit']">修改</el-button>
              <el-button link type="primary" icon="Plus" @click="handleAddSubChapter(scope.row)"
                v-hasPermi="['system:chapter:add']">添加子章节</el-button>
              <el-button link type="primary" icon="Delete" @click="handleDeleteChapter(scope.row)"
                v-hasPermi="['system:chapter:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 章节编辑对话框 -->
    <el-dialog :title="chapterFormTitle" v-model="chapterFormOpen" width="600px" append-to-body>
      <el-form ref="chapterRef" :model="chapterForm" :rules="chapterRules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="章节名称" prop="chapterTitle">
              <el-input v-model="chapterForm.chapterTitle" placeholder="请输入章节名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="chapterOrder">
              <el-input-number v-model="chapterForm.chapterOrder" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <!-- <el-form-item label="内容类型" prop="contentType">
              <el-select v-model="chapterForm.contentType" placeholder="请选择内容类型" style="width: 100%">
                <el-option label="视频" value="0" />
                <el-option label="音频" value="1" />
                <el-option label="文档" value="2" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="时长(秒)" prop="duration">
              <el-input-number v-model="chapterForm.duration" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否试听" prop="isTrial">
              <el-radio-group v-model="chapterForm.isTrial">
                <el-radio :value="0">否</el-radio>
                <el-radio :value="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="视频地址" prop="mediaUrl">
              <el-input v-model="chapterForm.mediaUrl" placeholder="请输入视频地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="章节内容" prop="chapterContent">
              <editor v-model="chapterForm.chapterContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="chapterForm.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitChapterForm">确 定</el-button>
          <el-button @click="cancelChapter">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Course">
import {
  listCourse, getCourse, delCourse, addCourse, updateCourse, exportCourse,
  publishCourse, unpublishCourse, updateCourseStatistics, updateCourseRating,
  getCourseDetails
} from "@/api/wechat/course/course";
import { listCategory } from "@/api/wechat/category";
import { getSimpleInstructorList } from "@/api/wechat/course/instructor";
import { listChapter, getChapter, delChapter, addChapter, updateChapter } from "@/api/wechat/course/chapter";

const { proxy } = getCurrentInstance();
const { sys_course_status, sys_difficulty_level, sys_content_type, whether_the_number } = proxy.useDict('sys_course_status', 'sys_difficulty_level', 'sys_content_type', 'whether_the_number');

const courseList = ref([]);
const categoryList = ref([]);
const instructorList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 章节管理相关变量
const chapterList = ref([]);
const chapterOpen = ref(false);
const chapterFormOpen = ref(false);
const chapterLoading = ref(false);
const chapterIds = ref([]);
const chapterSingle = ref(true);
const chapterMultiple = ref(true);
const chapterDialogTitle = ref("");
const chapterFormTitle = ref("");
const currentCourseId = ref(null);

const data = reactive({
  form: {},
  detailForm: {},
  chapterForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    categoryId: null,
    instructorId: null,
    status: null
  },
  rules: {
    title: [
      { required: true, message: "课程名称不能为空", trigger: "blur" }
    ],
    categoryId: [
      { required: true, message: "分类不能为空", trigger: "change" }
    ],
    instructorId: [
      { required: true, message: "讲师不能为空", trigger: "change" }
    ],
    price: [
      { required: true, message: "价格不能为空", trigger: "blur" }
    ]
  },
  chapterRules: {
    chapterTitle: [
      { required: true, message: "章节名称不能为空", trigger: "blur" }
    ],
    chapterOrder: [
      { required: true, message: "显示排序不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, chapterForm, rules, chapterRules } = toRefs(data);
console.log(sys_difficulty_level)
/** 查询课程列表 */
function getList() {
  loading.value = true;
  listCourse(queryParams.value).then(response => {
    courseList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询分类列表 */
function getCategoryList() {
  listCategory().then(response => {
    // 提取课程分类（parentId为13的分类）
    const courseCategories = [];
    if (response.data && Array.isArray(response.data)) {
      response.data.forEach(category => {
        if (category.categoryId === 13 && category.children) {
          courseCategories.push(...category.children);
        }
      });
    }
    categoryList.value = courseCategories;

  });
}

/** 查询讲师列表 */
function getInstructorList() {
  getSimpleInstructorList().then(response => {
    instructorList.value = response.data || response.rows || [];
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    title: null,
    summary: null,
    price: null,
    salesCount: 0,
    chapterCount: 0,
    trialChapterCount: 0,
    instructorId: null,
    coverImage: null,
    categoryId: null,
    difficultyLevel: null,
    durationTotal: 0,
    viewCount: 0,
    ratingAvg: 0.00,
    ratingCount: 0,
    isFree: 0,
    tags: null,
    status: "0",
    remark: null
  };
  proxy.resetForm("courseRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加课程";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getCourseDetails(_id).then(response => {
    form.value = response.data;
    form.value.difficultyLevel = '' + response.data.difficultyLevel;
    // 设置分类ID - 优先从categories数组获取，其次从categoryIds数组获取
    if (response.data.categories && response.data.categories.length > 0) {
      form.value.categoryId = response.data.categories[0].categoryId;
    } else if (response.data.categoryIds && response.data.categoryIds.length > 0) {
      form.value.categoryId = response.data.categoryIds[0];
    }
    open.value = true;
    title.value = "修改课程";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getCourseDetails(_id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["courseRef"].validate(valid => {
    if (valid) {
      // 处理分类ID，将单个categoryId转换为categoryIds数组
      const submitData = { ...form.value };
      if (submitData.categoryId) {
        submitData.categoryIds = [submitData.categoryId];
      }

      if (form.value.id != null) {
        updateCourse(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCourse(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除课程编号为"' + _ids + '"的数据项？').then(function () {
    return delCourse(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/course/export', {
    ...queryParams.value
  }, `course_${new Date().getTime()}.xlsx`)
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'publish':
      handlePublish(row);
      break;
    case 'unpublish':
      handleUnpublish(row);
      break;
    case 'updateStats':
      handleUpdateStats(row);
      break;
    case 'updateRating':
      handleUpdateRating(row);
      break;
  }
}

/** 发布课程 */
function handlePublish(row) {
  proxy.$modal.confirm('是否确认发布课程"' + row.title + '"？').then(function () {
    return publishCourse(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发布成功");
  }).catch(() => { });
}

/** 下架课程 */
function handleUnpublish(row) {
  proxy.$modal.confirm('是否确认下架课程"' + row.title + '"？').then(function () {
    return unpublishCourse(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("下架成功");
  }).catch(() => { });
}

/** 更新统计 */
function handleUpdateStats(row) {
  updateCourseStatistics(row.id).then(response => {
    getList();
    proxy.$modal.msgSuccess("统计更新成功");
  });
}

/** 更新评分 */
function handleUpdateRating(row) {
  updateCourseRating(row.id).then(response => {
    getList();
    proxy.$modal.msgSuccess("评分更新成功");
  });
}

/** 章节管理相关函数 */
function handleChapterManage(row) {
  currentCourseId.value = row.id;
  chapterDialogTitle.value = `${row.title} - 章节管理`;
  chapterOpen.value = true;
  getChapterList();
}

function getChapterList() {
  chapterLoading.value = true;
  listChapter({ courseId: currentCourseId.value }).then(response => {
    chapterList.value = proxy.handleTree(response.rows, "id", "parentId");
    chapterLoading.value = false;
  });
}

function handleChapterSelectionChange(selection) {
  chapterIds.value = selection.map(item => item.id);
  chapterSingle.value = selection.length != 1;
  chapterMultiple.value = !selection.length;
}

function handleAddChapter() {
  resetChapterForm();
  chapterForm.value.courseId = currentCourseId.value;
  chapterForm.value.parentId = 0;
  chapterFormOpen.value = true;
  chapterFormTitle.value = "添加章节";
}

function handleAddSubChapter(row) {
  resetChapterForm();
  chapterForm.value.courseId = currentCourseId.value;
  chapterForm.value.parentId = row.id;
  chapterForm.value.level = (row.level || 1) + 1;
  chapterFormOpen.value = true;
  chapterFormTitle.value = "添加子章节";
}

function handleUpdateChapter(row) {
  resetChapterForm();
  const _id = row?.id || chapterIds.value[0];
  getChapter(_id).then(response => {
    chapterForm.value = response.data;
    chapterFormOpen.value = true;
    chapterFormTitle.value = "修改章节";
  });
}

function handleDeleteChapter(row) {
  const _ids = row?.id || chapterIds.value;
  proxy.$modal.confirm('是否确认删除章节编号为"' + _ids + '"的数据项？').then(function () {
    return delChapter(_ids);
  }).then(() => {
    getChapterList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

function submitChapterForm() {
  proxy.$refs["chapterRef"].validate(valid => {
    if (valid) {
      if (chapterForm.value.id != null) {
        updateChapter(chapterForm.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          chapterFormOpen.value = false;
          getChapterList();
        });
      } else {
        addChapter(chapterForm.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          chapterFormOpen.value = false;
          getChapterList();
        });
      }
    }
  });
}

function cancelChapter() {
  chapterFormOpen.value = false;
  resetChapterForm();
}

function resetChapterForm() {
  chapterForm.value = {
    id: null,
    courseId: null,
    parentId: 0,
    chapterTitle: null,
    chapterContent: null,
    contentType: "0",
    duration: null,
    chapterOrder: 0,
    level: 1,
    isTrial: 0,
    mediaUrl: null,
    remark: null
  };
  proxy.resetForm("chapterRef");
}

onMounted(() => {
  getList();
  getCategoryList();
  getInstructorList();
});
</script>
