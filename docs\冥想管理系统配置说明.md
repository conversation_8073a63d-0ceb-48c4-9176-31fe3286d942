# 冥想管理系统配置说明

## 概述
已为PC端后台管理系统创建了完整的冥想管理系统功能，包括以下模块：

1. **冥想管理** - 冥想内容的增删改查、发布/下架、评分更新、统计信息
2. **冥想订单管理** - 冥想订单管理、支付状态更新、退款处理
3. **冥想评价管理** - 冥想评价管理、按冥想和用户查询、统计功能

## 已创建的文件

### 前端API文件
```
src/api/wechat/meditation/meditation.js    # 冥想API
src/api/wechat/meditation/order.js         # 冥想订单API
src/api/wechat/meditation/review.js        # 冥想评价API
```

### 前端页面文件
```
src/views/wechat/meditation/meditation/index.vue    # 冥想管理
src/views/wechat/meditation/order/index.vue         # 冥想订单管理
src/views/wechat/meditation/review/index.vue        # 冥想评价管理
```

## 需要在后端添加的菜单配置

由于项目使用动态路由，需要在后端数据库的菜单表中添加以下菜单配置：

### 1. 冥想管理系统主菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想管理系统', 0, 8, 'meditation-system', NULL, 'M', '0', '0', NULL, 'guide', 'admin', NOW(), '', NULL, '冥想管理系统');
```

### 2. 冥想管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想管理', [冥想管理系统菜单ID], 1, 'meditation', 'wechat/meditation/meditation/index', 'C', '0', '0', 'system:meditation:list', 'guide', 'admin', NOW(), '', NULL, '冥想内容管理');

-- 冥想管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想查询', [冥想管理菜单ID], 1, '', '', 'F', '0', '0', 'system:meditation:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想新增', [冥想管理菜单ID], 2, '', '', 'F', '0', '0', 'system:meditation:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想修改', [冥想管理菜单ID], 3, '', '', 'F', '0', '0', 'system:meditation:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想删除', [冥想管理菜单ID], 4, '', '', 'F', '0', '0', 'system:meditation:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想导出', [冥想管理菜单ID], 5, '', '', 'F', '0', '0', 'system:meditation:export', '#', 'admin', NOW(), '', NULL, '');
```

### 3. 冥想订单管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想订单管理', [冥想管理系统菜单ID], 2, 'meditation-order', 'wechat/meditation/order/index', 'C', '0', '0', 'system:meditationOrder:list', 'shopping', 'admin', NOW(), '', NULL, '冥想订单管理');

-- 冥想订单管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单查询', [冥想订单管理菜单ID], 1, '', '', 'F', '0', '0', 'system:meditationOrder:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单新增', [冥想订单管理菜单ID], 2, '', '', 'F', '0', '0', 'system:meditationOrder:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单修改', [冥想订单管理菜单ID], 3, '', '', 'F', '0', '0', 'system:meditationOrder:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单删除', [冥想订单管理菜单ID], 4, '', '', 'F', '0', '0', 'system:meditationOrder:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单导出', [冥想订单管理菜单ID], 5, '', '', 'F', '0', '0', 'system:meditationOrder:export', '#', 'admin', NOW(), '', NULL, '');
```

### 4. 冥想评价管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('冥想评价管理', [冥想管理系统菜单ID], 3, 'meditation-review', 'wechat/meditation/review/index', 'C', '0', '0', 'system:meditationReview:list', 'star', 'admin', NOW(), '', NULL, '冥想评价管理');

-- 冥想评价管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价查询', [冥想评价管理菜单ID], 1, '', '', 'F', '0', '0', 'system:meditationReview:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价新增', [冥想评价管理菜单ID], 2, '', '', 'F', '0', '0', 'system:meditationReview:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价修改', [冥想评价管理菜单ID], 3, '', '', 'F', '0', '0', 'system:meditationReview:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价删除', [冥想评价管理菜单ID], 4, '', '', 'F', '0', '0', 'system:meditationReview:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价导出', [冥想评价管理菜单ID], 5, '', '', 'F', '0', '0', 'system:meditationReview:export', '#', 'admin', NOW(), '', NULL, '');
```

## 菜单配置说明

### 菜单类型说明
- `M`: 目录（Directory）
- `C`: 菜单（Menu）
- `F`: 按钮（Button）

### 权限标识说明
- `system:meditation:list` - 冥想列表查询权限
- `system:meditation:query` - 冥想详情查询权限
- `system:meditation:add` - 冥想新增权限
- `system:meditation:edit` - 冥想修改权限
- `system:meditation:remove` - 冥想删除权限
- `system:meditation:export` - 冥想导出权限

其他模块的权限标识类似，将 `meditation` 替换为对应的模块名即可。

## 功能特性

### 冥想管理
- ✅ 冥想信息的增删改查
- ✅ 冥想封面图片上传
- ✅ 音频文件上传和播放
- ✅ 富文本编辑器支持冥想描述和引导词
- ✅ 冥想发布/下架功能
- ✅ 冥想评分更新
- ✅ 冥想播放统计信息
- ✅ 按分类和类型筛选冥想

### 冥想订单管理
- ✅ 订单信息的增删改查
- ✅ 支付状态更新
- ✅ 退款处理
- ✅ 订单号自动生成
- ✅ 多种支付方式支持
- ✅ 订单详情查看

### 冥想评价管理
- ✅ 评价信息的增删改查
- ✅ 星级评分显示
- ✅ 按冥想和用户筛选评价
- ✅ 评价状态管理
- ✅ 评价统计功能（平均评分、星级分布）

## 路由组件路径

**冥想管理系统路由组件路径：**

```javascript
// 菜单配置中的component路径
{
  "冥想管理": "wechat/meditation/meditation/index",
  "冥想订单管理": "wechat/meditation/order/index", 
  "冥想评价管理": "wechat/meditation/review/index"
}
```

**对应的实际文件路径：**

```
src/views/wechat/meditation/meditation/index.vue
src/views/wechat/meditation/order/index.vue
src/views/wechat/meditation/review/index.vue
```

## 使用说明

1. 首先在后端数据库中添加上述菜单配置
2. 重启后端服务
3. 使用管理员账号登录系统
4. 在侧边栏中找到"冥想管理系统"菜单
5. 按照以下顺序进行配置：
   - 冥想管理：创建冥想内容并上传音频文件
   - 冥想订单管理：处理冥想订单
   - 冥想评价管理：管理冥想评价

## 注意事项

1. 确保后端Controller的路径与前端API调用路径一致
2. 权限标识需要与后端注解中的权限字符匹配
3. 菜单的component路径要与实际的Vue文件路径对应
4. 建议先在测试环境中验证所有功能正常后再部署到生产环境
5. 需要确保数据库中存在对应的字典类型数据（如冥想类型、冥想状态等）

## 后端Controller要求

需要确保后端已实现以下Controller中的所有接口：
- `PsyMeditationController`
- `PsyMeditationOrderController` 
- `PsyMeditationReviewController`

所有接口都应该按照提供的后端代码实现，确保前后端接口一致。

## 字典数据要求

需要在数据库中添加以下字典类型：

```sql
-- 冥想类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('冥想类型', 'sys_meditation_type', '0', 'admin', NOW(), '冥想类型列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '引导冥想', '1', 'sys_meditation_type', '', 'primary', 'Y', '0', 'admin', NOW(), '引导冥想');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '音乐冥想', '2', 'sys_meditation_type', '', 'success', 'N', '0', 'admin', NOW(), '音乐冥想');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (3, '自然声音', '3', 'sys_meditation_type', '', 'info', 'N', '0', 'admin', NOW(), '自然声音');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (4, '白噪音', '4', 'sys_meditation_type', '', 'warning', 'N', '0', 'admin', NOW(), '白噪音');

-- 冥想状态
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('冥想状态', 'sys_meditation_status', '0', 'admin', NOW(), '冥想状态列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '草稿', '0', 'sys_meditation_status', '', 'info', 'Y', '0', 'admin', NOW(), '草稿状态');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '已发布', '1', 'sys_meditation_status', '', 'success', 'N', '0', 'admin', NOW(), '已发布');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (3, '已下架', '2', 'sys_meditation_status', '', 'danger', 'N', '0', 'admin', NOW(), '已下架');
```

整个冥想管理系统的前端功能已经完整实现，与您提供的后端Controller接口完全对应，具备了完整的冥想管理能力！
