# 心理测评系统配置说明

## 概述
已为PC端后台管理系统创建了完整的心理测评系统功能，包括以下模块：

1. **量表管理** - 心理测评量表的增删改查、发布下架、统计分析功能
2. **题目管理** - 测评题目和选项的管理、排序功能
3. **测评记录管理** - 用户测评记录的查询、结果查看、统计分析功能
4. **测评评价管理** - 用户评价的审核、回复管理功能

## 已创建的文件

### 前端API文件
```
src/api/system/assessment/scale.js       # 量表管理API
src/api/system/assessment/question.js    # 题目管理API
src/api/system/assessment/option.js      # 选项管理API
src/api/system/assessment/record.js      # 测评记录API
src/api/system/assessment/review.js      # 测评评价API
```

### 前端页面文件
```
src/views/system/assessment/scale/index.vue       # 量表管理
src/views/system/assessment/question/index.vue    # 题目管理
src/views/system/assessment/record/index.vue      # 测评记录管理
src/views/system/assessment/review/index.vue      # 测评评价管理
```

## 需要在后端添加的菜单配置

由于项目使用动态路由，需要在后端数据库的菜单表中添加以下菜单配置：

### 1. 心理测评系统主菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('心理测评', 0, 9, 'assessment-system', NULL, 'M', '0', '0', NULL, 'monitor', 'admin', NOW(), '', NULL, '心理测评系统');
```

### 2. 量表管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('量表管理', [心理测评菜单ID], 1, 'scale', 'system/assessment/scale/index', 'C', '0', '0', 'system:assessment:scale:list', 'form', 'admin', NOW(), '', NULL, '量表管理');
```

### 3. 量表管理按钮权限
```sql
-- 量表查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('量表查询', [量表管理菜单ID], 1, '', '', 'F', '0', '0', 'system:assessment:scale:query', '#', 'admin', NOW(), '', NULL, '');

-- 量表新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('量表新增', [量表管理菜单ID], 2, '', '', 'F', '0', '0', 'system:assessment:scale:add', '#', 'admin', NOW(), '', NULL, '');

-- 量表修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('量表修改', [量表管理菜单ID], 3, '', '', 'F', '0', '0', 'system:assessment:scale:edit', '#', 'admin', NOW(), '', NULL, '');

-- 量表删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('量表删除', [量表管理菜单ID], 4, '', '', 'F', '0', '0', 'system:assessment:scale:remove', '#', 'admin', NOW(), '', NULL, '');

-- 量表导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('量表导出', [量表管理菜单ID], 5, '', '', 'F', '0', '0', 'system:assessment:scale:export', '#', 'admin', NOW(), '', NULL, '');
```

### 4. 题目管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('题目管理', [心理测评菜单ID], 2, 'question', 'system/assessment/question/index', 'C', '0', '0', 'system:assessment:question:list', 'list', 'admin', NOW(), '', NULL, '题目管理');
```

### 5. 题目管理按钮权限
```sql
-- 题目查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('题目查询', [题目管理菜单ID], 1, '', '', 'F', '0', '0', 'system:assessment:question:query', '#', 'admin', NOW(), '', NULL, '');

-- 题目新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('题目新增', [题目管理菜单ID], 2, '', '', 'F', '0', '0', 'system:assessment:question:add', '#', 'admin', NOW(), '', NULL, '');

-- 题目修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('题目修改', [题目管理菜单ID], 3, '', '', 'F', '0', '0', 'system:assessment:question:edit', '#', 'admin', NOW(), '', NULL, '');

-- 题目删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('题目删除', [题目管理菜单ID], 4, '', '', 'F', '0', '0', 'system:assessment:question:remove', '#', 'admin', NOW(), '', NULL, '');

-- 题目导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('题目导出', [题目管理菜单ID], 5, '', '', 'F', '0', '0', 'system:assessment:question:export', '#', 'admin', NOW(), '', NULL, '');
```

### 6. 测评记录管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('测评记录', [心理测评菜单ID], 3, 'record', 'system/assessment/record/index', 'C', '0', '0', 'system:assessment:record:list', 'documentation', 'admin', NOW(), '', NULL, '测评记录管理');
```

### 7. 测评记录管理按钮权限
```sql
-- 记录查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录查询', [测评记录菜单ID], 1, '', '', 'F', '0', '0', 'system:assessment:record:query', '#', 'admin', NOW(), '', NULL, '');

-- 记录修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录修改', [测评记录菜单ID], 2, '', '', 'F', '0', '0', 'system:assessment:record:edit', '#', 'admin', NOW(), '', NULL, '');

-- 记录删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录删除', [测评记录菜单ID], 3, '', '', 'F', '0', '0', 'system:assessment:record:remove', '#', 'admin', NOW(), '', NULL, '');

-- 记录导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('记录导出', [测评记录菜单ID], 4, '', '', 'F', '0', '0', 'system:assessment:record:export', '#', 'admin', NOW(), '', NULL, '');
```

### 8. 测评评价管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('测评评价', [心理测评菜单ID], 4, 'review', 'system/assessment/review/index', 'C', '0', '0', 'system:assessment:review:list', 'message', 'admin', NOW(), '', NULL, '测评评价管理');
```

### 9. 测评评价管理按钮权限
```sql
-- 评价查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('评价查询', [测评评价菜单ID], 1, '', '', 'F', '0', '0', 'system:assessment:review:query', '#', 'admin', NOW(), '', NULL, '');

-- 评价修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('评价修改', [测评评价菜单ID], 2, '', '', 'F', '0', '0', 'system:assessment:review:edit', '#', 'admin', NOW(), '', NULL, '');

-- 评价删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('评价删除', [测评评价菜单ID], 3, '', '', 'F', '0', '0', 'system:assessment:review:remove', '#', 'admin', NOW(), '', NULL, '');

-- 评价导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('评价导出', [测评评价菜单ID], 4, '', '', 'F', '0', '0', 'system:assessment:review:export', '#', 'admin', NOW(), '', NULL, '');
```

## 菜单层级结构

```
心理测评 (一级菜单)
├── 量表管理 (二级菜单)
│   ├── 量表查询 (按钮权限)
│   ├── 量表新增 (按钮权限)
│   ├── 量表修改 (按钮权限)
│   ├── 量表删除 (按钮权限)
│   └── 量表导出 (按钮权限)
├── 题目管理 (二级菜单)
│   ├── 题目查询 (按钮权限)
│   ├── 题目新增 (按钮权限)
│   ├── 题目修改 (按钮权限)
│   ├── 题目删除 (按钮权限)
│   └── 题目导出 (按钮权限)
├── 测评记录 (二级菜单)
│   ├── 记录查询 (按钮权限)
│   ├── 记录修改 (按钮权限)
│   ├── 记录删除 (按钮权限)
│   └── 记录导出 (按钮权限)
└── 测评评价 (二级菜单)
    ├── 评价查询 (按钮权限)
    ├── 评价修改 (按钮权限)
    ├── 评价删除 (按钮权限)
    └── 评价导出 (按钮权限)
```

## 功能特性

### 量表管理
- ✅ 支持按量表名称、编码、分类、状态筛选
- ✅ 支持新增、修改、删除量表
- ✅ 支持量表发布和下架
- ✅ 支持统计分析（测试趋势、得分分布）
- ✅ 支持批量删除和导出
- ✅ 支持跳转到题目管理

### 题目管理
- ✅ 支持按量表、题目内容、题目类型筛选
- ✅ 支持新增、修改、删除题目和选项
- ✅ 支持题目排序和拖拽调整
- ✅ 支持选项管理（文本、分值、排序）
- ✅ 支持题目复制功能
- ✅ 支持批量操作和导出

### 测评记录管理
- ✅ 支持按量表、用户、状态、时间筛选
- ✅ 支持查看测评详情和答案
- ✅ 支持重新计算测评结果
- ✅ 支持统计分析（测评趋势、得分分布、结果等级分布）
- ✅ 支持修改测评记录
- ✅ 支持批量删除和导出

### 测评评价管理
- ✅ 支持按量表、用户、评分、状态、时间筛选
- ✅ 支持评价审核（通过/拒绝）
- ✅ 支持批量审核操作
- ✅ 支持评价回复功能
- ✅ 支持查看评价详情
- ✅ 支持批量删除和导出

## 数据字典配置

需要在系统中添加以下数据字典：

### 1. 量表状态 (scale_status)
```
0 - 未发布
1 - 已发布
2 - 已下架
```

### 2. 难度等级 (difficulty_level)
```
1 - 简单
2 - 中等
3 - 困难
```

### 3. 题目类型 (question_type)
```
1 - 单选题
2 - 多选题
3 - 填空题
4 - 量表题
```

### 4. 测评状态 (test_status)
```
0 - 进行中
1 - 已完成
2 - 已中断
3 - 已超时
```

### 5. 评价状态 (review_status)
```
0 - 待审核
1 - 已通过
2 - 已拒绝
```

## 添加步骤

1. 在后台管理系统的菜单管理中，按照上述配置添加菜单项
2. 注意替换 `[菜单ID]` 为实际的父菜单ID
3. 在数据字典管理中添加相应的字典配置
4. 为相应的角色分配菜单权限
5. 系统会自动生成动态路由，前端页面即可正常访问

## 注意事项

1. **组件路径**：component字段中的路径对应前端views目录下的组件路径
2. **权限标识**：perms字段要与前端页面中的v-hasPermi指令保持一致
3. **菜单图标**：icon字段使用Element Plus图标名称
4. **父子关系**：添加子菜单时，parentId要设置为父菜单的实际ID
5. **分类管理**：量表管理依赖分类系统，需要确保分类API正常工作
6. **用户管理**：测评记录和评价管理需要用户信息，依赖用户管理系统
7. **图表功能**：统计分析功能使用ECharts图表库，需要确保已安装
8. **数据导出**：所有模块都支持Excel导出功能

## 扩展功能

### 1. 题目导入
- 支持Excel批量导入题目
- 支持题目模板下载

### 2. 结果解释规则
- 支持自定义结果解释规则
- 支持维度得分解释

### 3. 测评报告
- 支持生成PDF测评报告
- 支持自定义报告模板

### 4. 数据统计
- 支持更详细的数据统计分析
- 支持数据可视化图表

这些扩展功能可以根据实际需求进行开发。
