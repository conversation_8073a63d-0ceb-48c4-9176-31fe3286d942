# 心理测评系统后台管理API适配说明

## 概述

根据后端调整的测评相关接口，前端后台管理系统需要进行相应的API适配。本文档详细说明了后台管理部分的适配工作。

## 主要变更

### 1. API路径确认

**实际路径格式：** `/system/assessment/xxx` （保持原有格式）

### 2. 接口功能适配

根据最新的后端接口文档，确认以下接口：
- 量表管理：`/system/assessment/scale/xxx`
- 题目管理：`/system/assessment/question/xxx`
- 测评记录：`/system/assessment/record/xxx`

### 3. 新增功能支持

- 生成测评报告功能
- 查看测评答案功能
- 测评统计分析功能

## 已完成的适配工作

### 1. 量表管理 (`src/views/system/assessment/scale/index.vue`)

#### 1.1 API路径确认
```javascript
// 实际使用的路径（与后端接口文档一致）
'/system/assessment/scale/list'
'/system/assessment/scale/publish/{id}'
'/system/assessment/scale/offline/{id}'
'/system/assessment/scale/stats/{id}'
```

#### 1.2 新增功能

**复制量表功能：**
- 添加了"复制量表"下拉菜单项
- 实现了 `handleCopy()` 函数
- 支持输入新量表名称进行复制

**验证量表功能：**
- 添加了"验证量表"下拉菜单项
- 实现了 `handleValidate()` 函数
- 显示验证结果和错误信息

#### 1.3 权限控制
- 复制量表：需要 `system:assessment:scale:add` 权限
- 验证量表：需要 `system:assessment:scale:query` 权限

### 2. 测评记录管理 (`src/views/system/assessment/record/index.vue`)

#### 2.1 新增功能

**生成报告功能：**
- 添加了"生成报告"下拉菜单项
- 实现了 `handleReport()` 函数
- 支持生成测评报告

**查看答案功能：**
- 已有功能，通过路由跳转到答案详情页面

#### 2.2 权限控制
- 生成报告：需要 `system:assessment:record:query` 权限

### 3. API文件更新

#### 3.1 量表API (`src/api/system/assessment/scale.js`)
```javascript
// 新增接口
export function copyScale(id, data)      // 复制量表
export function validateScale(id)       // 验证量表完整性

// 更新接口
export function publishScale(id)        // 发布量表 (POST方法)
export function offlineScale(id)        // 下架量表 (POST方法)
```

#### 3.2 记录API (`src/api/system/assessment/record.js`)
```javascript
// 新增接口
export function getRecordAnswers(id)     // 查看测评答案
export function generateRecordReport(id, reportLevel) // 生成测评报告
export function getTrendStats(days)     // 获取测评趋势统计
```

## 功能详细说明

### 1. 复制量表功能

**使用场景：** 基于现有量表创建新量表

**操作流程：**
1. 在量表列表中点击"更多"按钮
2. 选择"复制量表"
3. 输入新量表名称
4. 确认复制

**技术实现：**
```javascript
function handleCopy(row) {
  proxy.$modal.prompt('请输入新量表名称', '复制量表', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /\S/,
    inputErrorMessage: '量表名称不能为空'
  }).then(({ value }) => {
    return copyScale(row.id, { name: value });
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("复制成功");
  }).catch(() => {});
}
```

### 2. 验证量表功能

**使用场景：** 检查量表配置的完整性和正确性

**验证内容：**
- 量表基本信息完整性
- 题目配置正确性
- 计分规则有效性
- 分量表配置合理性

**技术实现：**
```javascript
function handleValidate(row) {
  validateScale(row.id).then(response => {
    if (response.data.isValid) {
      proxy.$modal.msgSuccess("量表验证通过");
    } else {
      const errors = response.data.errors || [];
      proxy.$modal.msgWarning(`验证失败：${errors.join(', ')}`);
    }
  }).catch(() => {
    proxy.$modal.msgError("验证失败");
  });
}
```

### 3. 生成报告功能

**使用场景：** 为已完成的测评记录生成详细报告

**报告内容：**
- 总体得分和等级
- 分量表得分
- 详细分析
- 建议与指导

**技术实现：**
```javascript
function handleReport(row) {
  proxy.$modal.confirm('是否生成测评报告？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    return generateRecordReport(row.id, 1);
  }).then(response => {
    proxy.$modal.msgSuccess("报告生成成功");
    console.log('报告数据:', response.data);
  }).catch(() => {});
}
```

## 数据结构变更

### 1. 量表数据结构 (PsyTScale)

**新增字段：**
- `alias`: 量表别名
- `payMode`: 付费模式 (0-免费, 1-付费, 2-部分付费)
- `payPhase`: 付费阶段
- `originalPrice`: 原价
- `currentPrice`: 现价
- `freeReportLevel`: 免费报告层级
- `paidReportLevel`: 付费报告层级
- `searchKeywords`: 搜索关键词
- `searchCount`: 搜索次数
- `viewCount`: 查看次数
- `testCount`: 测试次数
- `ratingAvg`: 平均评分
- `ratingCount`: 评分次数

### 2. 测评记录数据结构 (PsyTAssessmentRecord)

**新增字段：**
- `sessionId`: 会话ID
- `resultLevel`: 结果等级
- `resultDescription`: 结果描述
- `currentQuestionNo`: 当前题目序号
- `answeredCount`: 已答题数
- `progress`: 进度百分比
- `duration`: 测评耗时
- `source`: 测评来源
- `reportLevel`: 报告层级

## 权限配置

### 1. 量表管理权限
- `system:assessment:scale:list` - 查看量表列表
- `system:assessment:scale:query` - 查看量表详情
- `system:assessment:scale:add` - 新增量表
- `system:assessment:scale:edit` - 编辑量表
- `system:assessment:scale:remove` - 删除量表

### 2. 测评记录权限
- `system:assessment:record:list` - 查看记录列表
- `system:assessment:record:query` - 查看记录详情
- `system:assessment:record:edit` - 编辑记录
- `system:assessment:record:remove` - 删除记录

## 测试建议

### 1. 功能测试
- [ ] 复制量表功能测试
- [ ] 验证量表功能测试
- [ ] 生成报告功能测试
- [ ] 权限控制测试

### 2. 接口测试
- [ ] API路径更新验证
- [ ] 新增接口功能验证
- [ ] 错误处理测试

### 3. 用户体验测试
- [ ] 操作流程顺畅性
- [ ] 错误提示友好性
- [ ] 加载状态显示

## 部署清单

### 1. 代码更新
- [x] 更新量表管理API文件
- [x] 更新测评记录API文件
- [x] 更新量表管理页面组件
- [x] 更新测评记录页面组件

### 2. 权限配置
- [ ] 确认权限代码配置
- [ ] 更新角色权限分配

### 3. 测试验证
- [ ] 本地功能测试
- [ ] 接口联调测试
- [ ] 权限功能测试

## 注意事项

1. **向后兼容性**：确保现有功能不受影响
2. **权限控制**：新功能需要适配现有权限系统
3. **错误处理**：完善错误提示和异常处理
4. **用户体验**：保持操作流程的一致性

## 后续工作建议

1. **完善报告功能**：
   - 添加报告预览功能
   - 支持报告下载和分享
   - 实现多级报告展示

2. **增强验证功能**：
   - 提供详细的验证报告
   - 支持批量验证
   - 添加验证历史记录

3. **优化用户体验**：
   - 添加操作确认提示
   - 优化加载状态显示
   - 完善错误处理机制

## 更新日志

- 2024-01-XX: 完成后台管理API适配
- 2024-01-XX: 新增复制和验证量表功能
- 2024-01-XX: 新增生成报告功能
