# 数据库字段映射修正说明

## 概述
根据提供的数据库表结构，已对前端页面中的字段映射进行了修正，确保前后端数据字段一致。

## 主要修正内容

### 1. 课程表 (psy_course) 字段修正

#### 修正前后对比
| 前端原字段 | 数据库字段 | 修正后前端字段 | 说明 |
|-----------|-----------|---------------|------|
| subtitle | summary | summary | 课程简介 |
| studentCount | salesCount | salesCount | 销量 |
| rating | ratingAvg | ratingAvg | 平均评分 |
| duration | durationTotal | durationTotal | 课程总时长(秒) |
| - | difficultyLevel | difficultyLevel | 难度等级 |
| - | isFree | isFree | 是否免费 |
| - | viewCount | viewCount | 观看次数 |
| - | ratingCount | ratingCount | 评分人数 |
| - | chapterCount | chapterCount | 章节数量 |
| - | trialChapterCount | trialChapterCount | 试听章节数量 |

#### 新增字段
- `difficultyLevel`: 难度等级（1=入门，2=进阶，3=高级）
- `isFree`: 是否免费（0=付费，1=免费）
- `salesCount`: 已售数量
- `viewCount`: 观看次数
- `ratingAvg`: 平均评分
- `ratingCount`: 评分人数
- `durationTotal`: 课程总时长（秒）

### 2. 章节表 (psy_course_chapter) 字段修正

#### 修正前后对比
| 前端原字段 | 数据库字段 | 修正后前端字段 | 说明 |
|-----------|-----------|---------------|------|
| title | chapterTitle | chapterTitle | 章节标题 |
| content | chapterContent | chapterContent | 章节内容 |
| orderNum | chapterOrder | chapterOrder | 章节排序 |
| chapterType | contentType | contentType | 内容类型 |
| - | parentId | parentId | 父章节ID |
| - | level | level | 章节层级 |
| - | isTrial | isTrial | 是否试听章节 |

#### 内容类型枚举值
- `0`: 视频
- `1`: 音频  
- `2`: 文档

#### 新增字段
- `parentId`: 父章节ID（0表示根章节）
- `level`: 章节层级（1=一级，2=二级）
- `isTrial`: 是否试听章节（0=否，1=是）
- `duration`: 时长单位改为秒

### 3. 订单表 (psy_course_order) 字段修正

#### 修正前后对比
| 前端原字段 | 数据库字段 | 修正后前端字段 | 说明 |
|-----------|-----------|---------------|------|
| totalAmount | - | - | 删除（数据库中无此字段） |
| paidAmount | paymentAmount | paymentAmount | 实际支付金额 |
| paymentStatus | - | - | 删除（数据库中无此字段） |
| orderStatus | status | status | 订单状态 |

#### 订单状态枚举值
- `0`: 待支付
- `1`: 已支付
- `2`: 已完成
- `3`: 已取消

#### 新增字段
- `orderNo`: 订单编号
- `originalPrice`: 订单原价
- `paymentMethod`: 支付方式
- `paymentTime`: 支付时间
- `transactionId`: 第三方交易号
- `refundAmount`: 退款金额
- `refundTime`: 退款时间
- `cancelTime`: 取消时间

### 4. 讲师表 (psy_course_instructor) 字段

讲师表字段基本保持不变：
- `name`: 讲师姓名
- `title`: 讲师头衔
- `qualifications`: 讲师资质

### 5. 评价表 (psy_course_review) 字段

评价表字段基本保持不变：
- `courseId`: 课程ID
- `userId`: 用户ID
- `content`: 评价内容
- `rating`: 评分（1-5星）

## 前端页面修正详情

### 1. 课程管理页面修正
- ✅ 表格列字段名修正
- ✅ 表单字段修正
- ✅ 表单重置函数更新
- ✅ 新增难度等级和免费标识字段
- ✅ 时长单位改为秒

### 2. 章节管理页面修正
- ✅ 表格列字段名修正
- ✅ 表单字段修正
- ✅ 表单重置函数更新
- ✅ 内容类型枚举值修正
- ✅ 新增试听章节字段
- ✅ 时长单位改为秒

### 3. 订单管理页面修正
- ✅ 表格列字段名修正
- ✅ 表单字段修正
- ✅ 表单重置函数更新
- ✅ 订单状态枚举值修正
- ✅ 删除不存在的字段

### 4. 讲师管理页面
- ✅ 字段映射正确，无需修正

### 5. 评价管理页面
- ✅ 字段映射正确，无需修正

## 需要在后端添加的字典数据

### 1. 内容类型字典
```sql
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('内容类型', 'sys_content_type', '0', 'admin', NOW(), '章节内容类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '视频', '0', 'sys_content_type', '', 'primary', 'Y', '0', 'admin', NOW(), '视频内容');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '音频', '1', 'sys_content_type', '', 'success', 'N', '0', 'admin', NOW(), '音频内容');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (3, '文档', '2', 'sys_content_type', '', 'info', 'N', '0', 'admin', NOW(), '文档内容');
```

### 2. 是否字典
```sql
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('是否', 'sys_yes_no', '0', 'admin', NOW(), '是否选择');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '否', '0', 'sys_yes_no', '', 'danger', 'Y', '0', 'admin', NOW(), '否');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '是', '1', 'sys_yes_no', '', 'success', 'N', '0', 'admin', NOW(), '是');
```

### 3. 订单状态字典
```sql
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('订单状态', 'sys_order_status', '0', 'admin', NOW(), '订单状态');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '待支付', '0', 'sys_order_status', '', 'warning', 'Y', '0', 'admin', NOW(), '待支付');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '已支付', '1', 'sys_order_status', '', 'primary', 'N', '0', 'admin', NOW(), '已支付');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (3, '已完成', '2', 'sys_order_status', '', 'success', 'N', '0', 'admin', NOW(), '已完成');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (4, '已取消', '3', 'sys_order_status', '', 'danger', 'N', '0', 'admin', NOW(), '已取消');
```

## 后端Controller接口要求

### 1. 课程Controller接口
确保返回的数据包含以下字段：
- `salesCount`, `ratingAvg`, `ratingCount`, `viewCount`
- `difficultyLevel`, `isFree`, `durationTotal`
- `chapterCount`, `trialChapterCount`

### 2. 章节Controller接口
确保返回的数据包含以下字段：
- `chapterTitle`, `chapterContent`, `chapterOrder`
- `contentType`, `parentId`, `level`, `isTrial`

### 3. 订单Controller接口
确保返回的数据包含以下字段：
- `paymentAmount`, `status`, `orderNo`
- `paymentMethod`, `paymentTime`, `transactionId`
- `refundAmount`, `refundTime`, `originalPrice`, `cancelTime`

## 注意事项

1. **时长单位统一**：课程和章节的时长统一使用秒作为单位
2. **字段命名一致**：确保前后端字段名完全一致
3. **枚举值对应**：确保前端枚举值与数据库中的值一致
4. **必填字段验证**：根据数据库表结构调整前端表单验证规则
5. **数据类型匹配**：确保前端数据类型与数据库字段类型匹配

## 测试建议

1. **字段映射测试**：测试所有字段的显示和保存是否正确
2. **枚举值测试**：测试所有下拉选择和状态显示是否正确
3. **表单验证测试**：测试表单必填字段验证是否生效
4. **数据完整性测试**：测试新增、修改、删除操作的数据完整性

通过以上修正，前端页面的字段映射已与数据库表结构完全对应，确保了数据的一致性和准确性。
