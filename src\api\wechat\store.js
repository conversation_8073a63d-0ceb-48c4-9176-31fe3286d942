import request from '@/utils/request'

// 查询门店列表
export function listStore(query) {
  return request({
    url: '/wechat/store/list',
    method: 'get',
    params: query
  })
}

// 查询门店详细信息
export function getStore(id) {
  return request({
    url: `/wechat/store/${id}`,
    method: 'get'
  })
}

// 新增门店
export function addStore(data) {
  return request({
    url: '/wechat/store',
    method: 'post',
    data: data
  })
}

// 修改门店
export function updateStore(data) {
  return request({
    url: '/wechat/store',
    method: 'put',
    data: data
  })
}

// 删除门店
export function delStore(ids) {
  return request({
    url: `/wechat/store/${ids}`,
    method: 'delete'
  })
}

// 更新门店状态
export function updateStoreStatus(id, status) {
  return request({
    url: '/wechat/store/status',
    method: 'put',
    data: {
      id: id,
      status: status
    }
  })
}
