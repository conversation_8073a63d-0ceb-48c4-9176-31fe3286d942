<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="关键词" prop="keyword">
            <el-input
               v-model="queryParams.keyword"
               placeholder="请输入关键词"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="建议类型" prop="suggestionType">
            <el-select
               v-model="queryParams.suggestionType"
               placeholder="请选择建议类型"
               clearable
               style="width: 240px"
            >
               <el-option label="自动生成" value="auto" />
               <el-option label="手动添加" value="manual" />
               <el-option label="热门推荐" value="hot" />
            </el-select>
         </el-form-item>
         <el-form-item label="状态" prop="status">
            <el-select
               v-model="queryParams.status"
               placeholder="建议状态"
               clearable
               style="width: 240px"
            >
               <el-option
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['search:suggestion:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['search:suggestion:edit']"
            >修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['search:suggestion:remove']"
            >删除</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['search:suggestion:export']"
            >导出</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="suggestionList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="ID" align="center" prop="id" width="80" />
         <el-table-column label="关键词" align="center" prop="keyword" :show-overflow-tooltip="true" />
         <el-table-column label="建议类型" align="center" prop="suggestionType" width="100">
            <template #default="scope">
               <dict-tag :options="suggestion_type_options" :value="scope.row.suggestionType" />
            </template>
         </el-table-column>
         <el-table-column label="搜索次数" align="center" prop="searchCount" width="100" />
         <el-table-column label="优先级" align="center" prop="priority" width="100">
            <template #default="scope">
               <el-tag :type="getPriorityType(scope.row.priority)">{{ scope.row.priority }}</el-tag>
            </template>
         </el-table-column>
         <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['search:suggestion:edit']">修改</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['search:suggestion:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>
      
      <pagination
         v-show="total>0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改搜索建议对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="suggestionRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="关键词" prop="keyword">
               <el-input v-model="form.keyword" placeholder="请输入关键词" />
            </el-form-item>
            <el-form-item label="建议类型" prop="suggestionType">
               <el-select v-model="form.suggestionType" placeholder="请选择建议类型">
                  <el-option label="自动生成" value="auto" />
                  <el-option label="手动添加" value="manual" />
                  <el-option label="热门推荐" value="hot" />
               </el-select>
            </el-form-item>
            <el-form-item label="优先级" prop="priority">
               <el-input-number v-model="form.priority" :min="1" :max="999" />
               <span style="margin-left: 10px; color: #999; font-size: 12px;">数值越大优先级越高</span>
            </el-form-item>
            <el-form-item label="状态" prop="status">
               <el-radio-group v-model="form.status">
                  <el-radio
                     v-for="dict in sys_normal_disable"
                     :key="dict.value"
                     :value="dict.value"
                  >{{dict.label}}</el-radio>
               </el-radio-group>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="SearchSuggestion">
import { listSearchSuggestion, getSearchSuggestion, delSearchSuggestion, addSearchSuggestion, updateSearchSuggestion, exportSearchSuggestion } from "@/api/system/search/suggestion";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const suggestionList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 建议类型选项
const suggestion_type_options = ref([
  { label: "自动生成", value: "auto" },
  { label: "手动添加", value: "manual" },
  { label: "热门推荐", value: "hot" }
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword: null,
    suggestionType: null,
    status: null
  },
  rules: {
    keyword: [
      { required: true, message: "关键词不能为空", trigger: "blur" }
    ],
    suggestionType: [
      { required: true, message: "建议类型不能为空", trigger: "change" }
    ],
    priority: [
      { required: true, message: "优先级不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询搜索建议列表 */
function getList() {
  loading.value = true;
  listSearchSuggestion(queryParams.value).then(response => {
    suggestionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 获取优先级标签类型
function getPriorityType(priority) {
  if (priority >= 90) return 'danger';
  if (priority >= 70) return 'warning';
  if (priority >= 50) return 'success';
  return 'info';
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    keyword: null,
    suggestionType: "manual",
    priority: 50,
    status: "0"
  };
  proxy.resetForm("suggestionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加搜索建议";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getSearchSuggestion(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改搜索建议";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["suggestionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateSearchSuggestion(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSearchSuggestion(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const suggestionIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除搜索建议编号为"' + suggestionIds + '"的数据项？').then(function() {
    return delSearchSuggestion(suggestionIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/search/suggestion/export', {
    ...queryParams.value
  }, `search_suggestion_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
