import request from '@/utils/request'

// 查询咨询记录列表
export function listConsultationRecord(query) {
  return request({
    url: '/system/consultationRecord/list',
    method: 'get',
    params: query
  })
}

// 导出咨询记录列表
export function exportConsultationRecord(query) {
  return request({
    url: '/system/consultationRecord/export',
    method: 'post',
    params: query
  })
}

// 获取咨询记录详细信息
export function getConsultationRecord(id) {
  return request({
    url: `/system/consultationRecord/${id}`,
    method: 'get'
  })
}

// 获取咨询记录详细信息（包含关联信息）
export function getConsultationRecordDetails(id) {
  return request({
    url: `/system/consultationRecord/details/${id}`,
    method: 'get'
  })
}

// 新增咨询记录
export function addConsultationRecord(data) {
  return request({
    url: '/system/consultationRecord',
    method: 'post',
    data: data
  })
}

// 修改咨询记录
export function updateConsultationRecord(data) {
  return request({
    url: '/system/consultationRecord',
    method: 'put',
    data: data
  })
}

// 删除咨询记录
export function delConsultationRecord(ids) {
  return request({
    url: `/system/consultationRecord/${ids}`,
    method: 'delete'
  })
}

// 根据用户ID查询咨询记录列表
export function getConsultationRecordsByUser(userId) {
  return request({
    url: `/system/consultationRecord/user/${userId}`,
    method: 'get'
  })
}

// 根据咨询师ID查询咨询记录列表
export function getConsultationRecordsByConsultant(consultantId) {
  return request({
    url: `/system/consultationRecord/consultant/${consultantId}`,
    method: 'get'
  })
}

// 根据订单ID查询咨询记录
export function getConsultationRecordByOrder(orderId) {
  return request({
    url: `/system/consultationRecord/order/${orderId}`,
    method: 'get'
  })
}

// 开始咨询
export function startConsultation(orderId) {
  return request({
    url: `/system/consultationRecord/start/${orderId}`,
    method: 'post'
  })
}

// 结束咨询
export function endConsultation(recordId, consultContent) {
  return request({
    url: `/system/consultationRecord/end/${recordId}`,
    method: 'post',
    params: {
      consultContent: consultContent
    }
  })
}

// 中断咨询
export function interruptConsultation(recordId, interruptType, reason) {
  return request({
    url: `/system/consultationRecord/interrupt/${recordId}`,
    method: 'post',
    params: {
      interruptType: interruptType,
      reason: reason
    }
  })
}

// 恢复咨询
export function resumeConsultation(recordId) {
  return request({
    url: `/system/consultationRecord/resume/${recordId}`,
    method: 'post'
  })
}

// 用户评价
export function rateConsultation(recordId, userRating) {
  return request({
    url: `/system/consultationRecord/rate/${recordId}`,
    method: 'post',
    params: {
      userRating: userRating
    }
  })
}

// 获取用户咨询统计
export function getUserConsultationStats(userId) {
  return request({
    url: `/system/consultationRecord/statistics/user/${userId}`,
    method: 'get'
  })
}

// 获取咨询师咨询统计
export function getConsultantConsultationStats(consultantId) {
  return request({
    url: `/system/consultationRecord/statistics/consultant/${consultantId}`,
    method: 'get'
  })
}

// 查询用户与咨询师的咨询记录
export function getConsultationHistory(userId, consultantId) {
  return request({
    url: `/system/consultationRecord/history/${userId}/${consultantId}`,
    method: 'get'
  })
}
