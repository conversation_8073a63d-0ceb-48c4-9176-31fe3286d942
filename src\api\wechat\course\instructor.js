import request from '@/utils/request'

// 查询讲师列表
export function listInstructor(query) {
  return request({
    url: '/system/instructor/list',
    method: 'get',
    params: query
  })
}

// 导出讲师列表
export function exportInstructor(query) {
  return request({
    url: '/system/instructor/export',
    method: 'post',
    params: query
  })
}

// 获取讲师详细信息
export function getInstructor(id) {
  return request({
    url: `/system/instructor/${id}`,
    method: 'get'
  })
}

// 获取讲师详细信息（包含头像图片）
export function getInstructorDetails(id) {
  return request({
    url: `/system/instructor/details/${id}`,
    method: 'get'
  })
}

// 新增讲师
export function addInstructor(data) {
  return request({
    url: '/system/instructor',
    method: 'post',
    data: data
  })
}

// 修改讲师
export function updateInstructor(data) {
  return request({
    url: '/system/instructor',
    method: 'put',
    data: data
  })
}

// 删除讲师
export function delInstructor(ids) {
  return request({
    url: `/system/instructor/${ids}`,
    method: 'delete'
  })
}

// 获取所有讲师简单信息（用于下拉框）
export function getSimpleInstructorList() {
  return request({
    url: '/system/instructor/simple',
    method: 'get'
  })
}
