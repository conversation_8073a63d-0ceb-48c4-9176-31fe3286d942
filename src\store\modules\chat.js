import { defineStore } from 'pinia'
import {
  getAllConversations,
  getMessageSessions,
  getConsultantSessions,
  getMessageHistory,
  sendMessageApi,
  withdrawMessage,
  markSessionRead,
  markMessageRead,
  createConversation,
  getConversationDetail,
  getAdminConversationDetail
} from '@/api/wechat/message'

let socket = null
const WEBSOCKET_URL = import.meta.env.VITE_APP_WS_URL || 'ws://localhost:8080/websocket/'
console.log('WEBSOCKET_URL', WEBSOCKET_URL)
export const useChatStore = defineStore('chat', {
  state: () => ({
    conversations: [], // 所有会话列表
    currentConversation: null, // 当前选中的会话
    messages: [], // 当前会话的消息列表
    loading: false, // 加载状态
    pageNum: 1, // 当前页码
    pageSize: 20, // 每页消息条数
    hasMore: true, // 是否有更多消息
    socketConnected: false, // WebSocket连接状态
    userId: null, // 当前用户ID
    userRole: 'consultant', // 用户角色: 'user'用户, 'consultant'咨询师, 'admin'管理员
    isLoadingMore: false, // 是否正在加载更多消息
    _markReadTimeout: null, // 用于防抖动的标记会话已读的计时器
    reconnectAttempts: 0 // 重连次数
  }),

  getters: {
    // 当前选中的会话ID
    currentConversationId: (state) => state.currentConversation?.conversationId,

    // 判断是否为管理员
    isAdmin: (state) => {
      // 这里需要从userStore获取roles，暂时返回false，在actions中处理
      return false
    },

    // 判断是否为咨询师
    isConsultant: (state) => {
      // 这里需要从userStore获取roles，暂时返回false，在actions中处理
      return false
    },

    // 未读消息总数
    totalUnreadCount: (state) => {
      if (state.userRole === 'admin') {
        // 管理员模式下统计所有会话中的用户未读数
        return state.conversations.reduce((total, conv) => total + (conv.userUnreadCount || 0), 0)
      } else if (state.userRole === 'consultant') {
        // 咨询师模式下统计所有会话中的咨询师未读数
        return state.conversations.reduce((total, conv) => total + (conv.consultantUnreadCount || 0), 0)
      } else {
        // 用户模式下统计所有会话中的用户未读数
        return state.conversations.reduce((total, conv) => total + (conv.userUnreadCount || 0), 0)
      }
    }
  },

  actions: {
    // 根据用户权限设置角色
    setUserRoleByPermissions(userRoles) {
      if (userRoles.includes('admin')) {
        this.userRole = 'admin'
      } else if (userRoles.includes('consultant')) {
        this.userRole = 'consultant'
      } else {
        this.userRole = 'user'
      }
      console.log('根据权限设置用户角色:', this.userRole)
    },

    // 初始化WebSocket连接
    initWebSocket(userId) {
      if (!userId) return

      this.userId = userId
      this.closeWebSocket() // 确保关闭之前的连接

      try {
        socket = new WebSocket(`${WEBSOCKET_URL}${userId}`)

        socket.onopen = () => {
          console.log('WebSocket连接已建立，等待服务器确认')
        }

        socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleWebSocketMessage(data)
          } catch (error) {
            console.error('处理WebSocket消息失败', error)
          }
        }

        socket.onclose = (event) => {
          this.socketConnected = false
          console.log('WebSocket连接关闭，关闭码:', event.code, '原因:', event.reason)

          // 只有在非正常关闭时才自动重连
          if (event.code !== 1000 && event.code !== 1001) {
            // 使用指数退避算法进行重连
            const retryDelay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000)
            console.log(`${retryDelay}ms后尝试重新连接`)

            setTimeout(() => {
              if (!this.socketConnected && this.userId) {
                console.log('开始重新连接WebSocket')
                this.initWebSocket(this.userId)
                this.reconnectAttempts++
              }
            }, retryDelay)
          } else {
            console.log('WebSocket正常关闭，不进行重连')
          }
        }

        socket.onerror = (error) => {
          console.error('WebSocket错误:', error)
          this.socketConnected = false
        }
      } catch (error) {
        console.error('初始化WebSocket失败:', error)
      }
    },

    // 处理WebSocket消息
    async handleWebSocketMessage(data) {
      // console.log('处理WebSocket消息:', data)

      // 尝试确定消息类型，即使没有type字段
      let messageType = data.type;

      // 如果没有type字段，根据消息内容推断类型
      if (!messageType) {
        if (data.messageId && data.senderId && data.content) {
          messageType = 'chat';
          console.log('推断为聊天消息');
        } else if (data.messageId && data.isWithdrawn === '1') {
          messageType = 'withdraw_notification';
          console.log('推断为撤回消息');
        } else if (data.timestamp) {
          messageType = 'heartbeat';
          console.log('推断为心跳消息');
        }
      }

      console.log('处理WebSocket消息类型:', messageType, data)

      switch (messageType) {
        case 'connect':
          // 处理连接成功消息
          console.log('WebSocket连接成功，开始发送心跳')
          this.socketConnected = true
          this.sendHeartbeat()
          break

        case 'heartbeat':
          // 心跳响应，不做处理
          break

        case 'heartbeat_check':
          // 心跳检测，回复心跳
          this.sendHeartbeat()
          break

        case 'chat':
          // 收到新消息，处理逻辑
          console.log('收到聊天消息，当前会话ID:', this.currentConversationId, '消息会话ID:', data.conversationId)

          const audio = new Audio('../../../public/audio.mp3')
          audio.play()
          // 确保消息数据格式正确
          if (!data.messageId) {
            console.warn('收到的消息缺少messageId', data)
            data.messageId = `temp_${Date.now()}`
          }

          // 更新会话列表中的最后消息信息
          await this.updateConversationLastMessage(data)

          // 如果是当前会话的消息，则添加到消息列表
          if (String(data.conversationId) === String(this.currentConversationId)) {
            // 检查消息是否已存在
            const existingIndex = this.messages.findIndex(m =>
              m.messageId === data.messageId ||
              (m.sendTime === data.sendTime && m.senderId === data.senderId && m.content === data.content)
            )

            if (existingIndex === -1) {
              console.log('添加新消息到当前会话:', data)
              // 强制设置响应式更新
              this.messages = [...this.messages, data]

              // 如果是在当前对话中收到别人的消息，立即标记已读并重置未读计数
              if (data.senderId != this.userId) {
                console.log('在当前对话收到新消息，立即标记已读');

                // 重置当前会话的未读计数
                const conversation = this.conversations.find(c =>
                  String(c.conversationId) === String(this.currentConversationId)
                );

                if (conversation) {
                  // 根据用户角色，重置相应的未读计数
                  if (this.userRole === 'consultant' || this.userRole === 'admin') {
                    if (conversation.consultantUnreadCount > 0) {
                      console.log('重置咨询师未读计数');
                      conversation.consultantUnreadCount = 0;

                      // 调用标记已读接口
                      this.markConversationRead(this.currentConversationId);
                    }
                  } else {
                    if (conversation.userUnreadCount > 0) {
                      console.log('重置用户未读计数');
                      conversation.userUnreadCount = 0;

                      // 调用标记已读接口
                      this.markConversationRead(this.currentConversationId);
                    }
                  }
                }
              }

              // 触发自定义事件，通知前端有新消息
              window.dispatchEvent(new CustomEvent('websocket-message-received', { detail: data }))
            } else {
              console.log('消息已存在，不重复添加')
            }
          } else {
            console.log('不是当前会话消息，仅更新会话列表')
          }
          break

        case 'withdraw_notification':
          // 消息被撤回，更新消息状态
          const messageId = data.messageId
          const msgIndex = this.messages.findIndex(m => m.messageId === messageId)
          if (msgIndex !== -1) {
            // 使用响应式方式更新
            const updatedMessages = [...this.messages]
            updatedMessages[msgIndex] = { ...updatedMessages[msgIndex], isWithdrawn: '1' }
            this.messages = updatedMessages
          }
          break

        case 'read_notification':
          // 消息已读通知，可以在界面上显示已读状态
          break

        default:
          console.log('未处理的WebSocket消息类型', messageType || '未知类型')
          // 尝试进一步分析消息内容
          if (data.conversationId && data.content) {
            console.log('未知类型消息但包含会话ID和内容，按聊天消息处理');
            // 按聊天消息处理
            this.updateConversationLastMessage(data);

            // 如果是当前会话，添加到消息列表
            if (String(data.conversationId) === String(this.currentConversationId)) {
              // 检查是否已存在
              const msgExists = this.messages.some(m =>
                m.messageId === data.messageId ||
                (m.sendTime === data.sendTime && m.senderId === data.senderId)
              );

              if (!msgExists) {
                this.messages = [...this.messages, data];
                window.dispatchEvent(new CustomEvent('websocket-message-received', { detail: data }));
              }
            }
          }
      }
    },

    // 更新会话的最后消息信息
    async updateConversationLastMessage(message) {
      // 查找对应的会话
      let conversation = this.conversations.find(c => String(c.conversationId) === String(message.conversationId))

      // 如果找不到对应的会话，重新获取会话列表
      if (!conversation) {
        console.log('未找到对应的会话，重新获取会话列表:', message.conversationId)
        await this.getConversations()
        // 重新查找会话
        conversation = this.conversations.find(c => String(c.conversationId) === String(message.conversationId))

        // 如果还是找不到，直接返回
        if (!conversation) {
          console.warn('重新获取会话列表后仍未找到会话:', message.conversationId)
          return
        }
      }

      console.log('更新会话最后消息:', conversation.conversationId)

      // 更新会话的最后消息信息
      conversation.lastMessage = message.content
      conversation.lastMessageTime = message.sendTime || new Date()
      conversation.lastSenderId = message.senderId

      // 更新未读计数
      if (message.senderId != this.userId) {
        // 检查是否是当前会话，如果不是当前会话，则增加未读数
        const isCurrentConversation = String(message.conversationId) === String(this.currentConversationId);
        console.log('是否当前会话:', isCurrentConversation, '发送者ID:', message.senderId, '当前用户ID:', this.userId);

        // 如果是用户发送的消息且管理员不在当前会话中，或者是咨询师/管理员接收的消息
        if (this.userRole === 'consultant' || this.userRole === 'admin') {
          // 如果管理员或咨询师收到用户消息且不在当前会话，则增加未读数
          if (!isCurrentConversation) {
            console.log('管理员/咨询师不在当前会话，增加未读数');
            conversation.consultantUnreadCount = (conversation.consultantUnreadCount || 0) + 1;
          } else {
            console.log('管理员/咨询师在当前会话，不增加未读数');
          }
        } else {
          // 用户收到咨询师消息
          console.log('用户收到咨询师消息，增加未读数');
          conversation.userUnreadCount = (conversation.userUnreadCount || 0) + 1;
        }
      } else {
        console.log('自己发送的消息，不增加未读数');
      }

      // 将该会话移到顶部
      const index = this.conversations.findIndex(c => String(c.conversationId) === String(message.conversationId))
      if (index > 0) {
        const [removed] = this.conversations.splice(index, 1)
        this.conversations.unshift(removed)
      }
    },

    // 发送心跳
    sendHeartbeat() {
      if (socket && socket.readyState === WebSocket.OPEN) {
        const heartbeat = {
          type: 'heartbeat',
          timestamp: Date.now()
        }
        try {
          socket.send(JSON.stringify(heartbeat))
          // 每15秒发送一次心跳
          setTimeout(() => {
            if (this.socketConnected) {
              this.sendHeartbeat()
            }
          }, 15000)
        } catch (error) {
          console.error('发送心跳失败:', error)
          this.socketConnected = false
        }
      }
    },

    // 关闭WebSocket连接
    closeWebSocket() {
      if (socket) {
        try {
          // 重置重连次数
          this.reconnectAttempts = 0
          // 正常关闭连接
          socket.close(1000, '用户主动关闭连接')
          socket = null
          this.socketConnected = false
        } catch (error) {
          console.error('关闭WebSocket连接失败:', error)
        }
      }
    },

    // 获取会话列表（区分管理员和普通用户）
    async getConversations() {
      this.loading = true
      try {
        let response

        if (this.userRole === 'admin') {
          // 管理员获取所有会话
          response = await getAllConversations()
        } else if (this.userRole === 'consultant') {
          // 咨询师获取与用户的会话
          response = await getConsultantSessions()
        } else {
          // 普通用户获取与咨询师的会话
          response = await getMessageSessions()
        }

        if (response.code === 200) {
          this.conversations = response.data
        }
      } catch (error) {
        console.error('获取会话列表失败', error)
      } finally {
        this.loading = false
      }
    },

    // 获取会话详情
    async getConversationDetail(conversationId) {
      try {
        const response = this.userRole === 'admin'
          ? await getAdminConversationDetail(conversationId)
          : await getConversationDetail(conversationId)

        if (response.code === 200) {
          return response.data
        }
        return null
      } catch (error) {
        console.error('获取会话详情失败', error)
        return null
      }
    },

    // 创建新会话
    async createNewConversation(consultantId) {
      try {
        const response = await createConversation(consultantId)
        if (response.code === 200) {
          // 添加到会话列表前先获取最新的会话列表
          await this.getConversations()

          // 设置当前会话
          const newConversation = response.data
          this.setCurrentConversation(newConversation)

          return newConversation
        }
        return null
      } catch (error) {
        console.error('创建会话失败', error)
        return null
      }
    },

    // 设置当前会话
    setCurrentConversation(conversation) {
      this.currentConversation = conversation
      this.pageNum = 1
      this.hasMore = true
      this.messages = []

      // 当切换到某个会话时，将该会话的未读数清零
      if (conversation?.conversationId) {
        console.log('切换到会话:', conversation.conversationId, '清零未读数');
        // 根据用户角色，清零相应的未读数
        if (this.userRole === 'consultant' || this.userRole === 'admin') {
          conversation.consultantUnreadCount = 0;
        } else {
          conversation.userUnreadCount = 0;
        }

        // 标记会话已读
        this.markConversationRead(conversation.conversationId);

        // 如果有会话ID，立即获取消息
        this.getMessages()
      }
    },

    // 获取会话消息历史
    async getMessages() {
      if (!this.currentConversation || !this.hasMore) return Promise.resolve()

      this.loading = true
      try {
        // 无论是否管理员，都使用普通消息历史接口
        const response = await getMessageHistory(
          this.currentConversation.conversationId,
          this.pageNum,
          this.pageSize
        )

        if (response.code === 200) {
          const rows = response.rows || []

          // 确保消息按时间排序
          const sortedRows = [...rows].sort((a, b) => {
            return new Date(a.sendTime) - new Date(b.sendTime)
          })

          if (this.pageNum === 1) {
            this.messages = sortedRows
            // 如果是第一页，触发一个事件表示需要滚动到底部
            document.dispatchEvent(new CustomEvent('chat-messages-loaded'))

            // 第一页加载完成时，更新未读计数
            // 注意：标记已读操作已在setCurrentConversation中处理，这里不再重复调用
            if (this.currentConversation?.conversationId && !this.isLoadingMore) {
              try {
                // 查找当前会话并确保未读计数为0
                const conversation = this.conversations.find(c =>
                  String(c.conversationId) === String(this.currentConversationId)
                )
                if (conversation) {
                  if (this.userRole === 'consultant' || this.userRole === 'admin') {
                    conversation.consultantUnreadCount = 0;
                  } else {
                    conversation.userUnreadCount = 0;
                  }
                  console.log('消息加载完成，已清零未读计数');
                }
              } catch (error) {
                console.error('更新未读计数失败', error);
              }
            }
          } else {
            this.messages = [...sortedRows, ...this.messages]
          }

          // 更新分页状态
          if (rows.length < this.pageSize) {
            this.hasMore = false
          } else {
            this.pageNum++
          }

          return Promise.resolve()
        }
      } catch (error) {
        console.error('获取消息历史失败', error)
        return Promise.reject(error)
      } finally {
        this.loading = false
        // this.isLoadingMore = false
      }
    },

    // 发送消息
    async sendMessage({ content, messageType = '0', fileUrl = '' }) {
      if (!this.currentConversation) return null

      // 构建消息数据
      const messageData = {
        conversationId: this.currentConversation.conversationId,
        receiverId: this.userRole === 'consultant' || this.userRole === 'admin'
          ? this.currentConversation.userId
          : this.currentConversation.consultantId,
        content,
        messageType,
        fileUrl,
        isUser: false
      }

      // 管理员模式下，使用WebSocket发送消息
      if (this.userRole === 'admin') {
        // 构建WebSocket消息
        const wsMessage = {
          type: 'chat',
          ...messageData,
          senderId: this.userId,
          sendTime: new Date().toISOString(),
          // 生成临时消息ID
          messageId: `temp_${Date.now()}`
        }

        // 发送消息到服务器
        const sent = this.sendWebSocketMessage(wsMessage)

        if (sent) {
          // 添加到本地消息列表
          this.messages.push(wsMessage)

          // 更新会话最后消息
          const conversation = this.conversations.find(c => c.conversationId === this.currentConversation.conversationId)
          if (conversation) {
            conversation.lastMessage = content
            conversation.lastMessageTime = new Date()
            conversation.lastSenderId = this.userId
          }

          return wsMessage
        } else {
          throw new Error('WebSocket未连接，无法发送消息')
        }
      } else {
        // 普通用户或咨询师使用API发送消息
        try {
          const response = await sendMessageApi(messageData)

          if (response.code === 200) {
            // 添加到消息列表
            this.messages.push(response.data)

            // 更新会话最后消息
            const conversation = this.conversations.find(c => c.conversationId === this.currentConversation.conversationId)
            if (conversation) {
              conversation.lastMessage = content
              conversation.lastMessageTime = new Date()
              conversation.lastSenderId = this.userId
            }

            return response.data
          }
          return null
        } catch (error) {
          console.error('发送消息失败', error)
          throw error
        }
      }
    },

    // 通过WebSocket发送消息
    sendWebSocketMessage(message) {
      if (socket && socket.readyState === WebSocket.OPEN) {
        console.log('通过WebSocket发送消息:', message)
        socket.send(JSON.stringify(message))
        return true
      }
      console.warn('WebSocket未连接，无法发送消息')
      return false
    },

    // 撤回消息
    async withdrawMessageAction(messageId) {
      try {
        const response = await withdrawMessage(messageId)
        if (response.code === 200) {
          // 更新消息状态
          const index = this.messages.findIndex(m => m.messageId === messageId)
          if (index !== -1) {
            this.messages[index].isWithdrawn = '1'
          }

          // 通过WebSocket发送撤回通知
          this.sendWebSocketMessage({
            type: 'withdraw',
            messageId
          })

          return true
        }
        return false
      } catch (error) {
        console.error('撤回消息失败', error)
        return false
      }
    },

    // 标记消息为已读
    async markMessageAsRead(messageId) {
      try {
        // 通过API标记消息已读
        await markMessageRead(messageId)

        // 通过WebSocket通知对方
        if (socket && socket.readyState === WebSocket.OPEN) {
          const readMessage = {
            type: 'read',
            messageId,
            conversationId: this.currentConversationId,
            isUser: this.userRole === 'user'
          }
          socket.send(JSON.stringify(readMessage))
        }

        return true
      } catch (error) {
        console.error('标记消息已读失败', error)
        return false
      }
    },

    // 标记会话已读的工具方法，避免重复代码
    async markConversationRead(conversationId) {
      if (!conversationId) return;

      try {
        const isUser = this.userRole === 'user';
        console.log('标记会话已读:', conversationId, '是否用户:', isUser);

        // 使用防抖动，避免短时间内多次调用
        if (this._markReadTimeout) {
          clearTimeout(this._markReadTimeout);
        }

        this._markReadTimeout = setTimeout(async () => {
          try {
            await markSessionRead(conversationId, isUser);
            console.log('标记会话已读成功');
          } catch (error) {
            console.error('标记会话已读失败', error);
          }
        }, 300);
      } catch (error) {
        console.error('标记会话已读处理失败', error);
      }
    }
  }
})
