<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="问题内容" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入问题内容" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['wechat:match:question:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['wechat:match:question:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['wechat:match:question:remove']">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="questionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="问题编号" align="center" prop="id" width="100" />
      <el-table-column label="问题内容" align="center" prop="title" :show-overflow-tooltip="true" min-width="200" />
      <el-table-column label="问题类型" align="center" prop="type" width="100">
        <template #default="scope">
          <dict-tag :options="typeOptions" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-switch v-model="scope.row.status" :active-value="'0'" :inactive-value="'1'"
            @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['wechat:match:question:edit']"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
              v-hasPermi="['wechat:match:question:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改问题对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="questionRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="问题内容" prop="title">
          <el-input v-model="form.title" placeholder="请输入问题内容" />
        </el-form-item>
        <el-form-item label="问题类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择问题类型">
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="form.sort" type="number" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选项列表">
          <el-table :data="form.options" :show-header="true">
            <el-table-column type="index" width="50" label="序号" />
            <el-table-column prop="optionText" label="选项内容">
              <template #default="scope">
                <el-input v-model="scope.row.optionText" placeholder="请输入选项内容" />
              </template>
            </el-table-column>
            <el-table-column prop="valueCode" label="选项值" width="100">
              <template #default="scope">
                <el-input v-model="scope.row.valueCode" placeholder="选项值" />
              </template>
            </el-table-column>
            <el-table-column prop="tagType" label="标签类型" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.tagType" placeholder="标签类型" />
              </template>
            </el-table-column>
            <el-table-column prop="recommendTag" label="推荐标签" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.recommendTag" placeholder="推荐标签" />
              </template>
            </el-table-column>
            <el-table-column prop="sort" label="排序" width="80">
              <template #default="scope">
                <el-input v-model="scope.row.sort" type="number" placeholder="排序" />
              </template>
            </el-table-column>
            <el-table-column label="关联咨询师" width="120" v-if="!isExpertiseQuestion(form.id)">
              <template #default="scope">
                <el-button v-if="!isExpertiseQuestion(form.id)" link type="primary"
                  @click="handleSelectConsultants(scope.row)">选择咨询师</el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template #default="scope">
                <el-button type="danger" link icon="Delete" @click="handleDeleteOption(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 10px;">
            <el-button type="primary" @click="handleAddOption">添加选项</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选择咨询师对话框 -->
    <el-dialog title="选择咨询师" v-model="consultantDialogVisible" width="800px" append-to-body>
      <el-transfer v-model="selectedConsultantIds" :data="consultantList" :titles="['可选咨询师', '已选咨询师']" :props="{
        key: 'id',
        label: 'name'
      }" filterable :filter-method="filterMethod" filter-placeholder="请输入咨询师姓名" class="consultant-transfer" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmSelectConsultants">确 定</el-button>
          <el-button @click="consultantDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MatchQuestion">
import { listQuestion, getQuestion, delQuestion, addQuestion, updateQuestion, getOptionConsultants, updateOptionConsultants } from "@/api/wechat/match";
import { listAllSimpleConsultants } from "@/api/wechat/consultation/consultant";
import { ref, getCurrentInstance, onMounted } from 'vue'

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

// 问题类型选项
const typeOptions = [
  { label: '单选', value: 'radio' },
  { label: '多选', value: 'checkbox' },
  { label: '文本', value: 'text' }
];

const questionList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const title = ref("");

// 咨询师选择相关
const consultantDialogVisible = ref(false);
const consultantLoading = ref(false);
const consultantList = ref([]);
const selectedConsultantIds = ref([]);
const currentOption = ref(null);

// 查询参数
const queryParams = ref({
  title: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  title: "",
  type: "radio",
  sort: 0,
  parentId: 0,
  isRequired: "0",
  status: "0",
  options: []
});

// 过滤方法
const filterMethod = (query, item) => {
  return item.name.indexOf(query) > -1;
};

// 判断是否为咨询领域问题（ID为6或7）
function isExpertiseQuestion(id) {
  return id === 6 || id === 7;
}

// 表单校验规则
const rules = {
  title: [
    { required: true, message: "问题内容不能为空", trigger: "blur" }
  ],
  type: [
    { required: true, message: "问题类型不能为空", trigger: "change" }
  ]
};

/** 查询问题列表 */
function getList() {
  loading.value = true;
  listQuestion(queryParams.value).then(response => {
    questionList.value = response.data;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    title: "",
    type: "radio",
    sort: 0,
    parentId: 0,
    isRequired: "0",
    status: "0",
    options: []
  };
  proxy.resetForm("questionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加极速匹配问题";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const questionId = row.id || ids.value[0];
  getQuestion(questionId).then(response => {
    form.value = response.data;
    // 确保每个选项都有必要的字段
    if (form.value.options) {
      form.value.options.forEach(option => {
        if (!option.hasOwnProperty('consultantsChanged')) {
          option.consultantsChanged = false;
        }
        // 如果后端返回的是consultants数组，提取ID到consultantIds
        if (option.consultants && !option.consultantIds) {
          option.consultantIds = option.consultants.map(c => c.id || c);
        }
        // 确保consultantIds字段存在
        if (!option.consultantIds) {
          option.consultantIds = [];
        }
      });
    }
    open.value = true;
    title.value = "修改极速匹配问题";
  });
}

/** 添加选项 */
function handleAddOption() {
  form.value.options.push({
    optionText: "",
    valueCode: "",
    tagType: "",
    recommendTag: "",
    sort: form.value.options.length + 1,
    consultants: [],
    consultantIds: [],
    consultantsChanged: false
  });
}

/** 删除选项 */
function handleDeleteOption(index) {
  form.value.options.splice(index, 1);
  // 重新排序
  form.value.options.forEach((item, index) => {
    item.sort = index + 1;
  });
}

/** 选择咨询师按钮操作 */
function handleSelectConsultants(row) {
  currentOption.value = row;
  consultantLoading.value = true;

  // 并行请求咨询师列表和当前选项已关联的咨询师
  Promise.all([
    listAllSimpleConsultants(),
    row.id ? getOptionConsultants(row.id) : Promise.resolve([])
  ]).then(([consultantsResponse, consultantIds]) => {
    consultantList.value = consultantsResponse.data;

    // 设置已选择的咨询师ID，优先使用本地的consultantIds，其次使用接口返回的数据
    if (row.consultantIds && row.consultantIds.length > 0) {
      selectedConsultantIds.value = [...row.consultantIds];
    } else {
      selectedConsultantIds.value = consultantIds.data || [];
    }

    consultantLoading.value = false;
    consultantDialogVisible.value = true;
  }).catch(() => {
    consultantLoading.value = false;
    proxy.$modal.msgError("获取咨询师数据失败");
  });
}

/** 确认选择咨询师 */
function confirmSelectConsultants() {
  if (currentOption.value) {
    // 保存选中的咨询师ID数组（后端接收consultantIds）
    currentOption.value.consultantIds = [...selectedConsultantIds.value];

    // 同时保存完整的咨询师信息用于前端显示
    currentOption.value.consultants = selectedConsultantIds.value.map(id => {
      return consultantList.value.find(c => c.id === id);
    }).filter(Boolean);

    // 标记该选项的咨询师已被修改，但不立即调用接口
    currentOption.value.consultantsChanged = true;
  }
  consultantDialogVisible.value = false;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["questionRef"].validate(valid => {
    if (valid) {
      if (form.value.id) {
        updateQuestion(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
          // 更新选项关联的咨询师（只更新被修改过的选项）
          if (!isExpertiseQuestion(form.value.id)) {
            form.value.options.forEach(option => {
              if (option.id && option.consultantsChanged) {
                const consultantIds = option.consultantIds || [];
                updateOptionConsultants(option.id, consultantIds);
              }
            });
          }
        });
      } else {
        addQuestion(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
          // 新增后更新选项关联的咨询师（只更新有咨询师的选项）
          if (response.data && !isExpertiseQuestion(response.data.id)) {
            form.value.options.forEach((option, index) => {
              if (option.consultantIds && option.consultantIds.length > 0 && response.data.options && response.data.options[index]) {
                updateOptionConsultants(response.data.options[index].id, option.consultantIds);
              }
            });
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const questionIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除极速匹配问题编号为"' + questionIds + '"的数据项？').then(function () {
    return delQuestion(questionIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 修改状态 */
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.title + '"问题吗?').then(function () {
    return updateQuestion(row);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(() => {
    row.status = row.status === "0" ? "1" : "0";
  });
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.dialog-footer {
  padding: 20px;
  text-align: right;
}

.consultant-transfer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

:deep(.el-transfer) {
  width: 100%;
}

:deep(.el-transfer__buttons) {
  padding: 0 30px;
}

:deep(.el-transfer-panel) {
  width: 300px !important;
}

:deep(.el-transfer-panel__body) {
  height: 400px;
}

:deep(.el-transfer-panel__list.is-filterable) {
  height: 350px;
}
</style>
