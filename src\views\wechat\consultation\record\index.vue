<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="咨询师" prop="consultantName">
        <el-input v-model="queryParams.consultantName" placeholder="请输入咨询师名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="咨询状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择咨询状态" clearable style="width: 240px">
          <el-option label="待开始" value="0" />
          <el-option label="进行中" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="已中断" value="3" />
          <el-option label="已取消" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="咨询类型" prop="consultType">
        <el-select v-model="queryParams.consultType" placeholder="请选择咨询类型" clearable style="width: 240px">
          <el-option label="在线咨询" value="online" />
          <el-option label="电话咨询" value="phone" />
          <el-option label="面对面咨询" value="offline" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:consultationRecord:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:consultationRecord:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:consultationRecord:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:consultationRecord:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="id" width="80" />
      <el-table-column label="用户名称" align="center" prop="userName" />
      <el-table-column label="咨询师" align="center" prop="consultantName" />
      <el-table-column label="咨询类型" align="center" prop="consultType" width="100">
        <template #default="scope">
          <dict-tag :options="sys_consult_type" :value="scope.row.consultType" />
        </template>
      </el-table-column>
      <el-table-column label="咨询状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_consult_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="咨询时长" align="center" prop="duration" width="100">
        <template #default="scope">
          <span>{{ scope.row.duration }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="用户评分" align="center" prop="userRating" width="120">
        <template #default="scope">
          <el-rate v-model="scope.row.userRating" disabled show-score text-color="#ff9900"
            v-if="scope.row.userRating" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:consultationRecord:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:consultationRecord:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:consultationRecord:remove']">删除</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="start" v-if="scope.row.status == 0" icon="VideoPlay">开始咨询
                </el-dropdown-item>
                <el-dropdown-item command="end" v-if="scope.row.status == 1" icon="VideoStop">结束咨询
                </el-dropdown-item>
                <el-dropdown-item command="interrupt" v-if="scope.row.status == 1" icon="VideoPause">中断咨询
                </el-dropdown-item>
                <el-dropdown-item command="resume" v-if="scope.row.status == 3" icon="VideoPlay">恢复咨询
                </el-dropdown-item>
                <el-dropdown-item command="rate" v-if="scope.row.status == 2 && !scope.row.userRating"
                  icon="Star">用户评价</el-dropdown-item>
                <el-dropdown-item command="interruptions" icon="List">中断记录</el-dropdown-item>
                <el-dropdown-item command="statistics" icon="DataAnalysis">统计信息</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改咨询记录对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="recordRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户ID" prop="userId">
              <el-input-number v-model="form.userId" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="咨询师ID" prop="consultantId">
              <el-input-number v-model="form.consultantId" :min="1" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单ID" prop="orderId">
              <el-input-number v-model="form.orderId" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="咨询类型" prop="consultType">
              <el-select v-model="form.consultType" placeholder="请选择咨询类型" style="width: 100%">
                <el-option label="在线咨询" value="online" />
                <el-option label="电话咨询" value="phone" />
                <el-option label="面对面咨询" value="offline" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker v-model="form.startTime" type="datetime" placeholder="选择开始时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker v-model="form.endTime" type="datetime" placeholder="选择结束时间" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="咨询内容" prop="consultContent">
              <el-input v-model="form.consultContent" type="textarea" :rows="4" placeholder="请输入咨询内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 咨询记录详情对话框 -->
    <el-dialog title="咨询记录详情" v-model="detailOpen" width="1000px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ detailForm.id }}</el-descriptions-item>
        <el-descriptions-item label="订单ID">{{ detailForm.orderId }}</el-descriptions-item>
        <el-descriptions-item label="用户名称">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="咨询师">{{ detailForm.consultantName }}</el-descriptions-item>
        <el-descriptions-item label="咨询类型">
          <dict-tag :options="sys_consult_type" :value="detailForm.consultType" />
        </el-descriptions-item>
        <el-descriptions-item label="咨询状态">
          <dict-tag :options="sys_consult_status" :value="detailForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="实际开始时间">{{ parseTime(detailForm.actualStartTime) }}</el-descriptions-item>
        <el-descriptions-item label="实际结束时间">{{ parseTime(detailForm.actualEndTime) }}</el-descriptions-item>
        <el-descriptions-item label="咨询时长">{{ detailForm.duration }}分钟</el-descriptions-item>
        <el-descriptions-item label="用户评分" :span="2">
          <el-rate v-model="detailForm.userRating" disabled show-score text-color="#ff9900"
            v-if="detailForm.userRating" />
          <span v-else>未评分</span>
        </el-descriptions-item>
        <el-descriptions-item label="咨询内容" :span="2">{{ detailForm.consultContent }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="ConsultationRecord">
import {
  listConsultationRecord, getConsultationRecord, delConsultationRecord, addConsultationRecord, updateConsultationRecord, exportConsultationRecord,
  startConsultation, endConsultation, interruptConsultation, resumeConsultation, rateConsultation,
  getUserConsultationStats, getConsultantConsultationStats, getConsultationHistory
} from "@/api/wechat/consultation/record";

const { proxy } = getCurrentInstance();
const { sys_consult_type, sys_consult_status } = proxy.useDict('sys_consult_type', 'sys_consult_status');

const recordList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: null,
    consultantName: null,
    status: null,
    consultType: null
  },
  rules: {
    userId: [
      { required: true, message: "用户ID不能为空", trigger: "blur" }
    ],
    consultantId: [
      { required: true, message: "咨询师ID不能为空", trigger: "blur" }
    ],
    orderId: [
      { required: true, message: "订单ID不能为空", trigger: "blur" }
    ],
    consultType: [
      { required: true, message: "咨询类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, detailForm, rules } = toRefs(data);

/** 查询咨询记录列表 */
function getList() {
  loading.value = true;
  listConsultationRecord(queryParams.value).then(response => {
    recordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: null,
    consultantId: null,
    orderId: null,
    consultType: null,
    startTime: null,
    endTime: null,
    actualStartTime: null,
    actualEndTime: null,
    duration: null,
    mainSymptoms: null,
    consultContent: null,
    consultCount: null,
    userRating: null,
    attachment: null,
    remark: null
  };
  proxy.resetForm("recordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加咨询记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getConsultationRecord(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改咨询记录";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getConsultationRecord(_id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["recordRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateConsultationRecord(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addConsultationRecord(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除咨询记录编号为"' + _ids + '"的数据项？').then(function () {
    return delConsultationRecord(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/consultationRecord/export', {
    ...queryParams.value
  }, `consultation_record_${new Date().getTime()}.xlsx`)
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'start':
      handleStartConsultation(row);
      break;
    case 'end':
      handleEndConsultation(row);
      break;
    case 'interrupt':
      handleInterruptConsultation(row);
      break;
    case 'resume':
      handleResumeConsultation(row);
      break;
    case 'rate':
      handleRateConsultation(row);
      break;
    case 'interruptions':
      handleViewInterruptions(row);
      break;
    case 'statistics':
      handleViewStatistics(row);
      break;
  }
}

/** 开始咨询 */
function handleStartConsultation(row) {
  proxy.$modal.confirm('是否确认开始咨询"' + row.id + '"？').then(function () {
    return startConsultation(row.orderId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("咨询已开始");
  }).catch(() => { });
}

/** 结束咨询 */
function handleEndConsultation(row) {
  proxy.$prompt('请输入咨询总结内容', '结束咨询', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea'
  }).then(({ value }) => {
    return endConsultation(row.id, value);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("咨询已结束");
  }).catch(() => { });
}

/** 中断咨询 */
function handleInterruptConsultation(row) {
  proxy.$prompt('请输入中断原因', '中断咨询', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea'
  }).then(({ value }) => {
    return interruptConsultation(row.id, 'manual', value);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("咨询已中断");
  }).catch(() => { });
}

/** 恢复咨询 */
function handleResumeConsultation(row) {
  proxy.$modal.confirm('是否确认恢复咨询"' + row.id + '"？').then(function () {
    return resumeConsultation(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("咨询已恢复");
  }).catch(() => { });
}

/** 用户评价 */
function handleRateConsultation(row) {
  proxy.$prompt('请输入评分（1-5分）', '用户评价', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[1-5]$/,
    inputErrorMessage: '评分必须是1-5之间的整数'
  }).then(({ value }) => {
    return rateConsultation(row.id, parseInt(value));
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("评价成功");
  }).catch(() => { });
}

/** 查看中断记录 */
function handleViewInterruptions(row) {
  // 这里可以跳转到中断记录页面或打开对话框
  proxy.$modal.msgInfo("查看中断记录功能待实现");
}

/** 查看统计信息 */
function handleViewStatistics(row) {
  // 这里可以显示统计信息
  proxy.$modal.msgInfo("查看统计信息功能待实现");
}

onMounted(() => {
  getList();
});
</script>
