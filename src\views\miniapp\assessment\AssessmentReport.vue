<template>
  <div class="assessment-report">
    <div class="report-header">
      <h1>{{ reportData.scaleName }} - 测评报告</h1>
      <div class="report-meta">
        <span>测评时间：{{ formatDate(reportData.completionTime) }}</span>
        <span>测评耗时：{{ formatDuration(reportData.duration) }}</span>
      </div>
    </div>

    <!-- 总体得分 -->
    <div class="score-section">
      <div class="total-score">
        <div class="score-circle">
          <el-progress
            type="circle"
            :percentage="scorePercentage"
            :width="120"
            :stroke-width="8"
            :color="scoreColor"
          >
            <template #default="{ percentage }">
              <span class="score-text">{{ reportData.totalScore }}</span>
            </template>
          </el-progress>
        </div>
        <div class="score-info">
          <h2>{{ reportData.resultLevel }}</h2>
          <p class="score-description">{{ reportData.resultDescription }}</p>
        </div>
      </div>
    </div>

    <!-- 分量表得分 -->
    <div class="subscale-section" v-if="reportData.subscaleScores?.length">
      <h3>各维度得分</h3>
      <div class="subscale-grid">
        <div 
          v-for="subscale in reportData.subscaleScores" 
          :key="subscale.name"
          class="subscale-item"
        >
          <div class="subscale-header">
            <span class="subscale-name">{{ subscale.name }}</span>
            <span class="subscale-score">{{ subscale.score }}</span>
          </div>
          <el-progress 
            :percentage="getSubscalePercentage(subscale)" 
            :color="getSubscaleColor(subscale)"
            :show-text="false"
          />
          <p class="subscale-description">{{ subscale.description }}</p>
        </div>
      </div>
    </div>

    <!-- 详细分析 -->
    <div class="analysis-section" v-if="reportData.detailedAnalysis">
      <h3>详细分析</h3>
      <div class="analysis-content" v-html="reportData.detailedAnalysis"></div>
    </div>

    <!-- 建议与指导 -->
    <div class="suggestions-section" v-if="reportData.suggestions?.length">
      <h3>建议与指导</h3>
      <div class="suggestions-list">
        <div 
          v-for="(suggestion, index) in reportData.suggestions" 
          :key="index"
          class="suggestion-item"
        >
          <div class="suggestion-header">
            <el-icon class="suggestion-icon"><Lightbulb /></el-icon>
            <h4>{{ suggestion.title }}</h4>
          </div>
          <p class="suggestion-content">{{ suggestion.content }}</p>
        </div>
      </div>
    </div>

    <!-- 相关资源推荐 -->
    <div class="resources-section" v-if="reportData.recommendedResources?.length">
      <h3>相关资源推荐</h3>
      <div class="resources-grid">
        <div 
          v-for="resource in reportData.recommendedResources" 
          :key="resource.id"
          class="resource-item"
          @click="openResource(resource)"
        >
          <img :src="resource.coverImage" :alt="resource.title" />
          <div class="resource-info">
            <h4>{{ resource.title }}</h4>
            <p>{{ resource.description }}</p>
            <el-tag :type="getResourceType(resource.type)">{{ resource.typeName }}</el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="report-actions">
      <el-button type="primary" @click="downloadReport">
        <el-icon><Download /></el-icon>
        下载报告
      </el-button>
      <el-button @click="shareReport">
        <el-icon><Share /></el-icon>
        分享报告
      </el-button>
      <el-button @click="retakeAssessment">
        <el-icon><Refresh /></el-icon>
        重新测评
      </el-button>
      <el-button @click="backToRecords">
        返回记录
      </el-button>
    </div>

    <!-- 升级提示 -->
    <div class="upgrade-tip" v-if="showUpgradeTip">
      <div class="tip-content">
        <el-icon class="tip-icon"><Star /></el-icon>
        <div class="tip-text">
          <h4>升级到专业版报告</h4>
          <p>获得更详细的分析和个性化建议</p>
        </div>
        <el-button type="primary" @click="upgradeReport">立即升级</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Lightbulb, Download, Share, Refresh, Star } from '@element-plus/icons-vue'
import { generateReport, getAssessmentSuggestions } from '@/api/miniapp/assessment'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const reportData = ref({
  scaleName: '',
  completionTime: '',
  duration: 0,
  totalScore: 0,
  maxScore: 100,
  resultLevel: '',
  resultDescription: '',
  subscaleScores: [],
  detailedAnalysis: '',
  suggestions: [],
  recommendedResources: [],
  reportLevel: 1
})

// 计算属性
const scorePercentage = computed(() => {
  return Math.round((reportData.value.totalScore / reportData.value.maxScore) * 100)
})

const scoreColor = computed(() => {
  const percentage = scorePercentage.value
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  if (percentage >= 40) return '#f56c6c'
  return '#909399'
})

const showUpgradeTip = computed(() => {
  return reportData.value.reportLevel === 1 && reportData.value.maxReportLevel > 1
})

// 方法
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatDuration = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟${secs}秒`
  }
  return `${minutes}分钟${secs}秒`
}

const getSubscalePercentage = (subscale) => {
  return Math.round((subscale.score / subscale.maxScore) * 100)
}

const getSubscaleColor = (subscale) => {
  const percentage = getSubscalePercentage(subscale)
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  if (percentage >= 40) return '#f56c6c'
  return '#909399'
}

const getResourceType = (type) => {
  const typeMap = {
    'course': 'success',
    'meditation': 'primary',
    'article': 'info',
    'video': 'warning'
  }
  return typeMap[type] || 'info'
}

const loadReport = async () => {
  try {
    loading.value = true
    const recordId = route.params.recordId
    const response = await generateReport(recordId, 1)
    reportData.value = response.data
    
    // 加载建议
    const suggestionsResponse = await getAssessmentSuggestions(recordId)
    reportData.value.suggestions = suggestionsResponse.data
  } catch (error) {
    ElMessage.error('加载报告失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const downloadReport = async () => {
  try {
    // 实现报告下载功能
    ElMessage.success('报告下载中...')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const shareReport = async () => {
  try {
    // 实现报告分享功能
    await navigator.share({
      title: `${reportData.value.scaleName} - 测评报告`,
      text: `我完成了${reportData.value.scaleName}测评，得分${reportData.value.totalScore}分`,
      url: window.location.href
    })
  } catch (error) {
    // 如果不支持原生分享，使用复制链接
    await navigator.clipboard.writeText(window.location.href)
    ElMessage.success('报告链接已复制到剪贴板')
  }
}

const retakeAssessment = async () => {
  try {
    await ElMessageBox.confirm('确定要重新进行测评吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    router.push(`/miniapp-assessment/flow/${reportData.value.scaleId}`)
  } catch (error) {
    // 用户取消
  }
}

const backToRecords = () => {
  router.push('/miniapp-assessment/records')
}

const upgradeReport = async () => {
  try {
    const recordId = route.params.recordId
    const response = await generateReport(recordId, reportData.value.maxReportLevel)
    reportData.value = response.data
    ElMessage.success('报告已升级')
  } catch (error) {
    ElMessage.error('升级失败')
  }
}

const openResource = (resource) => {
  // 根据资源类型跳转到相应页面
  switch (resource.type) {
    case 'course':
      router.push(`/courses/${resource.id}`)
      break
    case 'meditation':
      router.push(`/meditation/${resource.id}`)
      break
    case 'article':
      window.open(resource.url, '_blank')
      break
    default:
      window.open(resource.url, '_blank')
  }
}

// 生命周期
onMounted(() => {
  loadReport()
})
</script>

<style scoped>
.assessment-report {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: white;
}

.report-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.report-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.report-meta {
  display: flex;
  justify-content: center;
  gap: 20px;
  color: #666;
  font-size: 14px;
}

.score-section {
  margin-bottom: 30px;
}

.total-score {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
}

.score-text {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.score-info h2 {
  font-size: 28px;
  margin-bottom: 10px;
  color: #409eff;
}

.score-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
}

.subscale-section,
.analysis-section,
.suggestions-section,
.resources-section {
  margin-bottom: 30px;
}

.subscale-section h3,
.analysis-section h3,
.suggestions-section h3,
.resources-section h3 {
  margin-bottom: 20px;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.subscale-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.subscale-item {
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fafafa;
}

.subscale-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.subscale-name {
  font-weight: bold;
  color: #303133;
}

.subscale-score {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.subscale-description {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.analysis-content {
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
  line-height: 1.8;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.suggestion-icon {
  color: #e6a23c;
  font-size: 18px;
}

.suggestion-header h4 {
  margin: 0;
  color: #303133;
}

.suggestion-content {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.resource-item {
  display: flex;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.resource-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.resource-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 12px;
}

.resource-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.resource-info p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.report-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin: 30px 0;
  flex-wrap: wrap;
}

.upgrade-tip {
  margin-top: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.tip-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.tip-icon {
  font-size: 24px;
  color: #ffd700;
}

.tip-text {
  flex: 1;
}

.tip-text h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
}

.tip-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .total-score {
    flex-direction: column;
    text-align: center;
  }
  
  .report-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .tip-content {
    flex-direction: column;
    text-align: center;
  }
}
</style>
