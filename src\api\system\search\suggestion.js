import request from '@/utils/request'

// 查询搜索建议列表
export function listSearchSuggestion(query) {
  return request({
    url: '/system/search/suggestion/list',
    method: 'get',
    params: query
  })
}

// 查询搜索建议详细
export function getSearchSuggestion(id) {
  return request({
    url: '/system/search/suggestion/' + id,
    method: 'get'
  })
}

// 新增搜索建议
export function addSearchSuggestion(data) {
  return request({
    url: '/system/search/suggestion',
    method: 'post',
    data: data
  })
}

// 修改搜索建议
export function updateSearchSuggestion(data) {
  return request({
    url: '/system/search/suggestion',
    method: 'put',
    data: data
  })
}

// 删除搜索建议
export function delSearchSuggestion(ids) {
  return request({
    url: '/system/search/suggestion/' + ids,
    method: 'delete'
  })
}

// 导出搜索建议
export function exportSearchSuggestion(query) {
  return request({
    url: '/system/search/suggestion/export',
    method: 'post',
    params: query
  })
}
