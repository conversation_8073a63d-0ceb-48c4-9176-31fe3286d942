import request from '@/utils/request'

// 查询咨询师评价列表
export function listConsultantReview(query) {
  return request({
    url: '/system/consultantReview/list',
    method: 'get',
    params: query
  })
}

// 导出咨询师评价列表
export function exportConsultantReview(query) {
  return request({
    url: '/system/consultantReview/export',
    method: 'post',
    params: query
  })
}

// 获取咨询师评价详细信息
export function getConsultantReview(id) {
  return request({
    url: `/system/consultantReview/${id}`,
    method: 'get'
  })
}

// 获取咨询师评价详细信息（包含关联信息）
export function getConsultantReviewDetails(id) {
  return request({
    url: `/system/consultantReview/details/${id}`,
    method: 'get'
  })
}

// 新增咨询师评价
export function addConsultantReview(data) {
  return request({
    url: '/system/consultantReview',
    method: 'post',
    data: data
  })
}

// 修改咨询师评价
export function updateConsultantReview(data) {
  return request({
    url: '/system/consultantReview',
    method: 'put',
    data: data
  })
}

// 删除咨询师评价
export function delConsultantReview(ids) {
  return request({
    url: `/system/consultantReview/${ids}`,
    method: 'delete'
  })
}

// 根据咨询师ID查询评价列表
export function getConsultantReviewsByConsultant(consultantId) {
  return request({
    url: `/system/consultantReview/consultant/${consultantId}`,
    method: 'get'
  })
}

// 根据用户ID查询评价列表
export function getConsultantReviewsByUser(userId) {
  return request({
    url: `/system/consultantReview/user/${userId}`,
    method: 'get'
  })
}

// 根据咨询记录ID查询评价
export function getConsultantReviewByRecord(recordId) {
  return request({
    url: `/system/consultantReview/record/${recordId}`,
    method: 'get'
  })
}

// 审核评价
export function auditConsultantReview(id, adminCheck) {
  return request({
    url: `/system/consultantReview/audit/${id}`,
    method: 'post',
    params: {
      adminCheck: adminCheck
    }
  })
}

// 咨询师回复评价
export function replyConsultantReview(id, consultantReply) {
  return request({
    url: `/system/consultantReview/reply/${id}`,
    method: 'post',
    params: {
      consultantReply: consultantReply
    }
  })
}

// 查询待审核评价列表
export function getPendingConsultantReviews() {
  return request({
    url: '/system/consultantReview/pending',
    method: 'get'
  })
}

// 查询已通过审核的评价列表
export function getApprovedConsultantReviews(consultantId) {
  return request({
    url: `/system/consultantReview/approved/${consultantId}`,
    method: 'get'
  })
}

// 获取咨询师评价统计信息
export function getConsultantReviewStatistics(consultantId) {
  return request({
    url: `/system/consultantReview/statistics/${consultantId}`,
    method: 'get'
  })
}

// 更新咨询师评分信息
export function updateConsultantRating(consultantId) {
  return request({
    url: `/system/consultantReview/updateRating/${consultantId}`,
    method: 'post'
  })
}
