import request from '@/utils/request'

// 查询章节列表
export function listChapter(query) {
  return request({
    url: '/system/chapter/list',
    method: 'get',
    params: query
  })
}

// 查询所有章节列表（用于构造树形数据）
export function listAllChapter() {
  return request({
    url: '/system/chapter/all',
    method: 'get'
  })
}

// 导出章节列表
export function exportChapter(query) {
  return request({
    url: '/system/chapter/export',
    method: 'post',
    params: query
  })
}

// 获取章节详细信息
export function getChapter(id) {
  return request({
    url: `/system/chapter/${id}`,
    method: 'get'
  })
}

// 新增章节
export function addChapter(data) {
  return request({
    url: '/system/chapter',
    method: 'post',
    data: data
  })
}

// 修改章节
export function updateChapter(data) {
  return request({
    url: '/system/chapter',
    method: 'put',
    data: data
  })
}

// 删除章节
export function delChapter(ids) {
  return request({
    url: `/system/chapter/${ids}`,
    method: 'delete'
  })
}

// 根据课程ID查询章节列表
export function getChaptersByCourse(courseId) {
  return request({
    url: `/system/chapter/course/${courseId}`,
    method: 'get'
  })
}

// 根据课程ID查询章节树结构
export function getChapterTree(courseId) {
  return request({
    url: `/system/chapter/tree/${courseId}`,
    method: 'get'
  })
}

// 更新章节排序
export function updateChapterOrder(id, order) {
  return request({
    url: `/system/chapter/order/${id}/${order}`,
    method: 'put'
  })
}
