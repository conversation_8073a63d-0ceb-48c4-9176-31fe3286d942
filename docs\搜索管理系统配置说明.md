# 搜索管理系统配置说明

## 概述
已为PC端后台管理系统创建了完整的搜索管理系统功能，包括以下模块：

1. **搜索记录管理** - 用户搜索记录的查询、统计、导出和删除功能
2. **热门搜索管理** - 热门搜索关键词的增删改查、状态管理和导出功能
3. **搜索建议管理** - 搜索建议词的管理、优先级设置和类型管理

## 已创建的文件

### 前端API文件
```
src/api/system/search/record.js       # 搜索记录API
src/api/system/search/hot.js          # 热门搜索API
src/api/system/search/suggestion.js   # 搜索建议API
```

### 前端页面文件
```
src/views/system/search/record/index.vue       # 搜索记录管理
src/views/system/search/hot/index.vue          # 热门搜索管理
src/views/system/search/suggestion/index.vue   # 搜索建议管理
```

## 需要在后端添加的菜单配置

由于项目使用动态路由，需要在后端数据库的菜单表中添加以下菜单配置：

### 1. 搜索管理系统主菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('搜索管理', 0, 8, 'search-system', NULL, 'M', '0', '0', NULL, 'search', 'admin', NOW(), '', NULL, '搜索管理系统');
```

### 2. 搜索记录管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索记录', [搜索管理菜单ID], 1, 'search-record', 'system/search/record/index', 'C', '0', '0', 'search:record:list', 'documentation', 'admin', NOW(), '', NULL, '搜索记录管理');
```

### 3. 搜索记录管理按钮权限
```sql
-- 搜索记录查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索记录查询', [搜索记录菜单ID], 1, '', '', 'F', '0', '0', 'search:record:query', '#', 'admin', NOW(), '', NULL, '');

-- 搜索记录新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索记录新增', [搜索记录菜单ID], 2, '', '', 'F', '0', '0', 'search:record:add', '#', 'admin', NOW(), '', NULL, '');

-- 搜索记录修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索记录修改', [搜索记录菜单ID], 3, '', '', 'F', '0', '0', 'search:record:edit', '#', 'admin', NOW(), '', NULL, '');

-- 搜索记录删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索记录删除', [搜索记录菜单ID], 4, '', '', 'F', '0', '0', 'search:record:remove', '#', 'admin', NOW(), '', NULL, '');

-- 搜索记录导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索记录导出', [搜索记录菜单ID], 5, '', '', 'F', '0', '0', 'search:record:export', '#', 'admin', NOW(), '', NULL, '');
```

### 4. 热门搜索管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('热门搜索', [搜索管理菜单ID], 2, 'hot-search', 'system/search/hot/index', 'C', '0', '0', 'search:hot:list', 'star', 'admin', NOW(), '', NULL, '热门搜索管理');
```

### 5. 热门搜索管理按钮权限
```sql
-- 热门搜索查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('热门搜索查询', [热门搜索菜单ID], 1, '', '', 'F', '0', '0', 'search:hot:query', '#', 'admin', NOW(), '', NULL, '');

-- 热门搜索新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('热门搜索新增', [热门搜索菜单ID], 2, '', '', 'F', '0', '0', 'search:hot:add', '#', 'admin', NOW(), '', NULL, '');

-- 热门搜索修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('热门搜索修改', [热门搜索菜单ID], 3, '', '', 'F', '0', '0', 'search:hot:edit', '#', 'admin', NOW(), '', NULL, '');

-- 热门搜索删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('热门搜索删除', [热门搜索菜单ID], 4, '', '', 'F', '0', '0', 'search:hot:remove', '#', 'admin', NOW(), '', NULL, '');

-- 热门搜索导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('热门搜索导出', [热门搜索菜单ID], 5, '', '', 'F', '0', '0', 'search:hot:export', '#', 'admin', NOW(), '', NULL, '');
```

### 6. 搜索建议管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索建议', [搜索管理菜单ID], 3, 'search-suggestion', 'system/search/suggestion/index', 'C', '0', '0', 'search:suggestion:list', 'guide', 'admin', NOW(), '', NULL, '搜索建议管理');
```

### 7. 搜索建议管理按钮权限
```sql
-- 搜索建议查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索建议查询', [搜索建议菜单ID], 1, '', '', 'F', '0', '0', 'search:suggestion:query', '#', 'admin', NOW(), '', NULL, '');

-- 搜索建议新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索建议新增', [搜索建议菜单ID], 2, '', '', 'F', '0', '0', 'search:suggestion:add', '#', 'admin', NOW(), '', NULL, '');

-- 搜索建议修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索建议修改', [搜索建议菜单ID], 3, '', '', 'F', '0', '0', 'search:suggestion:edit', '#', 'admin', NOW(), '', NULL, '');

-- 搜索建议删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索建议删除', [搜索建议菜单ID], 4, '', '', 'F', '0', '0', 'search:suggestion:remove', '#', 'admin', NOW(), '', NULL, '');

-- 搜索建议导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('搜索建议导出', [搜索建议菜单ID], 5, '', '', 'F', '0', '0', 'search:suggestion:export', '#', 'admin', NOW(), '', NULL, '');
```

## 菜单层级结构

```
搜索管理 (一级菜单)
├── 搜索记录 (二级菜单)
│   ├── 搜索记录查询 (按钮权限)
│   ├── 搜索记录新增 (按钮权限)
│   ├── 搜索记录修改 (按钮权限)
│   ├── 搜索记录删除 (按钮权限)
│   └── 搜索记录导出 (按钮权限)
├── 热门搜索 (二级菜单)
│   ├── 热门搜索查询 (按钮权限)
│   ├── 热门搜索新增 (按钮权限)
│   ├── 热门搜索修改 (按钮权限)
│   ├── 热门搜索删除 (按钮权限)
│   └── 热门搜索导出 (按钮权限)
└── 搜索建议 (二级菜单)
    ├── 搜索建议查询 (按钮权限)
    ├── 搜索建议新增 (按钮权限)
    ├── 搜索建议修改 (按钮权限)
    ├── 搜索建议删除 (按钮权限)
    └── 搜索建议导出 (按钮权限)
```

## 功能特性

### 搜索记录管理
- ✅ 支持按用户ID、关键词、搜索类型、IP地址筛选
- ✅ 支持按时间范围查询
- ✅ 支持新增、修改、删除搜索记录
- ✅ 支持批量删除
- ✅ 支持导出Excel

### 热门搜索管理
- ✅ 支持按关键词、搜索类型、状态筛选
- ✅ 支持新增、修改、删除热门搜索
- ✅ 支持状态管理（启用/停用）
- ✅ 支持热度分数管理
- ✅ 支持批量删除和导出

### 搜索建议管理
- ✅ 支持按关键词、建议类型、状态筛选
- ✅ 支持新增、修改、删除搜索建议
- ✅ 支持优先级管理（数值越大优先级越高）
- ✅ 支持建议类型管理（自动生成/手动添加/热门推荐）
- ✅ 支持状态管理和批量操作

## 添加步骤

1. 在后台管理系统的菜单管理中，按照上述配置添加菜单项
2. 注意替换 `[菜单ID]` 为实际的父菜单ID
3. 为相应的角色分配菜单权限
4. 系统会自动生成动态路由，前端页面即可正常访问

## 注意事项

1. **组件路径**：component字段中的路径对应前端views目录下的组件路径
2. **权限标识**：perms字段要与前端页面中的v-hasPermi指令保持一致
3. **菜单图标**：icon字段使用Element Plus图标名称
4. **父子关系**：添加子菜单时，parentId要设置为父菜单的实际ID
5. **搜索类型**：系统支持全部、咨询师、课程、冥想、抗逆力等搜索类型
6. **数据导出**：所有模块都支持Excel导出功能
