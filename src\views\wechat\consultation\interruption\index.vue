<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="咨询记录ID" prop="recordId">
        <el-input-number v-model="queryParams.recordId" :min="1" placeholder="请输入咨询记录ID" />
      </el-form-item>
      <el-form-item label="中断类型" prop="interruptType">
        <el-select v-model="queryParams.interruptType" placeholder="请选择中断类型" clearable style="width: 240px">
          <el-option label="手动中断" value="manual" />
          <el-option label="网络中断" value="network" />
          <el-option label="系统中断" value="system" />
          <el-option label="用户离开" value="user_leave" />
          <el-option label="咨询师离开" value="consultant_leave" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" type="datetimerange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:consultantInterruption:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:consultantInterruption:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:consultantInterruption:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:consultantInterruption:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="DataAnalysis" @click="handleStatistics"
          v-hasPermi="['system:consultantInterruption:list']">统计分析</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="interruptionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="中断ID" align="center" prop="id" width="80" />
      <el-table-column label="咨询记录ID" align="center" prop="recordId" width="120" />
      <el-table-column label="中断类型" align="center" prop="interruptType" width="120">
        <template #default="scope">
          <dict-tag :options="sys_interrupt_type" :value="scope.row.interruptType" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="中断时长" align="center" prop="duration" width="100">
        <template #default="scope">
          <span>{{ scope.row.duration }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="中断原因" align="center" prop="reason" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:consultantInterruption:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:consultantInterruption:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:consultantInterruption:remove']">删除</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="recordStats" icon="DataAnalysis">记录统计</el-dropdown-item>
                <el-dropdown-item command="typeStats" icon="PieChart">类型统计</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改中断记录对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="interruptionRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="咨询记录ID" prop="recordId">
              <el-input-number v-model="form.recordId" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中断类型" prop="interruptType">
              <el-select v-model="form.interruptType" placeholder="请选择中断类型" style="width: 100%">
                <el-option label="手动中断" value="manual" />
                <el-option label="网络中断" value="network" />
                <el-option label="系统中断" value="system" />
                <el-option label="用户离开" value="user_leave" />
                <el-option label="咨询师离开" value="consultant_leave" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker v-model="form.createTime" type="datetime" placeholder="选择创建时间" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="中断时长" prop="duration">
              <el-input-number v-model="form.duration" :min="0" placeholder="分钟" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="中断原因" prop="reason">
              <el-input v-model="form.reason" type="textarea" :rows="4" placeholder="请输入中断原因" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 中断记录详情对话框 -->
    <el-dialog title="中断记录详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="中断ID">{{ detailForm.id }}</el-descriptions-item>
        <el-descriptions-item label="咨询记录ID">{{ detailForm.recordId }}</el-descriptions-item>
        <el-descriptions-item label="中断类型">
          <dict-tag :options="sys_interrupt_type" :value="detailForm.interruptType" />
        </el-descriptions-item>
        <el-descriptions-item label="中断时长">{{ detailForm.duration }}分钟</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="中断原因" :span="2">{{ detailForm.reason }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog title="中断统计分析" v-model="statisticsOpen" width="800px" append-to-body>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="总体统计" name="overall">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="总中断次数">{{ overallStats.totalCount }}</el-descriptions-item>
            <el-descriptions-item label="总中断时长">{{ overallStats.totalDuration }}分钟</el-descriptions-item>
            <el-descriptions-item label="平均中断时长">{{ overallStats.avgDuration }}分钟</el-descriptions-item>
            <el-descriptions-item label="手动中断">{{ overallStats.manualCount }}</el-descriptions-item>
            <el-descriptions-item label="网络中断">{{ overallStats.networkCount }}</el-descriptions-item>
            <el-descriptions-item label="系统中断">{{ overallStats.systemCount }}</el-descriptions-item>
            <el-descriptions-item label="用户离开">{{ overallStats.userLeaveCount }}</el-descriptions-item>
            <el-descriptions-item label="咨询师离开">{{ overallStats.consultantLeaveCount }}</el-descriptions-item>
            <el-descriptions-item label="其他类型">{{ overallStats.otherCount }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="按记录统计" name="record">
          <el-form :inline="true" class="mb8">
            <el-form-item label="咨询记录ID">
              <el-input-number v-model="recordStatsId" :min="1" placeholder="请输入咨询记录ID" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getRecordStats">查询</el-button>
            </el-form-item>
          </el-form>
          <el-descriptions :column="2" border v-if="recordStats.recordId">
            <el-descriptions-item label="咨询记录ID">{{ recordStats.recordId }}</el-descriptions-item>
            <el-descriptions-item label="中断次数">{{ recordStats.count }}</el-descriptions-item>
            <el-descriptions-item label="总中断时长">{{ recordStats.totalDuration }}分钟</el-descriptions-item>
            <el-descriptions-item label="平均中断时长">{{ recordStats.avgDuration }}分钟</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="按类型统计" name="type">
          <el-form :inline="true" class="mb8">
            <el-form-item label="中断类型">
              <el-select v-model="typeStatsType" placeholder="请选择中断类型">
                <el-option label="手动中断" value="manual" />
                <el-option label="网络中断" value="network" />
                <el-option label="系统中断" value="system" />
                <el-option label="用户离开" value="user_leave" />
                <el-option label="咨询师离开" value="consultant_leave" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getTypeStats">查询</el-button>
            </el-form-item>
          </el-form>
          <el-descriptions :column="2" border v-if="typeStats.type">
            <el-descriptions-item label="中断类型">{{ typeStats.type }}</el-descriptions-item>
            <el-descriptions-item label="中断次数">{{ typeStats.count }}</el-descriptions-item>
            <el-descriptions-item label="占比">{{ typeStats.percentage }}%</el-descriptions-item>
            <el-descriptions-item label="平均时长">{{ typeStats.avgDuration }}分钟</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup name="ConsultantInterruption">
import {
  listConsultantInterruption, getConsultantInterruption, delConsultantInterruption, addConsultantInterruption, updateConsultantInterruption, exportConsultantInterruption,
  getConsultantInterruptionsByRecord, countConsultantInterruptions, sumConsultantInterruptionDuration,
  countConsultantInterruptionsByType, getConsultantInterruptionStats
} from "@/api/wechat/consultation/interruption";

const { proxy } = getCurrentInstance();
const { sys_interrupt_type } = proxy.useDict('sys_interrupt_type');

const interruptionList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const statisticsOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref("overall");
const recordStatsId = ref(null);
const typeStatsType = ref(null);

const data = reactive({
  form: {},
  detailForm: {},
  overallStats: {},
  recordStats: {},
  typeStats: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    recordId: null,
    interruptType: null,
    createTime: null
  },
  rules: {
    recordId: [
      { required: true, message: "咨询记录ID不能为空", trigger: "blur" }
    ],
    interruptType: [
      { required: true, message: "中断类型不能为空", trigger: "change" }
    ],
    reason: [
      { required: true, message: "中断原因不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, overallStats, recordStats, typeStats, rules } = toRefs(data);

/** 查询中断记录列表 */
function getList() {
  loading.value = true;
  listConsultantInterruption(queryParams.value).then(response => {
    interruptionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    recordId: null,
    interruptType: null,
    reason: null,
    duration: null,
    createTime: null
  };
  proxy.resetForm("interruptionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加中断记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getConsultantInterruption(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改中断记录";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getConsultantInterruption(_id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["interruptionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateConsultantInterruption(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addConsultantInterruption(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除中断记录编号为"' + _ids + '"的数据项？').then(function () {
    return delConsultantInterruption(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/consultantInterruption/export', {
    ...queryParams.value
  }, `consultant_interruption_${new Date().getTime()}.xlsx`)
}

/** 统计分析 */
function handleStatistics() {
  // 获取总体统计数据
  getOverallStatistics();
  statisticsOpen.value = true;
}

/** 获取总体统计 */
function getOverallStatistics() {
  // 这里可以调用后端接口获取统计数据
  // 暂时使用模拟数据
  overallStats.value = {
    totalCount: 150,
    totalDuration: 45000,
    avgDuration: 300,
    manualCount: 60,
    networkCount: 40,
    systemCount: 25,
    userLeaveCount: 15,
    consultantLeaveCount: 8,
    otherCount: 2
  };
}

/** 获取记录统计 */
function getRecordStats() {
  if (!recordStatsId.value) {
    proxy.$modal.msgWarning("请输入咨询记录ID");
    return;
  }

  countConsultantInterruptions(recordStatsId.value).then(response => {
    recordStats.value.count = response.data;
    recordStats.value.recordId = recordStatsId.value;
  });

  sumConsultantInterruptionDuration(recordStatsId.value).then(response => {
    recordStats.value.totalDuration = response.data;
    recordStats.value.avgDuration = recordStats.value.count > 0 ?
      Math.round(recordStats.value.totalDuration / recordStats.value.count) : 0;
  });
}

/** 获取类型统计 */
function getTypeStats() {
  if (!typeStatsType.value) {
    proxy.$modal.msgWarning("请选择中断类型");
    return;
  }

  // 这里可以调用后端接口获取类型统计
  // 暂时使用模拟数据
  typeStats.value = {
    type: typeStatsType.value,
    count: 60,
    percentage: 40,
    avgDuration: 280
  };
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'recordStats':
      recordStatsId.value = row.recordId;
      getRecordStats();
      activeTab.value = "record";
      statisticsOpen.value = true;
      break;
    case 'typeStats':
      typeStatsType.value = row.interruptType;
      getTypeStats();
      activeTab.value = "type";
      statisticsOpen.value = true;
      break;
  }
}

onMounted(() => {
  getList();
});
</script>
