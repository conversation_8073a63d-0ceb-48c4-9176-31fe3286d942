<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="咨询师" prop="counselorId">
        <el-select v-model="queryParams.counselorId" placeholder="请选择咨询师" clearable style="width: 200px">
          <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
            :value="counselor.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围" prop="dateRange">
        <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" style="width: 240px" value-format="YYYY-MM-DD" @change="handleDateRangeChange" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable style="width: 120px">
          <el-option label="可用" value="0" />
          <el-option label="已预约" value="1" />
          <el-option label="不可用" value="2" />
          <el-option label="已过期" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="showBatchGenerateDialog"
          v-hasPermi="['system:timeSlot:generate']">批量生成排班</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" @click="showBatchEditDialog"
          v-hasPermi="['system:timeSlot:edit']">批量修改状态</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="View" @click="switchViewMode">
          {{ viewMode === 'table' ? '日历视图' : '表格视图' }}
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getScheduleData"></right-toolbar>
    </el-row>

    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'">
      <el-table v-loading="loading" :data="scheduleList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="咨询师" align="center" width="100">
          <template #default="scope">
            {{ getCounselorName(scope.row.counselorId) }}
          </template>
        </el-table-column>
        <el-table-column label="日期" align="center" prop="slotDate" width="120" />
        <el-table-column label="时间段" align="center" prop="timeRangeName" width="120" />
        <el-table-column label="时间" align="center" width="150">
          <template #default="scope">
            {{ scope.row.startTime }} - {{ scope.row.endTime }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="预约信息" align="center" prop="appointmentInfo" :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="editSlot(scope.row)"
              v-hasPermi="['system:timeSlot:edit']">修改</el-button>
            <el-button link type="info" icon="View" @click="viewAppointment(scope.row)"
              v-if="scope.row.status === 1">查看预约</el-button>
            <el-button link type="danger" icon="Delete" @click="deleteSlot(scope.row)"
              v-hasPermi="['system:timeSlot:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getScheduleData" />
    </div>

    <!-- 日历视图 -->
    <div v-else class="calendar-view">
      <el-calendar v-model="calendarDate" @panel-change="handlePanelChange">
        <template #date-cell="{ data }">
          <div class="calendar-cell">
            <div class="date-number">{{ data.day.split('-').slice(-1)[0] }}</div>
            <div class="slots-container">
              <div v-for="slot in getSlotsByDate(data.day)" :key="slot.id" :class="['slot-item', getSlotClass(slot)]"
                @click="viewSlotDetail(slot)">
                <div class="slot-time">{{ slot.startTime.substring(0, 5) }}</div>
                <div class="slot-counselor">{{ slot.counselorName }}</div>
                <div class="slot-status">{{ getStatusText(slot.status) }}</div>
              </div>
            </div>
          </div>
        </template>
      </el-calendar>
    </div>

    <!-- 批量生成排班对话框 -->
    <el-dialog title="批量生成排班" v-model="batchGenerateOpen" width="500px" append-to-body>
      <el-form ref="batchGenerateRef" :model="batchGenerateForm" :rules="batchGenerateRules" label-width="100px">
        <el-form-item label="咨询师" prop="counselorIds">
          <el-select v-model="batchGenerateForm.counselorIds" multiple placeholder="请选择咨询师（留空为所有）" style="width: 100%">
            <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
              :value="counselor.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围" prop="dateRange">
          <el-date-picker v-model="batchGenerateForm.dateRange" type="daterange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%" value-format="YYYY-MM-DD" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleBatchGenerate">确 定</el-button>
          <el-button @click="batchGenerateOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量修改状态对话框 -->
    <el-dialog title="批量修改状态" v-model="batchEditOpen" width="400px" append-to-body>
      <el-form ref="batchEditRef" :model="batchEditForm" :rules="batchEditRules" label-width="80px">
        <el-form-item label="新状态" prop="status">
          <el-select v-model="batchEditForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="可用" value="0" />
            <el-option label="不可用" value="2" />
          </el-select>
        </el-form-item>
        <p style="color: #666; font-size: 12px;">已选择 {{ selectedSlots.length }} 个时间槽</p>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleBatchEdit">确 定</el-button>
          <el-button @click="batchEditOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 时间槽详情对话框 -->
    <el-dialog title="时间槽详情" v-model="slotDetailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border v-if="currentSlot">
        <el-descriptions-item label="咨询师">{{ currentSlot.counselorName }}</el-descriptions-item>
        <el-descriptions-item label="日期">{{ currentSlot.slotDate }}</el-descriptions-item>
        <el-descriptions-item label="时间段">{{ currentSlot.timeRangeName }}</el-descriptions-item>
        <el-descriptions-item label="时间">{{ currentSlot.startTime }} - {{ currentSlot.endTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentSlot.status)">
            {{ getStatusText(currentSlot.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="是否公开">{{ currentSlot.isPublic ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="预约信息" :span="2">
          {{ currentSlot.appointmentInfo || '暂无预约信息' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ currentSlot.remark || '无' }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="editSlot(currentSlot)" v-hasPermi="['system:timeSlot:edit']">修改</el-button>
          <el-button @click="slotDetailOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Schedule">
import {
  listTimeSlot, updateTimeSlot, delTimeSlot,
  generateSlotsForAll, batchUpdateSlotStatus
} from "@/api/wechat/timeSlot";
import { listConsultant } from "@/api/wechat/consultation/consultant";
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, getCurrentInstance, computed } from 'vue'

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 排班数据
const scheduleList = ref([]);
// 咨询师列表
const counselorList = ref([]);
// 选中的时间槽
const selectedSlots = ref([]);
// 视图模式
const viewMode = ref('table'); // table 或 calendar
// 日历日期
const calendarDate = ref(new Date());
// 日历数据
const calendarData = ref([]);

// 对话框状态
const batchGenerateOpen = ref(false);
const batchEditOpen = ref(false);
const slotDetailOpen = ref(false);
const currentSlot = ref(null);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  counselorId: undefined,
  dateRange: [],
  startDate: undefined,
  endDate: undefined,
  status: undefined
});

// 批量生成表单
const batchGenerateForm = reactive({
  counselorIds: [],
  dateRange: []
});

// 批量修改表单
const batchEditForm = reactive({
  status: undefined
});

// 表单校验
const batchGenerateRules = reactive({
  dateRange: [
    { required: true, message: "日期范围不能为空", trigger: "change" }
  ]
});

const batchEditRules = reactive({
  status: [
    { required: true, message: "状态不能为空", trigger: "change" }
  ]
});

/** 获取状态类型 */
function getStatusType(status) {
  const statusMap = {
    0: 'success',  // 可用
    1: 'warning',  // 已预约
    2: 'danger',   // 不可用
    3: 'info'      // 已过期
  };
  return statusMap[status] || 'info';
}

/** 获取状态文本 */
function getStatusText(status) {
  const statusMap = {
    0: '可用',
    1: '已预约',
    2: '不可用',
    3: '已过期'
  };
  return statusMap[status] || '未知';
}

/** 获取时间槽样式类 */
function getSlotClass(slot) {
  return `slot-${getStatusType(slot.status)}`;
}

/** 根据日期获取时间槽 */
function getSlotsByDate(date) {
  return calendarData.value.filter(slot => slot.slotDate === date);
}

/** 查询排班数据 */
function getScheduleData() {
  loading.value = true;

  if (viewMode.value === 'calendar') {
    // 日历视图获取当月数据
    const year = calendarDate.value.getFullYear();
    const month = calendarDate.value.getMonth() + 1;
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
    const endDate = new Date(year, month, 0).toISOString().split('T')[0];

    const params = {
      ...queryParams,
      startDate,
      endDate,
      pageNum: 1,
      pageSize: 1000 // 获取所有数据
    };

    listTimeSlot(params).then(response => {
      calendarData.value = response.rows || [];
      loading.value = false;
    });
  } else {
    // 表格视图
    listTimeSlot(queryParams).then(response => {
      scheduleList.value = response.rows || [];
      total.value = response.total || 0;
      loading.value = false;
    });
  }
}

/** 获取咨询师列表 */
function getCounselorList() {
  listConsultant().then(response => {
    counselorList.value = response.rows || response.data || [];
  });
}

/** 根据咨询师ID获取咨询师姓名 */
function getCounselorName(counselorId) {
  if (!counselorId) return '未知咨询师';
  const counselor = counselorList.value.find(c => c.id === counselorId);
  return counselor ? counselor.name : `咨询师${counselorId}`;
}

/** 日期范围变化处理 */
function handleDateRangeChange(dateRange) {
  if (dateRange && dateRange.length === 2) {
    // 使用 value-format="YYYY-MM-DD" 后，直接获得字符串格式
    queryParams.startDate = dateRange[0];
    queryParams.endDate = dateRange[1];
  } else {
    queryParams.startDate = undefined;
    queryParams.endDate = undefined;
  }
}

/** 将 Date 对象转换为 YYYY-MM-DD 格式字符串 */
function formatDateToString(date) {
  if (!date) return undefined;
  // 如果已经是字符串格式，直接返回
  if (typeof date === 'string') return date;
  // 如果是 Date 对象，转换为字符串
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getScheduleData();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.startDate = undefined;
  queryParams.endDate = undefined;
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  selectedSlots.value = selection;
}

/** 切换视图模式 */
function switchViewMode() {
  viewMode.value = viewMode.value === 'table' ? 'calendar' : 'table';
  getScheduleData();
}

/** 日历面板变化 */
function handlePanelChange() {
  if (viewMode.value === 'calendar') {
    getScheduleData();
  }
}

/** 查看时间槽详情 */
function viewSlotDetail(slot) {
  currentSlot.value = slot;
  slotDetailOpen.value = true;
}

/** 修改时间槽 */
function editSlot(slot) {
  // 这里可以跳转到时间槽编辑页面或打开编辑对话框
  proxy.$router.push(`/wechat/timeSlot?id=${slot.id}`);
}

/** 删除时间槽 */
function deleteSlot(slot) {
  ElMessageBox.confirm('是否确认删除该时间槽?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return delTimeSlot(slot.id);
  }).then(() => {
    getScheduleData();
    ElMessage.success("删除成功");
  }).catch(() => { });
}

/** 查看预约信息 */
function viewAppointment(slot) {
  ElMessageBox.alert(slot.appointmentInfo || '暂无预约信息', '预约详情', {
    confirmButtonText: '确定'
  });
}

/** 显示批量生成对话框 */
function showBatchGenerateDialog() {
  batchGenerateForm.counselorIds = [];
  batchGenerateForm.dateRange = [];
  batchGenerateOpen.value = true;
}

/** 批量生成排班 */
function handleBatchGenerate() {
  proxy.$refs["batchGenerateRef"].validate(valid => {
    if (valid) {
      const { counselorIds, dateRange } = batchGenerateForm;
      const [startDate, endDate] = dateRange;

      // 如果选择了特定咨询师，需要循环调用单个咨询师的生成接口
      // 这里简化处理，直接调用生成所有咨询师的接口
      generateSlotsForAll(startDate, endDate).then(response => {
        ElMessage.success(response.msg);
        batchGenerateOpen.value = false;
        getScheduleData();
      });
    }
  });
}

/** 显示批量修改对话框 */
function showBatchEditDialog() {
  if (selectedSlots.value.length === 0) {
    ElMessage.warning('请先选择要修改的时间槽');
    return;
  }
  batchEditForm.status = undefined;
  batchEditOpen.value = true;
}

/** 批量修改状态 */
function handleBatchEdit() {
  proxy.$refs["batchEditRef"].validate(valid => {
    if (valid) {
      const slotIds = selectedSlots.value.map(slot => slot.id);
      batchUpdateSlotStatus(slotIds, batchEditForm.status).then(response => {
        ElMessage.success("批量修改成功");
        batchEditOpen.value = false;
        getScheduleData();
      });
    }
  });
}

onMounted(() => {
  getCounselorList();
  getScheduleData();
});
</script>

<style scoped>
.calendar-view {
  margin-top: 20px;
}

.calendar-cell {
  height: 120px;
  overflow: hidden;
}

.date-number {
  font-weight: bold;
  margin-bottom: 5px;
}

.slots-container {
  max-height: 90px;
  overflow-y: auto;
}

.slot-item {
  font-size: 10px;
  padding: 2px 4px;
  margin: 1px 0;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s;
}

.slot-item:hover {
  transform: scale(1.05);
}

.slot-success {
  background-color: #f0f9ff;
  border: 1px solid #67c23a;
  color: #67c23a;
}

.slot-warning {
  background-color: #fdf6ec;
  border: 1px solid #e6a23c;
  color: #e6a23c;
}

.slot-danger {
  background-color: #fef0f0;
  border: 1px solid #f56c6c;
  color: #f56c6c;
}

.slot-info {
  background-color: #f4f4f5;
  border: 1px solid #909399;
  color: #909399;
}

.slot-time {
  font-weight: bold;
}

.slot-counselor {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.slot-status {
  font-size: 9px;
}
</style>
