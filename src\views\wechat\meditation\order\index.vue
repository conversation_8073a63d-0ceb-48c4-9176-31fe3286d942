<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="冥想名称" prop="meditationName">
        <el-input v-model="queryParams.meditationName" placeholder="请输入冥想名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="支付状态" prop="paymentStatus">
        <el-select v-model="queryParams.paymentStatus" placeholder="请选择支付状态" clearable style="width: 240px">
          <el-option label="待支付" value="0" />
          <el-option label="已支付" value="1" />
          <el-option label="支付失败" value="2" />
          <el-option label="已退款" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="orderStatus">
        <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable style="width: 240px">
          <el-option label="待支付" value="0" />
          <el-option label="已完成" value="1" />
          <el-option label="已取消" value="2" />
          <el-option label="已退款" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:meditationOrder:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:meditationOrder:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:meditationOrder:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:meditationOrder:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="orderNo" width="180" />
      <el-table-column label="冥想名称" align="center" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ getMeditationNameById(scope.row.meditationId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" width="120">
        <template #default="scope">
          <span>{{ getUserNameById(scope.row.userId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单金额" align="center" prop="totalAmount" width="100">
        <template #default="scope">
          <span class="text-red-500">¥{{ scope.row.totalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付金额" align="center" prop="paidAmount" width="100">
        <template #default="scope">
          <span class="text-green-500">¥{{ scope.row.paidAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="paymentMethod" width="100">
        <template #default="scope">
          <dict-tag :options="sys_payment_method" :value="scope.row.paymentMethod" />
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="paymentStatus" width="100">
        <template #default="scope">
          <dict-tag :options="sys_payment_status" :value="scope.row.paymentStatus" />
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="orderStatus" width="100">
        <template #default="scope">
          <dict-tag :options="sys_order_status" :value="scope.row.orderStatus" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:meditationOrder:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:meditationOrder:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:meditationOrder:remove']">删除</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="payment" v-if="scope.row.paymentStatus == 0" icon="CreditCard">支付
                </el-dropdown-item>
                <el-dropdown-item command="refund" v-if="scope.row.paymentStatus == 1" icon="RefreshLeft">退款
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改订单对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="orderRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单号" prop="orderNo">
              <el-input v-model="form.orderNo" placeholder="请输入订单号" :disabled="form.id != null" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="冥想" prop="meditationId">
              <el-select v-model="form.meditationId" placeholder="请选择冥想" style="width: 100%">
                <el-option v-for="meditation in meditationList" :key="meditation.id" :label="meditation.title"
                  :value="meditation.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单金额" prop="totalAmount">
              <el-input-number v-model="form.totalAmount" :precision="2" :min="0" :max="99999" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付金额" prop="paidAmount">
              <el-input-number v-model="form.paidAmount" :precision="2" :min="0" :max="99999" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="支付方式" prop="paymentMethod">
              <el-select v-model="form.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
                <el-option v-for="dict in sys_payment_method" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付状态" prop="paymentStatus">
              <el-select v-model="form.paymentStatus" placeholder="请选择支付状态" style="width: 100%">
                <el-option v-for="dict in sys_payment_status" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单状态" prop="orderStatus">
              <el-select v-model="form.orderStatus" placeholder="请选择订单状态" style="width: 100%">
                <el-option v-for="dict in sys_order_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交易流水号" prop="transactionId">
              <el-input v-model="form.transactionId" placeholder="请输入交易流水号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ detailForm.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="冥想名称">{{ detailForm.meditationName }}</el-descriptions-item>
        <el-descriptions-item label="用户">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">¥{{ detailForm.totalAmount }}</el-descriptions-item>
        <el-descriptions-item label="支付金额">¥{{ detailForm.paidAmount }}</el-descriptions-item>
        <el-descriptions-item label="退款金额">¥{{ detailForm.refundAmount }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">
          <dict-tag :options="sys_payment_method" :value="detailForm.paymentMethod" />
        </el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <dict-tag :options="sys_payment_status" :value="detailForm.paymentStatus" />
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <dict-tag :options="sys_order_status" :value="detailForm.orderStatus" />
        </el-descriptions-item>
        <el-descriptions-item label="交易流水号">{{ detailForm.transactionId }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ parseTime(detailForm.paymentTime) }}</el-descriptions-item>
        <el-descriptions-item label="退款时间">{{ parseTime(detailForm.refundTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailForm.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 支付对话框 -->
    <el-dialog title="更新支付状态" v-model="paymentOpen" width="400px" append-to-body>
      <el-form ref="paymentRef" :model="paymentForm" :rules="paymentRules" label-width="80px">
        <el-form-item label="支付状态" prop="status">
          <el-select v-model="paymentForm.status" placeholder="请选择支付状态" style="width: 100%">
            <el-option label="已支付" value="1" />
            <el-option label="支付失败" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="paymentForm.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
            <el-option v-for="dict in sys_payment_method" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易流水号" prop="transactionId">
          <el-input v-model="paymentForm.transactionId" placeholder="请输入交易流水号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPayment">确 定</el-button>
          <el-button @click="paymentOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退款对话框 -->
    <el-dialog title="订单退款" v-model="refundOpen" width="400px" append-to-body>
      <el-form ref="refundRef" :model="refundForm" :rules="refundRules" label-width="80px">
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input-number v-model="refundForm.refundAmount" :precision="2" :min="0" :max="refundForm.maxAmount" />
          <div class="text-gray-500 text-sm mt-1">最大可退款金额：¥{{ refundForm.maxAmount }}</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRefund">确 定</el-button>
          <el-button @click="refundOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MeditationOrder">
import {
  listMeditationOrder, getMeditationOrder, delMeditationOrder, addMeditationOrder, updateMeditationOrder, exportMeditationOrder,
  updateMeditationOrderPaymentStatus, refundMeditationOrder, generateMeditationOrderNo
} from "@/api/wechat/meditation/order";
import { listMeditation } from "@/api/wechat/meditation/meditation";
import { listUser } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { sys_payment_method, sys_payment_status, sys_order_status } = proxy.useDict('sys_payment_method', 'sys_payment_status', 'sys_order_status');

const orderList = ref([]);
const meditationList = ref([]);
const userList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const paymentOpen = ref(false);
const refundOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  paymentForm: {},
  refundForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: null,
    meditationName: null,
    paymentStatus: null,
    orderStatus: null
  },
  rules: {
    orderNo: [
      { required: true, message: "订单号不能为空", trigger: "blur" }
    ],
    meditationId: [
      { required: true, message: "冥想不能为空", trigger: "change" }
    ],
    totalAmount: [
      { required: true, message: "订单金额不能为空", trigger: "blur" }
    ]
  },
  paymentRules: {
    status: [
      { required: true, message: "支付状态不能为空", trigger: "change" }
    ],
    paymentMethod: [
      { required: true, message: "支付方式不能为空", trigger: "change" }
    ]
  },
  refundRules: {
    refundAmount: [
      { required: true, message: "退款金额不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, paymentForm, refundForm, rules, paymentRules, refundRules } = toRefs(data);

/** 查询订单列表 */
function getList() {
  loading.value = true;
  listMeditationOrder(queryParams.value).then(response => {
    orderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询冥想列表 */
function getMeditationList() {
  listMeditation().then(response => {
    meditationList.value = response.rows;
  });
}

/** 根据冥想ID获取冥想名称 */
function getMeditationNameById(meditationId) {
  if (!meditationId) return '-';
  const meditation = meditationList.value.find(item => item.id === meditationId);
  return meditation ? meditation.title : `冥想ID: ${meditationId}`;
}

/** 查询用户列表 */
function getUserList() {
  listUser().then(response => {
    userList.value = response.rows;
  });
}

/** 根据用户ID获取用户名称 */
function getUserNameById(userId) {
  if (!userId) return '-';
  const user = userList.value.find(item => item.userId === userId);
  return user ? (user.nickName || user.userName) : `用户ID: ${userId}`;
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    orderNo: null,
    meditationId: null,
    userId: null,
    totalAmount: null,
    paidAmount: null,
    refundAmount: null,
    paymentMethod: null,
    paymentStatus: "0",
    orderStatus: "0",
    transactionId: null,
    remark: null
  };
  proxy.resetForm("orderRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 生成订单号
  generateMeditationOrderNo().then(response => {
    form.value.orderNo = response.data;
  });
  open.value = true;
  title.value = "添加订单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getMeditationOrder(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改订单";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getMeditationOrder(_id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["orderRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateMeditationOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMeditationOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除订单编号为"' + _ids + '"的数据项？').then(function () {
    return delMeditationOrder(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/meditationOrder/export', {
    ...queryParams.value
  }, `meditation_order_${new Date().getTime()}.xlsx`)
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'payment':
      handlePayment(row);
      break;
    case 'refund':
      handleRefund(row);
      break;
  }
}

/** 支付处理 */
function handlePayment(row) {
  paymentForm.value = {
    orderNo: row.orderNo,
    status: null,
    paymentMethod: null,
    transactionId: null
  };
  paymentOpen.value = true;
}

/** 提交支付 */
function submitPayment() {
  proxy.$refs["paymentRef"].validate(valid => {
    if (valid) {
      updateMeditationOrderPaymentStatus(
        paymentForm.value.orderNo,
        paymentForm.value.status,
        paymentForm.value.paymentMethod,
        paymentForm.value.transactionId
      ).then(response => {
        proxy.$modal.msgSuccess("支付状态更新成功");
        paymentOpen.value = false;
        getList();
      });
    }
  });
}

/** 退款处理 */
function handleRefund(row) {
  refundForm.value = {
    orderNo: row.orderNo,
    refundAmount: null,
    maxAmount: row.paidAmount
  };
  refundOpen.value = true;
}

/** 提交退款 */
function submitRefund() {
  proxy.$refs["refundRef"].validate(valid => {
    if (valid) {
      refundMeditationOrder(refundForm.value.orderNo, refundForm.value.refundAmount).then(response => {
        proxy.$modal.msgSuccess("退款成功");
        refundOpen.value = false;
        getList();
      });
    }
  });
}

onMounted(() => {
  getList();
  getMeditationList();
  getUserList();
});
</script>
