<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="用户ID" prop="userId">
            <el-input
               v-model="queryParams.userId"
               placeholder="请输入用户ID"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="搜索关键词" prop="keyword">
            <el-input
               v-model="queryParams.keyword"
               placeholder="请输入搜索关键词"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="搜索类型" prop="searchType">
            <el-select
               v-model="queryParams.searchType"
               placeholder="请选择搜索类型"
               clearable
               style="width: 240px"
            >
               <el-option label="全部" value="all" />
               <el-option label="咨询师" value="consultant" />
               <el-option label="课程" value="course" />
               <el-option label="冥想" value="meditation" />
               <el-option label="测评" value="assessment" />
            </el-select>
         </el-form-item>
         <el-form-item label="IP地址" prop="ipAddress">
            <el-input
               v-model="queryParams.ipAddress"
               placeholder="请输入IP地址"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="搜索时间" style="width: 308px">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD"
               type="daterange"
               range-separator="-"
               start-placeholder="开始日期"
               end-placeholder="结束日期"
            ></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['search:record:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['search:record:remove']"
            >删除</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['search:record:export']"
            >导出</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="ID" align="center" prop="id" width="80" />
         <el-table-column label="用户ID" align="center" prop="userId" width="100" />
         <el-table-column label="搜索关键词" align="center" prop="keyword" :show-overflow-tooltip="true" />
         <el-table-column label="搜索类型" align="center" prop="searchType" width="100">
            <template #default="scope">
               <dict-tag :options="search_type_options" :value="scope.row.searchType" />
            </template>
         </el-table-column>
         <el-table-column label="结果数量" align="center" prop="resultCount" width="100" />
         <el-table-column label="搜索时间" align="center" prop="searchTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.searchTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
         </el-table-column>
         <el-table-column label="IP地址" align="center" prop="ipAddress" width="140" />
         <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['search:record:edit']">修改</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['search:record:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>
      
      <pagination
         v-show="total>0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改搜索记录对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="recordRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="用户ID" prop="userId">
               <el-input v-model="form.userId" placeholder="请输入用户ID" />
            </el-form-item>
            <el-form-item label="搜索关键词" prop="keyword">
               <el-input v-model="form.keyword" placeholder="请输入搜索关键词" />
            </el-form-item>
            <el-form-item label="搜索类型" prop="searchType">
               <el-select v-model="form.searchType" placeholder="请选择搜索类型">
                  <el-option label="全部" value="all" />
                  <el-option label="咨询师" value="consultant" />
                  <el-option label="课程" value="course" />
                  <el-option label="冥想" value="meditation" />
                  <el-option label="测评" value="assessment" />
               </el-select>
            </el-form-item>
            <el-form-item label="结果数量" prop="resultCount">
               <el-input-number v-model="form.resultCount" :min="0" />
            </el-form-item>
            <el-form-item label="IP地址" prop="ipAddress">
               <el-input v-model="form.ipAddress" placeholder="请输入IP地址" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="SearchRecord">
import { listSearchRecord, getSearchRecord, delSearchRecord, addSearchRecord, updateSearchRecord, exportSearchRecord } from "@/api/system/search/record";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const recordList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);

// 搜索类型选项
const search_type_options = ref([
  { label: "全部", value: "all" },
  { label: "咨询师", value: "consultant" },
  { label: "课程", value: "course" },
  { label: "冥想", value: "meditation" },
  { label: "测评", value: "assessment" }
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: null,
    keyword: null,
    searchType: null,
    ipAddress: null
  },
  rules: {
    userId: [
      { required: true, message: "用户ID不能为空", trigger: "blur" }
    ],
    keyword: [
      { required: true, message: "搜索关键词不能为空", trigger: "blur" }
    ],
    searchType: [
      { required: true, message: "搜索类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询搜索记录列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != dateRange && '' != dateRange) {
    queryParams.value.params["beginTime"] = dateRange.value[0];
    queryParams.value.params["endTime"] = dateRange.value[1];
  }
  listSearchRecord(queryParams.value).then(response => {
    recordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: null,
    keyword: null,
    searchType: null,
    resultCount: 0,
    ipAddress: null
  };
  proxy.resetForm("recordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加搜索记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getSearchRecord(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改搜索记录";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["recordRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateSearchRecord(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSearchRecord(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const recordIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除搜索记录编号为"' + recordIds + '"的数据项？').then(function() {
    return delSearchRecord(recordIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/search/record/export', {
    ...queryParams.value
  }, `search_record_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
