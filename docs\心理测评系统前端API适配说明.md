# 心理测评系统前端API适配说明

## 概述

根据后端调整的测评相关接口，前端需要进行相应的API适配。本文档详细说明了需要修改的文件和具体的适配方案。

## 主要变更

### 1. API路径调整

**原路径格式：** `/system/assessment/xxx`
**新路径格式：** `/system/xxx` 或 `/miniapp/user/psy-assessment/xxx`

### 2. 新增接口类型

- **后台管理接口：** `/system/xxx` - 用于管理员操作
- **小程序用户接口：** `/miniapp/user/psy-assessment/xxx` - 用于用户测评
- **企业测评接口：** `/miniapp/enterprise/assessment/xxx` - 用于企业测评

## 文件修改清单

### 1. API文件更新

#### 1.1 量表管理API (`src/api/system/assessment/scale.js`)

**主要变更：**
- 路径从 `/system/assessment/scale/` 改为 `/system/scale/`
- 发布/下架接口改为POST方法
- 新增复制量表和验证量表接口

**新增接口：**
```javascript
// 复制量表
export function copyScale(id, data)

// 验证量表完整性
export function validateScale(id)
```

#### 1.2 新增小程序测评API (`src/api/miniapp/assessment.js`)

**包含接口：**
- 量表查询相关（列表、详情、搜索、热门、推荐等）
- 测评流程相关（开始、答题、进度控制等）
- 测评记录相关（记录查询、报告生成等）
- 统计分析相关

#### 1.3 新增企业测评API (`src/api/miniapp/enterprise.js`)

**包含接口：**
- 企业量表管理
- 测评计划管理
- 参与者管理
- 统计报告

### 2. 前端组件更新

#### 2.1 量表管理页面 (`src/views/system/assessment/scale/index.vue`)

**需要更新的功能：**
- 发布/下架操作的API调用
- 新增复制量表功能
- 新增验证量表功能
- 统计信息显示

#### 2.2 测评记录页面 (`src/views/system/assessment/record/index.vue`)

**需要更新的功能：**
- 查看答案功能
- 生成报告功能
- 趋势统计功能

#### 2.3 新增测评流程组件 (`src/views/miniapp/assessment/AssessmentFlow.vue`)

**主要功能：**
- 测评开始页面
- 题目答题界面
- 进度控制
- 结果展示

## 具体适配步骤

### 步骤1：更新现有API文件

1. **更新量表API路径**
```javascript
// 原路径
url: '/system/assessment/scale/list'

// 新路径
url: '/system/scale/list'
```

2. **更新发布/下架接口**
```javascript
// 原接口
export function publishScale(id) {
  return request({
    url: '/system/assessment/scale/publish/' + id,
    method: 'put'
  })
}

// 新接口
export function publishScale(id) {
  return request({
    url: '/system/scale/' + id + '/publish',
    method: 'post'
  })
}
```

### 步骤2：创建新的API文件

1. **创建小程序测评API** (`src/api/miniapp/assessment.js`)
2. **创建企业测评API** (`src/api/miniapp/enterprise.js`)

### 步骤3：更新前端组件

1. **更新导入语句**
```javascript
// 根据使用场景选择合适的API
import { listScale, publishScale } from '@/api/system/assessment/scale'
// 或
import { listScales, getScaleDetail } from '@/api/miniapp/assessment'
```

2. **更新方法调用**
```javascript
// 发布量表
const handlePublish = async (row) => {
  try {
    await publishScale(row.id)
    ElMessage.success('发布成功')
    getList()
  } catch (error) {
    ElMessage.error('发布失败')
  }
}
```

### 步骤4：新增功能实现

1. **复制量表功能**
```javascript
const handleCopy = async (row) => {
  try {
    const { value: newName } = await ElMessageBox.prompt('请输入新量表名称', '复制量表')
    await copyScale(row.id, { name: newName })
    ElMessage.success('复制成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('复制失败')
    }
  }
}
```

2. **验证量表功能**
```javascript
const handleValidate = async (row) => {
  try {
    const result = await validateScale(row.id)
    if (result.data.isValid) {
      ElMessage.success('量表验证通过')
    } else {
      ElMessage.warning(`验证失败：${result.data.errors.join(', ')}`)
    }
  } catch (error) {
    ElMessage.error('验证失败')
  }
}
```

## 数据结构变更

### 1. 量表数据结构 (PsyTScale)

**新增字段：**
- `alias`: 量表别名
- `payMode`: 付费模式
- `payPhase`: 付费阶段
- `originalPrice`: 原价
- `currentPrice`: 现价
- `freeReportLevel`: 免费报告层级
- `paidReportLevel`: 付费报告层级
- `searchKeywords`: 搜索关键词
- `searchCount`: 搜索次数
- `viewCount`: 查看次数
- `testCount`: 测试次数
- `ratingAvg`: 平均评分
- `ratingCount`: 评分次数

### 2. 测评记录数据结构 (PsyTAssessmentRecord)

**新增字段：**
- `sessionId`: 会话ID
- `resultLevel`: 结果等级
- `resultDescription`: 结果描述
- `currentQuestionNo`: 当前题目序号
- `answeredCount`: 已答题数
- `progress`: 进度百分比
- `duration`: 测评耗时
- `source`: 测评来源
- `reportLevel`: 报告层级

## 错误处理

### 1. 统一错误处理

```javascript
const handleApiError = (error, defaultMessage = '操作失败') => {
  const message = error.response?.data?.msg || error.message || defaultMessage
  ElMessage.error(message)
  console.error('API Error:', error)
}
```

### 2. 网络超时处理

```javascript
const handleTimeout = () => {
  ElMessage.error('请求超时，请检查网络连接')
}
```

## 测试建议

### 1. 单元测试

- 测试API接口调用
- 测试数据格式转换
- 测试错误处理逻辑

### 2. 集成测试

- 测试完整的测评流程
- 测试数据同步
- 测试权限控制

### 3. 用户体验测试

- 测试页面加载速度
- 测试交互响应
- 测试错误提示

## 注意事项

1. **向后兼容性**：确保现有功能不受影响
2. **权限控制**：新接口需要适配现有权限系统
3. **数据迁移**：如有数据结构变更，需要考虑数据迁移
4. **缓存更新**：清理相关缓存，确保使用新接口
5. **文档更新**：及时更新相关技术文档

## 部署清单

### 1. 代码更新
- [ ] 更新API文件
- [ ] 更新组件文件
- [ ] 新增测评流程组件
- [ ] 更新路由配置

### 2. 配置更新
- [ ] 更新API基础路径
- [ ] 更新权限配置
- [ ] 更新环境变量

### 3. 测试验证
- [ ] API接口测试
- [ ] 功能测试
- [ ] 性能测试
- [ ] 兼容性测试

### 4. 文档更新
- [ ] 更新API文档
- [ ] 更新用户手册
- [ ] 更新开发文档

## 已完成的适配工作

### 1. API文件更新
- ✅ 更新了 `src/api/system/assessment/scale.js` - 量表管理API
- ✅ 更新了 `src/api/system/assessment/record.js` - 测评记录API
- ✅ 创建了 `src/api/miniapp/assessment.js` - 小程序测评API
- ✅ 创建了 `src/api/miniapp/enterprise.js` - 企业测评API

### 2. 前端组件创建
- ✅ 创建了 `src/views/miniapp/assessment/AssessmentFlow.vue` - 测评流程组件
- ✅ 创建了 `src/views/miniapp/assessment/ScaleList.vue` - 量表列表组件
- ✅ 创建了 `src/views/miniapp/assessment/AssessmentReport.vue` - 测评报告组件
- ✅ 创建了 `src/views/miniapp/enterprise/PlanList.vue` - 企业计划管理组件

### 3. 路由配置
- ✅ 创建了 `src/router/modules/assessment.js` - 测评相关路由配置

### 4. 权限指令修复
- ✅ 修复了 Element Plus 组件上的 `v-hasPermi` 指令问题
- ✅ 更新了相关页面使用 `checkPermi` 函数

## 下一步工作建议

### 1. 完善组件功能
- [ ] 实现量表详情页面
- [ ] 实现测评记录详情页面
- [ ] 实现企业参与者管理页面
- [ ] 实现统计分析页面

### 2. 集成现有系统
- [ ] 将新的测评路由集成到主路由配置中
- [ ] 更新导航菜单配置
- [ ] 适配现有的权限系统

### 3. 数据联调
- [ ] 与后端进行接口联调
- [ ] 验证数据格式和字段映射
- [ ] 处理异常情况和错误提示

### 4. 用户体验优化
- [ ] 添加加载状态和骨架屏
- [ ] 优化移动端适配
- [ ] 添加操作确认和提示

### 5. 测试验证
- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户体验测试

## 技术要点总结

### 1. API路径变更
```javascript
// 原路径
'/system/assessment/scale/list'

// 新路径
'/system/scale/list'                    // 后台管理
'/miniapp/user/psy-assessment/scale/list' // 小程序用户
'/miniapp/enterprise/assessment/scale/available' // 企业测评
```

### 2. 新增功能特性
- 测评流程控制（开始、暂停、恢复、完成）
- 多级报告系统（免费/付费报告）
- 企业测评计划管理
- 实时进度跟踪
- 统计分析功能

### 3. 数据结构增强
- 量表支持付费模式和多级报告
- 测评记录包含详细进度信息
- 企业测评支持批量管理

### 4. 用户体验改进
- 响应式设计适配移动端
- 直观的进度显示
- 丰富的交互反馈
- 便捷的操作流程

## 联系方式

如有问题，请联系开发团队或查看相关技术文档。

## 更新日志

- 2024-01-XX: 完成API适配和基础组件开发
- 2024-01-XX: 修复权限指令问题
- 2024-01-XX: 创建路由配置和文档
