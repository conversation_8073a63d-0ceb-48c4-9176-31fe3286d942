<template>
  <div class="app-container chat-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>{{ isAdmin ? '管理员聊天控制台' : '在线咨询' }}</span>
        </div>
      </template>

      <div class="chat-main">
        <!-- 左侧会话列表 -->
        <!-- <audio controls src="/assets/audio.mp3"></audio> -->
        <!-- <audio controls ref="audioRef" autoplay loop>
          <source src="../../../../public/audio.mp3" type="audio/mpeg">
          您的浏览器不支持 audio 元素。
        </audio> -->
        <div class="conversation-list">
          <el-input v-model="searchText" placeholder="搜索会话" prefix-icon="Search" clearable />

          <el-scrollbar height="calc(100vh - 240px)" v-loading="loading">
            <template v-if="filteredConversations.length === 0 && !loading">
              <div class="empty-tip">
                暂无会话
              </div>
            </template>
            <template v-else>
              <div v-for="(conversation) in filteredConversations" :key="conversation.conversationId"
                class="conversation-item"
                :class="{ active: currentConversation?.conversationId === conversation.conversationId }"
                @click="selectConversation(conversation)">
                <div class="avatar">
                  <el-avatar :size="40" :src="conversation.userAvatar">
                    {{ getInitials(conversation.userId) }}
                  </el-avatar>
                </div>
                <div class="conversation-info">
                  <div class="conversation-header">
                    <span class="name">{{ `用户${conversation.userName} 与咨询师${conversation.counselorName ||
                      conversation.consultantId} 的对话` }}</span>
                    <span class="time">{{ formatTime(conversation.lastMessageTime) }}</span>
                  </div>
                  <el-badge v-if="conversation.consultantUnreadCount" :value="conversation.consultantUnreadCount"
                    type="danger" :max="99" class="badge" />
                  <div class="last-message">
                    <span v-if="conversation.lastSenderId === conversation.userId">
                      用户:
                    </span>
                    <span v-else>
                      咨询师:
                    </span>
                    {{ conversation.lastMessage || '暂无消息' }}
                  </div>
                </div>
              </div>
            </template>
          </el-scrollbar>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="chat-area">
          <template v-if="currentConversation">
            <!-- 聊天头部 -->
            <div class="chat-header">
              <span>
                {{ `用户 ${userName} 与咨询师 ${consultantName} 的对话` }}
              </span>
              <!-- <div v-if="isAdmin" class="admin-tip">
                <el-tag type="warning">管理员模式：以咨询师身份回复</el-tag>
              </div> -->
            </div>

            <!-- 聊天消息列表 -->
            <div class="message-list" ref="messageListRef">
              <el-scrollbar ref="scrollbarRef" v-loading="loading">
                <div ref="loadMoreRef" v-if="hasMore && !loading" class="load-more" @click="loadMoreMessages">
                  <span>加载更多</span>
                </div>

                <template v-if="messages.length === 0 && !loading">
                  <div class="empty-tip">
                    暂无消息，开始聊天吧
                  </div>
                </template>

                <template v-else>
                  <template v-for="(group, groupIndex) in groupedMessages" :key="groupIndex">
                    <template v-for="message in group.messages" :key="message.messageId">
                      <!-- 时间显示 -->
                      <template v-if="shouldShowTime(message)">
                        <div class="time-divider">
                          <span>{{ formatTimeDisplay(message.sendTime, group.isToday, group.date) }}</span>
                        </div>
                      </template>

                      <div class="message-item" :data-message-id="message.messageId" :class="{
                        'message-self': isSelfMessage(message),
                        'message-withdrawn': message.isWithdrawn === '1'
                      }">
                        <!-- 消息头像 -->
                        <div class="message-avatar">
                          <el-avatar :size="30" :src="getAvatarUrl(message)">
                            {{ getInitials(message.senderId) }}
                          </el-avatar>
                        </div>

                        <!-- 消息内容 -->
                        <div class="message-content">
                          <!-- 管理员标签 -->
                          <div v-if="isAdminMessage(message)" class="admin-tag-top">
                            <el-tag type="danger" size="small">管理员</el-tag>
                          </div>

                          <div class="message-bubble" :class="{ 'text-bubble': message.messageType === '0' }">
                            <template v-if="message.isWithdrawn === '1'">
                              <span class="withdrawn-text">此消息已撤回</span>
                            </template>
                            <template v-else>
                              <!-- 根据消息类型显示不同内容 -->
                              <template v-if="message.messageType === '0'">
                                <!-- 文本消息 -->
                                {{ message.content }}
                              </template>

                              <template v-else-if="message.messageType === '1'">
                                <!-- 图片消息 -->
                                <div class="image-content">
                                  <el-image :src="message.fileUrl" :preview-src-list="[message.fileUrl]" fit="cover" />
                                </div>
                              </template>

                              <template v-else-if="message.messageType === '2'">
                                <!-- 文件消息 -->
                                <div class="file-content">
                                  <el-button type="primary" link @click="downloadFile(message.fileUrl)">
                                    <el-icon>
                                      <Document />
                                    </el-icon>
                                    下载文件
                                  </el-button>
                                </div>
                              </template>
                            </template>
                          </div>

                          <!-- 消息操作 -->
                          <div class="message-actions" v-if="isSelfMessage(message) && message.isWithdrawn !== '1'">
                            <el-button type="primary" link size="small" @click="withdrawMsg(message.messageId)">
                              撤回
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </template>
                  </template>
                </template>
              </el-scrollbar>
            </div>

            <!-- 聊天输入框 -->
            <div class="message-input">
              <div class="input-toolbar">
                <el-upload action="#" :auto-upload="false" :show-file-list="false" :on-change="handleImageSelect">
                  <el-button type="primary" link>
                    <el-icon>
                      <Picture />
                    </el-icon>
                  </el-button>
                </el-upload>

                <el-upload action="#" :auto-upload="false" :show-file-list="false" :on-change="handleFileSelect">
                  <el-button type="primary" link>
                    <el-icon>
                      <Paperclip />
                    </el-icon>
                  </el-button>
                </el-upload>
              </div>

              <div class="input-area">
                <el-input v-model="inputMessage" type="textarea" :rows="3" placeholder="输入消息..." resize="none"
                  @keyup.enter="handleEnterPress" />

                <el-button type="primary" @click="sendMessage" :disabled="!inputMessage.trim()">
                  发送
                </el-button>
              </div>
            </div>
          </template>

          <div v-else class="no-conversation">
            <el-empty description="请选择一个会话" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, Paperclip, Document } from '@element-plus/icons-vue'
import { useChatStore } from '@/store/modules/chat'
import useUserStore from '@/store/modules/user'
import { sendImage, sendFile, markSessionRead } from '@/api/wechat/message'
import { formatDate } from '@/utils'

// 初始化store
const chatStore = useChatStore()
const userStore = useUserStore()

// 初始化用户角色
onMounted(() => {
  // 根据用户角色设置聊天模式
  const userRoles = userStore.roles || []

  // 根据用户权限设置角色
  chatStore.setUserRoleByPermissions(userRoles)

  // 初始化WebSocket连接
  chatStore.initWebSocket(userStore.id)

  // 获取会话列表
  chatStore.getConversations()

  // 监听消息加载完成事件
  document.addEventListener('chat-messages-loaded', () => {
    console.log('消息加载完成事件触发');
    setTimeout(() => {
      scrollToBottom();
    }, 100);
  });

  // 监听WebSocket消息接收事件
  window.addEventListener('websocket-message-received', (event) => {
    console.log('WebSocket消息接收事件触发', event.detail);
    setTimeout(() => {
      scrollToBottom();
    }, 200);
  });

  // 监听页面刷新或关闭事件，确保离开前标记当前会话已读
  window.addEventListener('beforeunload', () => {
    if (chatStore.currentConversation?.conversationId) {
      console.log('页面刷新或关闭，标记当前会话已读');
      // 标记当前会话已读
      const isUser = chatStore.userRole === 'user';
      markSessionRead(chatStore.currentConversation.conversationId, isUser).catch(() => {
        // 忽略错误，因为页面正在关闭
      });
    }
  });
})

// 响应式数据
const searchText = ref('')
const inputMessage = ref('')
const loading = computed(() => chatStore.loading)
const currentConversation = computed(() => chatStore.currentConversation)
const messages = computed(() => chatStore.messages)
const conversations = computed(() => chatStore.conversations)
const hasMore = computed(() => chatStore.hasMore)
const userRole = computed(() => chatStore.userRole)
const isAdmin = computed(() => chatStore.userRole === 'admin')
const isConsultant = computed(() => chatStore.userRole === 'consultant')
const messageListRef = ref(null)
const scrollbarRef = ref(null)
const loadMoreRef = ref(null)
const userAvatar = ref('')
const userName = ref('')
const consultantName = ref('')
const consultantAvatar = ref('')

// 过滤会话列表
const filteredConversations = computed(() => {
  if (!searchText.value) return conversations.value

  return conversations.value.filter(conv => {
    // 根据用户ID或咨询师ID搜索
    const userId = String(conv.userId)
    const consultantId = String(conv.consultantId)
    const searchValue = searchText.value.toLowerCase()

    return userId.includes(searchValue) ||
      consultantId.includes(searchValue) ||
      (conv.lastMessage && conv.lastMessage.toLowerCase().includes(searchValue))
  })
})

// 检查消息是否由自己发送
const isSelfMessage = (message) => {
  // 检查消息是否包含有效的senderId
  if (!message || !message.senderId) {
    console.log('消息缺少senderId:', message);
    return false;
  }

  // 获取发送者ID
  const senderId = message.senderId || message.sender_id || message.userId;

  // 如果是管理员发送的消息，显示在右边
  if (String(senderId) === '1') {
    return true;
  }

  // 如果是咨询师发送的消息，显示在右边
  if (String(senderId) === String(currentConversation.value?.consultantId)) {
    return true;
  }

  // 如果是普通用户发送的消息，只有在用户自己登录时才显示在右边
  return String(senderId) === String(userStore.id) && !isAdmin.value && !isConsultant.value;
}

// 检查消息是否由管理员发送
const isAdminMessage = (message) => {
  if (!message || !message.senderId) {
    return false;
  }
  const senderId = message.senderId || message.sender_id || message.userId;
  return String(senderId) === '1'; // 管理员ID为1
}

// 获取用户头像URL
const getAvatarUrl = (message) => {
  // 获取发送者ID
  const senderId = message.senderId || message.sender_id || message.userId;

  // 如果是管理员发送的消息
  if (String(senderId) === '1') {
    return userStore.avatar; // 使用管理员头像
  }

  // 如果是咨询师发送的消息
  if (String(senderId) === String(currentConversation.value?.consultantId)) {
    return consultantAvatar.value || userStore.avatar;
  }

  // 如果是用户发送的消息
  return userAvatar.value;
}

// 获取名称首字母（用于头像显示）
const getInitials = (userId) => {
  return `U${userId}`
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''

  const now = new Date()
  const date = new Date(timestamp)
  const diff = now.getTime() - date.getTime()
  const diffDays = Math.floor(diff / (24 * 60 * 60 * 1000))

  // 今天的消息只显示时间
  if (diffDays === 0) {
    return formatTimeMessage(timestamp, 'time')
  }

  // 昨天的消息显示"昨天 时间"
  if (diffDays === 1) {
    return `昨天 ${formatTimeMessage(timestamp, 'time')}`
  }

  // 本周内的消息显示"星期几 时间"
  if (diffDays < 7) {
    const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return `${weekday[date.getDay()]} ${formatTimeMessage(timestamp, 'time')}`
  }

  // 本年内的消息显示"月/日 时间"
  if (date.getFullYear() === now.getFullYear()) {
    return formatTimeMessage(timestamp, 'chat')
  }

  // 更早的消息显示完整日期
  return formatTimeMessage(timestamp, 'detail')
}

// 选择会话
const selectConversation = async (conversation) => {
  // 防止重复点击同一个会话
  if (chatStore.currentConversation?.conversationId === conversation.conversationId) {
    console.log('重复点击同一会话，不执行操作');
    return;
  }

  // 重置消息列表和分页状态
  chatStore.messages = [];
  chatStore.pageNum = 1;
  chatStore.hasMore = true;

  // 设置当前会话并加载消息
  await chatStore.setCurrentConversation(conversation);
  userAvatar.value = conversation.userAvatar;
  userName.value = conversation.userName;
  consultantName.value = conversation.counselorName;
  consultantAvatar.value = conversation.consultantAvatar;

  // 强制重新获取消息
  await chatStore.getMessages();

  // 延时确保消息加载完成后滚动到底部
  setTimeout(() => {
    scrollToBottom();
  }, 300);
}

// 加载更多消息 - 改用直接操作DOM的方式
const loadMoreMessages = () => {
  const scrollHeight = JSON.parse(JSON.stringify(scrollbarRef.value.wrapRef.scrollHeight));
  console.log(scrollHeight);

  // 标记加载状态
  chatStore.isLoadingMore = true;
  // 加载消息
  chatStore.getMessages().then(() => {
    // 获取滚动容器
    const scrollElem = scrollbarRef.value.wrapRef;
    console.log(chatStore.isLoadingMore);
    if (scrollElem) {
      // 调整滚动位置，使加载更多按钮保持在原来的位置
      scrollElem.scrollTop = scrollElem.scrollHeight - scrollHeight;
    }
    // 重置标记
    chatStore.isLoadingMore = false;
  }).catch(() => {
    chatStore.isLoadingMore = false;
  }).finally(() => {
    chatStore.isLoadingMore = false;
  });
}

// 滚动到底部
const scrollToBottom = () => {
  console.log('执行滚动到底部');

  // 使用多次延迟确保在DOM更新后滚动
  setTimeout(() => {
    try {
      if (scrollbarRef.value?.wrapRef) {
        const scrollWrap = scrollbarRef.value.wrapRef;
        scrollWrap.scrollTop = scrollWrap.scrollHeight;
        console.log('第一次尝试滚动:', scrollWrap.scrollHeight);

        // 双保险：再次延迟滚动确保完全滚动到底部
        setTimeout(() => {
          scrollWrap.scrollTop = scrollWrap.scrollHeight;
          console.log('第二次尝试滚动:', scrollWrap.scrollHeight);
        }, 100);
      }
    } catch (e) {
      console.error('滚动失败', e);
    }
  }, 50);
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return

  try {
    console.log('发送消息内容:', inputMessage.value);
    console.log('当前用户角色:', chatStore.userRole, '是否管理员:', isAdmin.value);

    const result = await chatStore.sendMessage({
      content: inputMessage.value,
      messageType: '0'
    })

    console.log('消息发送结果:', result);

    // 清空输入框
    inputMessage.value = ''

    // 强制延时确保在消息发送后滚动到底部
    setTimeout(() => {
      scrollToBottom()
    }, 300)
  } catch (error) {
    console.error('发送消息失败', error)
    ElMessage.error('发送消息失败')
  }
}

// 处理Enter键按下事件
const handleEnterPress = (e) => {
  // 阻止默认行为
  e.preventDefault()
  // 发送消息
  sendMessage()
}

// 处理图片选择
const handleImageSelect = async (file) => {
  if (!file) return

  try {
    // 获取接收者ID
    const receiverId = chatStore.userRole === 'consultant'
      ? currentConversation.value.userId
      : currentConversation.value.consultantId

    // 上传图片
    const response = await sendImage(file.raw, receiverId)

    if (response.code === 200) {
      // 更新消息列表
      chatStore.messages.push(response.data)
      // 强制延时确保滚动到底部
      setTimeout(() => {
        scrollToBottom()
      }, 300)
    } else {
      ElMessage.error('发送图片失败')
    }
  } catch (error) {
    ElMessage.error('发送图片失败')
  }
}

// 处理文件选择
const handleFileSelect = async (file) => {
  if (!file) return

  try {
    // 获取接收者ID
    const receiverId = chatStore.userRole === 'consultant'
      ? currentConversation.value.userId
      : currentConversation.value.consultantId

    // 上传文件
    const response = await sendFile(file.raw, receiverId)

    if (response.code === 200) {
      // 更新消息列表
      chatStore.messages.push(response.data)
      // 强制延时确保滚动到底部
      setTimeout(() => {
        scrollToBottom()
      }, 300)
    } else {
      ElMessage.error('发送文件失败')
    }
  } catch (error) {
    ElMessage.error('发送文件失败')
  }
}

// 下载文件
const downloadFile = (url) => {
  if (!url) return
  window.open(url, '_blank')
}

// 撤回消息
const withdrawMsg = async (messageId) => {
  try {
    const confirmed = await ElMessageBox.confirm('确定要撤回这条消息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (confirmed) {
      const result = await chatStore.withdrawMessageAction(messageId)
      if (result) {
        ElMessage.success('消息已撤回')
      } else {
        // ElMessage.error('撤回消息失败')
      }
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 在组件销毁前关闭WebSocket连接
onBeforeUnmount(() => {
  chatStore.closeWebSocket()
  document.removeEventListener('chat-messages-loaded', scrollToBottom)
  window.removeEventListener('websocket-message-received', scrollToBottom)
  window.removeEventListener('beforeunload', null)

  // 确保在离开页面前标记当前会话已读
  if (chatStore.currentConversation?.conversationId) {
    console.log('组件卸载，标记当前会话已读');
    const isUser = chatStore.userRole === 'user';
    markSessionRead(chatStore.currentConversation.conversationId, isUser).catch(error => {
      console.error('离开页面标记会话已读失败', error);
    });
  }
})

// 监听消息列表变化
watch(() => messages.value, (newVal, oldVal) => {
  // 加载更多消息时不触发滚动
  if (chatStore.isLoadingMore) {
    return;
  }

  // 消息新增或初始加载时才滚动到底部
  if (newVal.length > 0 && (oldVal.length === 0 || newVal.length > oldVal.length)) {
    // console.log('检测到消息列表变化，消息数量:', oldVal.length, '->', newVal.length);
    scrollToBottom();
  }
}, { deep: true });

// 额外监听最后一条消息的变化
watch(() => {
  // 当有消息时，监听最后一条消息
  if (messages.value && messages.value.length > 0) {
    return messages.value[messages.value.length - 1];
  }
  return null;
}, (newLastMsg, oldLastMsg) => {
  if (newLastMsg && (!oldLastMsg || newLastMsg.messageId !== oldLastMsg.messageId)) {
    console.log('检测到新的最后一条消息:', newLastMsg);
    scrollToBottom();
  }
}, { deep: true });

// 判断是否应该显示时间
const shouldShowTime = (currentMessage) => {
  if (!messages.value || messages.value.length === 0) return true;

  // 找到当前消息在整个消息列表中的索引
  const currentIndex = messages.value.findIndex(msg => msg.messageId === currentMessage.messageId);

  // 如果是第一条消息，显示时间
  if (currentIndex === 0) return true;

  // 获取前一条消息
  const prevMessage = messages.value[currentIndex - 1];

  // 如果找不到前一条消息，显示时间
  if (!prevMessage) return true;

  // 计算时间差
  const currentTime = new Date(currentMessage.sendTime).getTime();
  const prevTime = new Date(prevMessage.sendTime).getTime();
  const tenMinutes = 10 * 60 * 1000;

  // 只有时间间隔超过10分钟才显示时间，不考虑发送者变化
  return (currentTime - prevTime) > tenMinutes;
};

// 按日期对消息进行分组
const groupedMessages = computed(() => {
  if (!messages.value || messages.value.length === 0) return [];

  const groups = [];
  let currentDate = '';
  let currentGroup = null;

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  messages.value.forEach(message => {
    const messageDate = formatDate(message.sendTime, 'YYYY-MM-DD');
    const formattedDate = formatMessageDate(message.sendTime);
    const date = new Date(message.sendTime);
    const messageDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const isToday = messageDay.getTime() === today.getTime();

    if (messageDate !== currentDate) {
      currentDate = messageDate;
      currentGroup = {
        date: formattedDate,
        messages: [],
        isToday: isToday
      };
      groups.push(currentGroup);
    }

    currentGroup.messages.push(message);
  });

  return groups;
});

// 格式化时间显示
const formatTimeDisplay = (timestamp, isToday, dateLabel) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const timeStr = `${hours}:${minutes}`;

  // 今天的消息只显示时间
  if (isToday) {
    return timeStr;
  }

  // 之前的消息显示日期标签和时间
  return `${dateLabel} ${timeStr}`;
};

// 格式化消息日期显示
const formatMessageDate = (timestamp) => {
  if (!timestamp) return '';

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const date = new Date(timestamp);
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  // 今天
  if (messageDate.getTime() === today.getTime()) {
    return '今天';
  }

  // 昨天
  if (messageDate.getTime() === yesterday.getTime()) {
    return '昨天';
  }

  // 本周内
  const daysDiff = Math.floor((today - messageDate) / (24 * 60 * 60 * 1000));
  if (daysDiff < 7) {
    const weekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return weekday[date.getDay()];
  }

  // 本年内
  if (date.getFullYear() === now.getFullYear()) {
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  }

  // 更早的消息
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
};

// 格式化时间
const formatTimeMessage = (timestamp, type = 'time') => {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${hours}:${minutes}`;
};
</script>

<style scoped lang="scss">
.chat-container {
  height: calc(100vh - 120px);
}

.box-card {
  height: 100%;
  background-color: var(--el-bg-color);

  :deep .el-card__body {
    padding-right: 0 !important;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--el-text-color-primary);
}

.chat-main {
  display: flex;
  height: calc(100vh - 180px);
}

.conversation-list {
  width: 300px;
  border-right: 1px solid var(--el-border-color-lighter);
  padding: 10px;
  background-color: var(--el-bg-color);
}

.conversation-item {
  display: flex;
  padding: 12px 12px 10px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 5px;
  min-height: 60px;
  transition: background-color 0.3s;

  &:hover {
    background-color: var(--el-fill-color-light);
  }

  &.active {
    background-color: var(--el-color-primary-light-8);

    .name,
    .last-message,
    .time {
      color: var(--el-color-primary);
    }
  }
}

.avatar {
  position: relative;
  margin-right: 10px;
  display: flex;
  align-items: flex-start;
  margin-top: 2px;
}

.conversation-info {
  flex: 1;
  overflow: hidden;
  position: relative;

  .badge {
    position: absolute;
    bottom: -5px;
    right: 0;
  }
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.name {
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  line-height: 1.3;
  max-height: 2.6em;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: var(--el-text-color-primary);
}

.time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.last-message {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--el-text-color-regular);
  font-size: 13px;
  width: calc(100% - 50px);
  position: relative;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 40px);
  background-color: var(--el-bg-color);
}

.chat-header {
  padding: 15px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--el-text-color-primary);
}

.admin-tip {
  font-size: 12px;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  padding-right: 0;
}

.message-item {
  display: flex;
  margin-bottom: 15px;
  width: 100%;
  clear: both;
}

.message-avatar {
  margin-right: 10px;
  flex-shrink: 0;
  position: relative;
}

.admin-tag {
  display: none;
}

.admin-tag-top {
  margin-bottom: 4px;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  width: fit-content;
  position: relative;
}

.message-self {
  flex-direction: row-reverse;
  padding-right: 15px;

  .message-content {
    align-items: flex-end;
  }

  .admin-tag-top {
    text-align: right;
  }

  .message-avatar {
    margin-right: 0;
    margin-left: 10px;
  }

  .message-bubble {
    background-color: var(--el-color-primary-light-8);
    color: var(--el-color-primary-dark-2);
  }
}

.message-bubble {
  background-color: var(--el-fill-color-light);
  padding: 10px;
  border-radius: 6px;
  word-break: break-word;
  box-sizing: border-box;
  width: fit-content;
  max-width: 100%;
  white-space: pre-wrap;
  color: var(--el-text-color-primary);
  border: 1px solid var(--el-border-color-lighter);
}

.text-bubble {
  display: inline-block;
}

.message-withdrawn .message-bubble {
  background-color: var(--el-fill-color);
}

.withdrawn-text {
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.image-content {
  display: inline-block;
  max-width: 100%;
}

.image-content img {
  max-width: 250px;
  border-radius: 4px;
  display: block;
}

.file-content {
  display: inline-block;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.message-input {
  padding: 15px;
  border-top: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color);
}

.input-toolbar {
  display: flex;
  margin-bottom: 10px;
}

.input-area {
  display: flex;
}

.input-area .el-button {
  margin-left: 10px;
  align-self: flex-end;
}

.no-conversation {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.load-more {
  text-align: center;
  padding: 10px;
  color: var(--el-color-primary);
  cursor: pointer;
}

.empty-tip {
  text-align: center;
  padding: 20px;
  color: var(--el-text-color-secondary);
}

.el-loading-mask {
  z-index: 10;
}

.time-divider {
  text-align: center;
  margin: 10px 0;
  padding: 5px 0;
  clear: both;

  span {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    background-color: var(--el-fill-color-light);
    padding: 2px 8px;
    border-radius: 3px;
    display: inline-block;
  }
}

.message-sender {
  display: none !important;
}
</style>
