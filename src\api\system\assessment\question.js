import request from '@/utils/request'

// 查询题目列表
export function listQuestion(query) {
  return request({
    url: '/system/assessment/question/list',
    method: 'get',
    params: query
  })
}

// 查询题目详细
export function getQuestion(id) {
  return request({
    url: '/system/assessment/question/' + id,
    method: 'get'
  })
}

// 新增题目
export function addQuestion(data) {
  return request({
    url: '/system/assessment/question',
    method: 'post',
    data: data
  })
}

// 修改题目
export function updateQuestion(data) {
  return request({
    url: '/system/assessment/question',
    method: 'put',
    data: data
  })
}

// 删除题目
export function delQuestion(ids) {
  return request({
    url: '/system/assessment/question/' + ids,
    method: 'delete'
  })
}

// 批量更新题目排序
export function updateQuestionOrder(data) {
  return request({
    url: '/system/assessment/question/updateOrder',
    method: 'put',
    data: data
  })
}

// 复制题目
export function copyQuestion(id) {
  return request({
    url: '/system/assessment/question/copy/' + id,
    method: 'post'
  })
}

// 导出题目
export function exportQuestion(query) {
  return request({
    url: '/system/assessment/question/export',
    method: 'post',
    params: query
  })
}

// 根据量表ID查询题目
export function getQuestionsByScale(scaleId) {
  return request({
    url: '/system/assessment/question/scale/' + scaleId,
    method: 'get'
  })
}
