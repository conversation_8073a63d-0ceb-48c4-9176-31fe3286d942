import request from '@/utils/request'

// 查询题目列表
export function listQuestion(params) {
  return request({
    url: '/system/question/list',
    method: 'get',
    params
  })
}

// 新增题目
export function addQuestion(data) {
  return request({
    url: '/system/question',
    method: 'post',
    data
  })
}

// 修改题目
export function updateQuestion(data) {
  return request({
    url: '/system/question',
    method: 'put',
    data
  })
}

// 删除题目
export function delQuestion(ids) {
  return request({
    url: `/system/question/${ids}`,
    method: 'delete'
  })
}

// 获取题目详情
export function getQuestion(id) {
  return request({
    url: `/system/question/${id}`,
    method: 'get'
  })
}

// api/question.js
export function listWithOptions() {
  return request({
    url: '/system/question/listWithOptions',
    method: 'get',

  })
}