import request from '@/utils/request'

// 查询选项列表
export function listOption(query) {
  return request({
    url: '/system/assessment/option/list',
    method: 'get',
    params: query
  })
}

// 查询选项详细
export function getOption(id) {
  return request({
    url: '/system/assessment/option/' + id,
    method: 'get'
  })
}

// 新增选项
export function addOption(data) {
  return request({
    url: '/system/assessment/option',
    method: 'post',
    data: data
  })
}

// 修改选项
export function updateOption(data) {
  return request({
    url: '/system/assessment/option',
    method: 'put',
    data: data
  })
}

// 删除选项
export function delOption(ids) {
  return request({
    url: '/system/assessment/option/' + ids,
    method: 'delete'
  })
}

// 批量更新选项排序
export function updateOptionOrder(data) {
  return request({
    url: '/system/assessment/option/updateOrder',
    method: 'put',
    data: data
  })
}

// 根据题目ID查询选项
export function getOptionsByQuestion(questionId) {
  return request({
    url: '/system/assessment/option/question/' + questionId,
    method: 'get'
  })
}
