import request from '@/utils/request'

// 查询课程列表
export function listCourse(query) {
  return request({
    url: '/system/course/list',
    method: 'get',
    params: query
  })
}

// 导出课程列表
export function exportCourse(query) {
  return request({
    url: '/system/course/export',
    method: 'post',
    params: query
  })
}

// 获取课程详细信息
export function getCourse(id) {
  return request({
    url: `/system/course/${id}`,
    method: 'get'
  })
}

// 获取课程详细信息（包含章节、讲师等）
export function getCourseDetails(id) {
  return request({
    url: `/system/course/details/${id}`,
    method: 'get'
  })
}

// 新增课程
export function addCourse(data) {
  return request({
    url: '/system/course',
    method: 'post',
    data: data
  })
}

// 修改课程
export function updateCourse(data) {
  return request({
    url: '/system/course',
    method: 'put',
    data: data
  })
}

// 删除课程
export function delCourse(ids) {
  return request({
    url: `/system/course/${ids}`,
    method: 'delete'
  })
}

// 发布课程
export function publishCourse(id) {
  return request({
    url: `/system/course/publish/${id}`,
    method: 'put'
  })
}

// 下架课程
export function unpublishCourse(id) {
  return request({
    url: `/system/course/unpublish/${id}`,
    method: 'put'
  })
}

// 更新课程统计信息
export function updateCourseStatistics(id) {
  return request({
    url: `/system/course/updateStatistics/${id}`,
    method: 'put'
  })
}

// 更新课程评分信息
export function updateCourseRating(id) {
  return request({
    url: `/system/course/updateRating/${id}`,
    method: 'put'
  })
}

// 根据分类ID查询课程列表
export function getCoursesByCategory(categoryId) {
  return request({
    url: `/system/course/category/${categoryId}`,
    method: 'get'
  })
}

// 根据讲师ID查询课程列表
export function getCoursesByInstructor(instructorId) {
  return request({
    url: `/system/course/instructor/${instructorId}`,
    method: 'get'
  })
}
