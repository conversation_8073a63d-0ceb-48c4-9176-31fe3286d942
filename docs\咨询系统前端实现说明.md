# 咨询系统前端实现说明

## 概述
基于后端提供的咨询相关Controller接口，完成了咨询系统前端功能的实现，包括咨询记录管理、咨询师评价管理和咨询中断记录管理三个核心模块。

## 系统架构

### 文件组织结构
```
src/
├── api/wechat/consultation/          # 咨询系统API接口
│   ├── record.js                     # 咨询记录接口
│   ├── review.js                     # 咨询师评价接口
│   └── interruption.js               # 咨询中断记录接口
└── views/wechat/consultation/        # 咨询系统页面
    ├── record/                       # 咨询记录管理
    │   └── index.vue
    ├── review/                       # 咨询师评价管理
    │   └── index.vue
    └── interruption/                 # 咨询中断记录管理
        └── index.vue
```

## 功能模块详解

### 1. 咨询记录管理 (record/index.vue)

#### 核心功能
- ✅ **基础CRUD操作**：新增、修改、删除、查询咨询记录
- ✅ **咨询流程管理**：开始咨询、结束咨询、中断咨询、恢复咨询
- ✅ **评价功能**：用户评价咨询服务
- ✅ **数据导出**：支持Excel格式导出
- ✅ **详情查看**：完整的咨询记录详情展示

#### 特色功能
```javascript
// 咨询状态管理
const consultStatus = {
  0: "待开始",
  1: "进行中", 
  2: "已完成",
  3: "已中断",
  4: "已取消"
};

// 咨询类型支持
const consultType = {
  "online": "在线咨询",
  "phone": "电话咨询", 
  "offline": "面对面咨询"
};
```

#### 操作流程
1. **开始咨询**：通过订单ID启动咨询会话
2. **咨询进行**：实时记录咨询过程
3. **中断处理**：支持手动中断和自动恢复
4. **结束咨询**：记录咨询总结和时长
5. **用户评价**：1-5星评分系统

### 2. 咨询师评价管理 (review/index.vue)

#### 核心功能
- ✅ **评价管理**：查看、编辑、删除用户评价
- ✅ **审核流程**：待审核、已通过、已拒绝状态管理
- ✅ **咨询师回复**：支持咨询师对评价进行回复
- ✅ **统计分析**：评分分布、平均评分等统计信息
- ✅ **匿名评价**：支持匿名评价功能

#### 审核工作流
```javascript
// 审核状态
const auditStatus = {
  0: "待审核",
  1: "已通过",
  2: "已拒绝"
};

// 审核操作
function handleAudit(row) {
  // 显示审核对话框
  // 管理员选择通过/拒绝
  // 可添加审核意见
}
```

#### 统计功能
- **评分分布**：1-5星评价数量统计
- **平均评分**：咨询师整体评分
- **评价趋势**：时间维度的评价变化
- **回复率**：咨询师回复评价的比例

### 3. 咨询中断记录管理 (interruption/index.vue)

#### 核心功能
- ✅ **中断记录管理**：记录所有咨询中断事件
- ✅ **中断类型分类**：手动、网络、系统、用户离开、咨询师离开
- ✅ **时长统计**：中断时长的精确记录和统计
- ✅ **多维度分析**：总体统计、按记录统计、按类型统计

#### 中断类型定义
```javascript
const interruptTypes = {
  "manual": "手动中断",
  "network": "网络中断",
  "system": "系统中断", 
  "user_leave": "用户离开",
  "consultant_leave": "咨询师离开"
};
```

#### 统计分析功能
1. **总体统计**
   - 总中断次数和时长
   - 各类型中断分布
   - 平均中断时长

2. **按记录统计**
   - 特定咨询记录的中断情况
   - 中断频率分析

3. **按类型统计**
   - 特定类型中断的详细数据
   - 占比和趋势分析

## API接口设计

### 1. 咨询记录接口 (record.js)

#### 基础CRUD接口
```javascript
// 查询列表
listConsultationRecord(query)
// 获取详情
getConsultationRecord(id)
// 新增记录
addConsultationRecord(data)
// 修改记录
updateConsultationRecord(data)
// 删除记录
delConsultationRecord(ids)
```

#### 业务流程接口
```javascript
// 开始咨询
startConsultation(orderId)
// 结束咨询
endConsultation(recordId, consultContent)
// 中断咨询
interruptConsultation(recordId, interruptType, reason)
// 恢复咨询
resumeConsultation(recordId)
// 用户评价
rateConsultation(recordId, userRating)
```

#### 统计查询接口
```javascript
// 用户咨询统计
getUserConsultationStats(userId)
// 咨询师咨询统计
getConsultantConsultationStats(consultantId)
// 咨询历史记录
getConsultationHistory(userId, consultantId)
```

### 2. 咨询师评价接口 (review.js)

#### 评价管理接口
```javascript
// 基础CRUD
listConsultantReview(query)
getConsultantReview(id)
addConsultantReview(data)
updateConsultantReview(data)
delConsultantReview(ids)
```

#### 业务流程接口
```javascript
// 审核评价
auditConsultantReview(id, adminCheck)
// 咨询师回复
replyConsultantReview(id, consultantReply)
// 待审核列表
getPendingConsultantReviews()
// 已通过列表
getApprovedConsultantReviews(consultantId)
```

#### 统计接口
```javascript
// 评价统计
getConsultantReviewStatistics(consultantId)
// 更新评分
updateConsultantRating(consultantId)
```

### 3. 咨询中断记录接口 (interruption.js)

#### 基础管理接口
```javascript
// CRUD操作
listConsultantInterruption(query)
getConsultantInterruption(id)
addConsultantInterruption(data)
updateConsultantInterruption(data)
delConsultantInterruption(ids)
```

#### 统计分析接口
```javascript
// 按记录查询中断
getConsultantInterruptionsByRecord(recordId)
// 统计中断次数
countConsultantInterruptions(recordId)
// 统计中断时长
sumConsultantInterruptionDuration(recordId)
// 按类型统计
countConsultantInterruptionsByType(recordId, interruptType)
// 综合统计
getConsultantInterruptionStats(recordId)
```

## 用户界面设计

### 1. 列表页面设计
- **搜索筛选**：支持多条件组合搜索
- **批量操作**：支持批量删除等操作
- **状态标识**：使用颜色标签区分不同状态
- **操作按钮**：根据权限动态显示操作按钮

### 2. 表单设计
- **分步骤表单**：复杂表单采用分步骤设计
- **实时验证**：表单字段实时验证
- **智能提示**：提供输入建议和帮助信息
- **数据回显**：编辑时正确回显数据

### 3. 对话框设计
- **嵌套对话框**：支持多层对话框嵌套
- **响应式布局**：适配不同屏幕尺寸
- **加载状态**：显示数据加载状态
- **错误处理**：友好的错误提示

## 权限控制

### 权限点定义
```javascript
// 咨询记录权限
'system:consultationRecord:list'    // 查看列表
'system:consultationRecord:query'   // 查看详情
'system:consultationRecord:add'     // 新增记录
'system:consultationRecord:edit'    // 修改记录
'system:consultationRecord:remove'  // 删除记录
'system:consultationRecord:export'  // 导出数据

// 咨询师评价权限
'system:consultantReview:list'      // 查看列表
'system:consultantReview:query'     // 查看详情
'system:consultantReview:add'       // 新增评价
'system:consultantReview:edit'      // 修改评价
'system:consultantReview:remove'    // 删除评价
'system:consultantReview:audit'     // 审核评价
'system:consultantReview:reply'     // 回复评价

// 咨询中断记录权限
'system:consultantInterruption:list'    // 查看列表
'system:consultantInterruption:query'   // 查看详情
'system:consultantInterruption:add'     // 新增记录
'system:consultantInterruption:edit'    // 修改记录
'system:consultantInterruption:remove'  // 删除记录
```

### 权限控制实现
```vue
<!-- 按钮权限控制 -->
<el-button v-hasPermi="['system:consultationRecord:add']" @click="handleAdd">
  新增
</el-button>

<!-- 操作列权限控制 -->
<el-button v-hasPermi="['system:consultationRecord:edit']" @click="handleUpdate">
  修改
</el-button>
```

## 数据字典配置

### 需要配置的字典类型
```javascript
// 咨询状态
sys_consult_status: {
  0: "待开始",
  1: "进行中", 
  2: "已完成",
  3: "已中断",
  4: "已取消"
}

// 咨询类型
sys_consult_type: {
  "online": "在线咨询",
  "phone": "电话咨询",
  "offline": "面对面咨询"
}

// 审核状态
sys_audit_status: {
  0: "待审核",
  1: "已通过",
  2: "已拒绝"
}

// 中断类型
sys_interrupt_type: {
  "manual": "手动中断",
  "network": "网络中断", 
  "system": "系统中断",
  "user_leave": "用户离开",
  "consultant_leave": "咨询师离开"
}

// 是否选项
sys_yes_no: {
  0: "否",
  1: "是"
}
```

## 技术特点

### 1. 响应式设计
- 使用Vue 3 Composition API
- 响应式数据管理
- 组件化开发

### 2. 状态管理
- 使用reactive和ref管理状态
- 统一的数据流管理
- 组件间通信

### 3. 表单处理
- Element Plus表单组件
- 统一的验证规则
- 错误处理机制

### 4. 接口调用
- 统一的API封装
- 错误处理和重试机制
- 加载状态管理

## 后续优化建议

### 1. 功能增强
- 实时通信功能（WebSocket）
- 文件上传和预览
- 消息推送功能
- 移动端适配

### 2. 性能优化
- 虚拟滚动（大数据量）
- 懒加载和分页优化
- 缓存策略
- 代码分割

### 3. 用户体验
- 操作引导和帮助
- 快捷键支持
- 主题切换
- 国际化支持

### 4. 数据可视化
- 统计图表展示
- 实时数据监控
- 报表生成
- 数据导出多格式支持

## 总结

咨询系统前端实现完成了以下核心功能：

1. **完整的业务流程**：从咨询预约到结束评价的完整流程
2. **丰富的管理功能**：支持多维度的数据管理和统计分析
3. **友好的用户界面**：响应式设计，操作简便
4. **完善的权限控制**：细粒度的权限管理
5. **可扩展的架构**：模块化设计，便于后续扩展

系统已具备投入使用的基本条件，可根据实际需求进行进一步的功能完善和优化。
