# 前端路由组件路径配置

## 文件结构重新组织说明

为了更好地组织代码结构，为后续的冥想和抗逆力功能做准备，已将课程相关的所有功能整理到统一的文件夹下。

## 当前文件结构

```
src/views/wechat/
├── course/                    # 课程管理系统
│   ├── course/               # 课程管理
│   │   └── index.vue
│   ├── chapter/              # 章节管理
│   │   └── index.vue
│   ├── instructor/           # 讲师管理
│   │   └── index.vue
│   ├── order/                # 订单管理
│   │   └── index.vue
│   └── review/               # 评价管理
│       └── index.vue
├── meditation/               # 冥想系统（已完成）
│   ├── meditation/           # 冥想管理
│   ├── order/                # 冥想订单管理
│   └── review/               # 冥想评价管理
└── resilience/               # 抗逆力系统
    ├── assessment/           # 抗逆力评估
    ├── training/             # 抗逆力训练
    └── report/               # 抗逆力报告
```

## 路由组件路径配置

### 课程管理系统

| 功能模块 | 路由路径 | 组件路径 | 菜单权限 |
|---------|---------|----------|----------|
| 课程管理 | `/course-system/course` | `wechat/course/course/index` | `system:course:list` |
| 章节管理 | `/course-system/chapter` | `wechat/course/chapter/index` | `system:chapter:list` |
| 讲师管理 | `/course-system/instructor` | `wechat/course/instructor/index` | `system:instructor:list` |
| 订单管理 | `/course-system/order` | `wechat/course/order/index` | `system:order:list` |
| 评价管理 | `/course-system/review` | `wechat/course/review/index` | `system:review:list` |

### 冥想系统（已完成）

| 功能模块 | 路由路径 | 组件路径 | 菜单权限 |
|---------|---------|----------|----------|
| 冥想管理 | `/meditation-system/meditation` | `wechat/meditation/meditation/index` | `system:meditation:list` |
| 冥想订单管理 | `/meditation-system/meditation-order` | `wechat/meditation/order/index` | `system:meditationOrder:list` |
| 冥想评价管理 | `/meditation-system/meditation-review` | `wechat/meditation/review/index` | `system:meditationReview:list` |

### 抗逆力系统

| 功能模块 | 路由路径 | 组件路径 | 菜单权限 |
|---------|---------|----------|----------|
| 抗逆力评估 | `/resilience-system/assessment` | `wechat/resilience/assessment/index` | `system:resilience:assessment:list` |
| 抗逆力训练 | `/resilience-system/training` | `wechat/resilience/training/index` | `system:resilience:training:list` |
| 抗逆力报告 | `/resilience-system/report` | `wechat/resilience/report/index` | `system:resilience:report:list` |

## API文件路径

### 课程管理系统API

```
src/api/wechat/
├── course.js                 # 课程API
├── chapter.js                # 章节API
├── instructor.js             # 讲师API
├── order.js                  # 订单API
└── review.js                 # 评价API
```

### 冥想系统API（已完成）

```
src/api/wechat/meditation/
├── meditation.js             # 冥想API
├── order.js                  # 冥想订单API
└── review.js                 # 冥想评价API
```

### 抗逆力系统API

```
src/api/wechat/
├── resilience/
│   ├── assessment.js         # 抗逆力评估API
│   ├── training.js           # 抗逆力训练API
│   └── report.js             # 抗逆力报告API
```

## 菜单配置建议

### 主菜单结构

```
心理健康管理系统
├── 课程管理系统 (course-system)
│   ├── 课程管理 (course)
│   ├── 章节管理 (chapter)
│   ├── 讲师管理 (instructor)
│   ├── 订单管理 (order)
│   └── 评价管理 (review)
├── 冥想系统 (meditation-system)
│   ├── 冥想管理 (meditation)
│   ├── 冥想订单管理 (meditation-order)
│   └── 冥想评价管理 (meditation-review)
└── 抗逆力系统 (resilience-system)
    ├── 抗逆力评估 (assessment)
    ├── 抗逆力训练 (training)
    └── 抗逆力报告 (report)
```

## 组织架构优势

### 1. **模块化管理**
- 每个系统独立管理，便于维护
- 功能模块清晰分离，降低耦合度
- 便于团队协作开发

### 2. **扩展性强**
- 新增功能模块时结构清晰
- 便于后续功能的快速开发
- 支持模块级别的权限控制

### 3. **维护便利**
- 文件路径规范统一
- 便于定位和修改代码
- 支持模块级别的版本管理

### 4. **用户体验**
- 菜单结构层次清晰
- 功能分类合理
- 便于用户快速找到所需功能

## 注意事项

1. **路径一致性**：确保菜单配置中的component路径与实际文件路径完全一致
2. **权限配置**：每个功能模块都需要配置相应的权限标识
3. **命名规范**：文件夹和文件命名保持一致的风格
4. **API组织**：API文件的组织结构应与页面组件保持对应关系

## 后续开发建议

1. **冥想系统**：✅ 已完成冥想管理、订单管理、评价管理
2. **抗逆力系统**：建议先开发抗逆力评估和训练模块
3. **数据关联**：注意各系统间的数据关联关系设计
4. **权限设计**：提前规划好各系统的权限控制策略

这种组织结构为后续的系统扩展提供了良好的基础，确保了代码的可维护性和可扩展性。
