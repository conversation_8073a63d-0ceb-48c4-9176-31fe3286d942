-- 咨询系统菜单配置SQL脚本
-- 注意：执行前请确认sys_menu表的结构和当前最大menu_id

-- 1. 插入一级菜单：咨询系统
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询系统', 0, 6, 'consultation', NULL, '', 1, 0, 'M', '0', '0', '', 'message', 'admin', sysdate(), 'admin', sysdate(), '咨询系统菜单');

-- 获取咨询系统菜单ID（假设为1100，实际使用时需要查询获取）
SET @consultation_menu_id = LAST_INSERT_ID();

-- 2. 插入二级菜单：咨询记录
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询记录', @consultation_menu_id, 1, 'record', 'wechat/consultation/record/index', '', 1, 0, 'C', '0', '0', 'system:consultationRecord:list', 'list', 'admin', sysdate(), 'admin', sysdate(), '咨询记录菜单');

SET @record_menu_id = LAST_INSERT_ID();

-- 3. 插入二级菜单：咨询师评价
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询师评价', @consultation_menu_id, 2, 'review', 'wechat/consultation/review/index', '', 1, 0, 'C', '0', '0', 'system:consultantReview:list', 'star', 'admin', sysdate(), 'admin', sysdate(), '咨询师评价菜单');

SET @review_menu_id = LAST_INSERT_ID();

-- 4. 插入二级菜单：中断记录
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('中断记录', @consultation_menu_id, 3, 'interruption', 'wechat/consultation/interruption/index', '', 1, 0, 'C', '0', '0', 'system:consultantInterruption:list', 'warning', 'admin', sysdate(), 'admin', sysdate(), '中断记录菜单');

SET @interruption_menu_id = LAST_INSERT_ID();

-- 5. 插入咨询记录权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('咨询记录查询', @record_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:consultationRecord:query', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询记录新增', @record_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:consultationRecord:add', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询记录修改', @record_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:consultationRecord:edit', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询记录删除', @record_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:consultationRecord:remove', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询记录导出', @record_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:consultationRecord:export', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 6. 插入咨询师评价权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('咨询师评价查询', @review_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantReview:query', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询师评价新增', @review_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantReview:add', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询师评价修改', @review_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantReview:edit', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询师评价删除', @review_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantReview:remove', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询师评价导出', @review_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantReview:export', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询师评价审核', @review_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantReview:audit', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('咨询师评价回复', @review_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantReview:reply', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 7. 插入中断记录权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('中断记录查询', @interruption_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantInterruption:query', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('中断记录新增', @interruption_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantInterruption:add', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('中断记录修改', @interruption_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantInterruption:edit', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('中断记录删除', @interruption_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantInterruption:remove', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('中断记录导出', @interruption_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantInterruption:export', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 8. 插入数据字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
('咨询状态', 'sys_consult_status', '0', 'admin', sysdate(), 'admin', sysdate(), '咨询状态列表'),
('咨询类型', 'sys_consult_type', '0', 'admin', sysdate(), 'admin', sysdate(), '咨询类型列表'),
('审核状态', 'sys_audit_status', '0', 'admin', sysdate(), 'admin', sysdate(), '审核状态列表'),
('中断类型', 'sys_interrupt_type', '0', 'admin', sysdate(), 'admin', sysdate(), '中断类型列表');

-- 9. 插入数据字典数据
-- 咨询状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '待开始', '0', 'sys_consult_status', '', 'info', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '咨询待开始状态'),
(2, '进行中', '1', 'sys_consult_status', '', 'primary', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '咨询进行中状态'),
(3, '已完成', '2', 'sys_consult_status', '', 'success', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '咨询已完成状态'),
(4, '已中断', '3', 'sys_consult_status', '', 'warning', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '咨询已中断状态'),
(5, '已取消', '4', 'sys_consult_status', '', 'danger', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '咨询已取消状态');

-- 咨询类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '在线咨询', 'online', 'sys_consult_type', '', 'primary', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '在线咨询类型'),
(2, '电话咨询', 'phone', 'sys_consult_type', '', 'success', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '电话咨询类型'),
(3, '面对面咨询', 'offline', 'sys_consult_type', '', 'info', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '面对面咨询类型');

-- 审核状态
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '待审核', '0', 'sys_audit_status', '', 'info', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '待审核状态'),
(2, '已通过', '1', 'sys_audit_status', '', 'success', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '审核通过状态'),
(3, '已拒绝', '2', 'sys_audit_status', '', 'danger', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '审核拒绝状态');

-- 中断类型
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '手动中断', 'manual', 'sys_interrupt_type', '', 'primary', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '手动中断类型'),
(2, '网络中断', 'network', 'sys_interrupt_type', '', 'warning', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '网络中断类型'),
(3, '系统中断', 'system', 'sys_interrupt_type', '', 'danger', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '系统中断类型'),
(4, '用户离开', 'user_leave', 'sys_interrupt_type', '', 'info', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '用户离开类型'),
(5, '咨询师离开', 'consultant_leave', 'sys_interrupt_type', '', 'info', 'N', '0', 'admin', sysdate(), 'admin', sysdate(), '咨询师离开类型');

-- 提交事务
COMMIT;

-- 查询验证
SELECT menu_id, menu_name, parent_id, path, component, perms FROM sys_menu WHERE menu_name LIKE '%咨询%' ORDER BY parent_id, order_num;
