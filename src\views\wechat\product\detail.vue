<template>
  <div class="app-container">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="form.productName" placeholder="请输入产品名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属分类" prop="categoryIds">
            <el-tree-select v-model="form.categoryIds" :data="categoryOptions"
              :props="{ label: 'categoryName', value: 'categoryId', children: 'children' }" value-key="categoryId"
              placeholder="请选择分类" clearable multiple style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品图片" prop="productImage">
            <el-upload class="avatar-uploader" :action="uploadImgUrl" :show-file-list="false"
              :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <img v-if="form.productImage" :src="form.productImage" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务方式" prop="serviceMethod">
            <el-select v-model="form.serviceMethod" multiple placeholder="请选择服务方式" style="width: 100%">
              <el-option v-for="item in psy_service_type" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务保障" prop="serviceGuarantee">
            <el-select v-model="form.serviceGuarantee" multiple placeholder="请选择服务保障" style="width: 100%">
              <el-option v-for="item in psy_consulting_guarantee" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务方向" prop="serviceDirection">
            <el-select v-model="form.serviceDirection" placeholder="请选择服务方向" style="width: 100%"
              @change="handleServiceDirectionChange">
              <el-option v-for="item in psy_service_direction" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务方向类型" prop="serviceDirectionType">
            <el-select v-model="form.serviceDirectionType" placeholder="请选择服务方向类型" style="width: 100%">
              <el-option v-for="item in serviceDirectionTypeOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务时长" prop="serviceDuration">
            <el-select v-model="form.serviceDuration" placeholder="请选择服务时长" style="width: 100%">
              <el-option v-for="item in psy_service_duration" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="咨询师等级" prop="consultantGrade">
            <el-input v-model="form.consultantGrade" placeholder="请输入咨询师等级" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="原价" prop="originalPrice">
            <el-input-number v-model="form.originalPrice" :min="0" :precision="2" controls-position="right"
              style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="折扣价" prop="discountPrice">
            <el-input-number v-model="form.discountPrice" :min="0" :precision="2" controls-position="right"
              style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="折扣率" prop="discountRate">
            <el-input-number v-model="form.discountRate" :min="0" :max="1" :precision="2" controls-position="right"
              style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期" prop="validityPeriod">
            <el-input v-model="form.validityPeriod" placeholder="请输入有效期" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="适用年龄范围" prop="ageRange">
            <el-input v-model="form.ageRange" placeholder="请输入适用年龄范围" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单人购买上限" prop="singlePurchaseLimit">
            <el-input-number v-model="form.singlePurchaseLimit" :min="0" controls-position="right"
              style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需要预约" prop="needAppointment">
            <el-radio-group v-model="form.needAppointment">
              <el-radio :value="'0'">否</el-radio>
              <el-radio :value="'1'">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="disableFlag">
            <el-radio-group v-model="form.disableFlag">
              <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="补充说明" prop="graphicDetails">
            <el-input type="textarea" v-model="form.graphicDetails" :rows="4" placeholder="请输入补充说明" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="图文详情" prop="supplementInfo">
            <el-input type="textarea" v-model="form.supplementInfo" :rows="3" placeholder="请输入图文详情" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 服务项目列表 -->
      <el-divider content-position="left">服务项目</el-divider>
      <div v-for="(item, index) in form.serviceItems" :key="index" class="service-item-box">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="'项目名称'" :prop="'serviceItems.' + index + '.itemName'"
              :rules="{ required: true, message: '项目名称不能为空' }">
              <el-input v-model="item.itemName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="'项目分类'" :prop="'serviceItems.' + index + '.category'"
              :rules="{ required: true, message: '项目分类不能为空' }">
              <el-select v-model="item.category" placeholder="请选择分类" style="width: 100%">
                <el-option v-for="dict in psy_project_classification" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="'价格'" :prop="'serviceItems.' + index + '.price'"
              :rules="{ required: true, message: '价格不能为空' }">
              <el-input-number v-model="item.price" :min="0" :precision="2" controls-position="right"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="text-right">
            <el-button type="danger" icon="Delete" circle @click="removeServiceItem(index)" />
          </el-col>
        </el-row>
      </div>
      <el-button type="primary" plain icon="Plus" @click="addServiceItem">添加服务项目</el-button>

      <el-form-item class="mt-4">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { getCurrentInstance, ref, reactive } from 'vue'
import { getProduct, addProduct, updateProduct } from '@/api/wechat/product'
import { useRoute, useRouter } from 'vue-router'
import { listCategory } from '@/api/wechat/category'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()

const { sys_normal_disable, psy_project_classification, psy_service_type, psy_consulting_guarantee, psy_service_duration, psy_service_direction, psy_service_direction_type } = proxy.useDict(
  'sys_normal_disable',
  'psy_project_classification',
  'psy_service_type',
  'psy_consulting_guarantee',
  'psy_service_duration',
  'psy_service_direction',
  'psy_service_direction_type'
)

// 上传图片地址
const uploadImgUrl = import.meta.env.VITE_APP_BASE_API + "/common/upload"

const formRef = ref()
const categoryOptions = ref([])
const serviceDirectionTypeOptions = ref([])
const form = reactive({
  productId: undefined,
  categoryIds: [],
  productName: undefined,
  productImage: undefined,
  serviceMethod: [],
  serviceGuarantee: [],
  serviceDirection: undefined,
  serviceDirectionType: undefined,
  serviceDuration: undefined,
  consultantGrade: undefined,
  originalPrice: 0,
  discountPrice: 0,
  discountRate: 1,
  validityPeriod: undefined,
  graphicDetails: undefined,
  supplementInfo: undefined,
  needAppointment: '0',
  singlePurchaseLimit: undefined,
  ageRange: undefined,
  disableFlag: '0',
  serviceItems: []
})

const rules = {
  categoryIds: [{ required: true, message: '请选择所属分类', trigger: 'change' }],
  productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  serviceMethod: [{ required: true, message: '请选择服务方式', trigger: 'change' }],
  serviceDirection: [{ required: true, message: '请选择服务方向', trigger: 'change' }],
  serviceDirectionType: [{ required: true, message: '请选择服务方向类型', trigger: 'change' }],
  originalPrice: [{ required: true, message: '请输入原价', trigger: 'blur' }],
  discountPrice: [{ required: true, message: '请输入折扣价', trigger: 'blur' }],
  validityPeriod: [{ required: true, message: '请输入有效期', trigger: 'blur' }]
}

// 图片上传成功处理
const handleAvatarSuccess = (res) => {
  form.productImage = res.url
}

// 上传前校验
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    proxy.$message.error('上传头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    proxy.$message.error('上传头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 处理服务方向变化
const handleServiceDirectionChange = (value) => {
  // 清空当前选中的服务方向类型
  form.serviceDirectionType = undefined

  if (value) {
    // 根据选中的服务方向值获取对应的字典类型名称
    const selectedDirection = psy_service_direction.value.find(item => item.value === value)
    if (selectedDirection) {
      // 使用服务方向的字典值作为字典类型名称，加载服务方向类型选项
      proxy.getDicts(selectedDirection.value).then(response => {
        serviceDirectionTypeOptions.value = response.data
      })
    } else {
      serviceDirectionTypeOptions.value = []
    }
  } else {
    serviceDirectionTypeOptions.value = []
  }
}

// 添加服务项目
const addServiceItem = () => {
  form.serviceItems.push({
    itemName: '',
    category: '',
    price: 0,
    quantity: '1份',
    contents: []
  })
}

// 移除服务项目
const removeServiceItem = (index) => {
  form.serviceItems.splice(index, 1)
}

// 获取详情
const getDetail = async (id) => {
  try {
    const res = await getProduct(id)
    // 处理数组类型的字段
    const formData = {
      ...res.data,
      serviceMethod: res.data.serviceMethod ? res.data.serviceMethod.split(',').map(item => item.trim()) : [],
      serviceGuarantee: res.data.serviceGuarantee ? res.data.serviceGuarantee.split(',').map(item => item.trim()) : [],
      // 处理分类数据
      categoryIds: res.data.categories ? res.data.categories.map(cat => cat.categoryId) : []
    }
    // 使用处理后的数据更新表单
    Object.assign(form, formData)

    // 如果存在服务方向，则加载对应的服务方向类型选项
    if (form.serviceDirection) {
      handleServiceDirectionChange(form.serviceDirection)
    }

    // 打印日志以便调试
    console.log('分类数据:', {
      原始数据: res.data.categories,
      处理后: form.categoryIds
    })
  } catch (error) {
    console.error('获取详情失败:', error)
  }
}

// 获取分类列表
const getCategoryList = async () => {
  try {
    const res = await listCategory()
    categoryOptions.value = res.data
    // 打印日志以便调试
    console.log('分类选项数据:', categoryOptions.value)
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 构建提交数据
        const submitData = {
          ...form,
          serviceMethod: form.serviceMethod.join(','),
          serviceGuarantee: form.serviceGuarantee.join(','),
          categoryIds: Array.isArray(form.categoryIds) ? form.categoryIds.join(',') : form.categoryIds
        }

        // 打印日志以便调试
        console.log('提交的分类数据:', {
          原始数据: form.categoryIds,
          处理后: submitData.categoryIds
        })

        const api = form.productId ? updateProduct : addProduct
        await api(submitData)
        proxy.$modal.msgSuccess('操作成功')
        cancel()
      } catch (error) {
        console.error('提交失败:', error)
      }
    }
  })
}

// 修改取消操作的处理
const cancel = () => {
  router.push('/wechat/product/product')
}

// 初始化
const productId = route.query.id
if (productId) {
  getDetail(productId)
}
getCategoryList()
</script>

<style lang="scss" scoped>
.service-item-box {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.text-right {
  text-align: right;
  padding-top: 40px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
