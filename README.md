## 前端运行

```bash
# 克隆项目
git clone https://gitee.com/xihuanxinli_0/xihuanxinli-cms.git

# 进入项目目录
cd RuoYi-Vue3

# 安装依赖
yarn --registry=https://registry.npmmirror.com

# 启动服务
yarn dev

# 构建测试环境 yarn build:stage
# 构建生产环境 yarn build:prod
# 前端访问地址 http://localhost:80
```

## 内置功能

1.  用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2.  部门管理：配置系统组织机构（公司、部门、小组），树结构展现支持数据权限。
3.  岗位管理：配置系统用户所属担任职务。
4.  菜单管理：配置系统菜单，操作权限，按钮权限标识等。
5.  角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
6.  字典管理：对系统中经常使用的一些较为固定的数据进行维护。
7.  参数管理：对系统动态配置常用参数。
8.  通知公告：系统通知公告信息发布维护。
9.  操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
10. 登录日志：系统登录日志记录查询包含登录异常。
11. 在线用户：当前系统中活跃用户状态监控。
12. 定时任务：在线（添加、修改、删除)任务调度包含执行结果日志。
13. 代码生成：前后端代码的生成（java、html、xml、sql）支持CRUD下载 。
14. 系统接口：根据业务代码自动生成相关的api接口文档。
15. 服务监控：监视当前系统CPU、内存、磁盘、堆栈等相关信息。
16. 缓存监控：对系统的缓存信息查询，命令统计等。
17. 在线构建器：拖动表单元素生成相应的HTML代码。
18. 连接池监视：监视当前系统数据库连接池状态，可进行分析SQL找出系统性能瓶颈。

# 希欢心理咨询系统 - 聊天功能

## 功能介绍

该项目实现了一个基于Vue3、Pinia和WebSocket的实时聊天系统，主要包含以下功能：

1. **聊天会话管理**：
   - 用户与咨询师之间的会话列表
   - 会话未读消息计数
   - 最新消息显示与时间排序

2. **实时消息功能**：
   - 基于WebSocket的实时消息推送
   - 支持文本、图片和文件消息
   - 心跳检测与自动重连机制

3. **管理员功能**：
   - 管理员可查看所有用户与咨询师的对话
   - 管理员可以咨询师身份回复用户消息
   - 用户无法区分是管理员还是咨询师回复

4. **消息操作**：
   - 消息已读标记
   - 消息撤回功能
   - 历史消息分页加载

## 技术架构

- **前端框架**：Vue 3 + Element Plus
- **状态管理**：Pinia
- **实时通信**：WebSocket
- **API请求**：Axios

## 核心组件

1. **Pinia Store**：
   - `chat.js` - 管理聊天状态和WebSocket连接

2. **页面组件**：
   - `src/views/wechat/message/index.vue` - 聊天界面

3. **API接口**：
   - `src/api/wechat/message.js` - 封装聊天相关API

## WebSocket消息类型

- `chat` - 聊天消息
- `heartbeat` - 心跳消息
- `heartbeat_check` - 心跳检测
- `read` - 消息已读通知
- `withdraw` - 消息撤回

## 使用说明

1. 普通咨询师模式下：
   - 可查看与用户的会话
   - 发送和接收消息
   - 撤回自己发送的消息

2. 管理员模式下：
   - 可查看所有用户与咨询师的会话
   - 以咨询师身份回复用户消息
   - 操作会以咨询师ID显示给用户

## 注意事项

- 管理员回复用户时，会使用咨询师的ID作为发送者ID
- WebSocket连接会在用户登录后自动建立
- 定时心跳机制确保连接稳定性
