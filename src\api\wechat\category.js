import request from '@/utils/request'

// 查询分类列表
export function listCategory(query) {
  return request({
    url: '/psy/category/list',
    method: 'get',
    params: query
  })
}

// 查询分类详细
export function getCategory(categoryId) {
  return request({
    url: `/psy/category/${categoryId}`,
    method: 'get'
  })
}

// 新增分类
export function addCategory(data) {
  return request({
    url: '/psy/category',
    method: 'post',
    data: data
  })
}

// 修改分类
export function updateCategory(data) {
  return request({
    url: '/psy/category',
    method: 'put',
    data: data
  })
}

// 删除分类
export function delCategory(categoryId) {
  return request({
    url: `/psy/category/${categoryId}`,
    method: 'delete'
  })
}

// 查询分类树结构
export function categoryTree() {
  return request({
    url: '/psy/category/tree',
    method: 'get'
  })
}

// 查询带产品的分类树结构
export function categoryTreeWithProducts() {
  return request({
    url: '/psy/category/treeWithProducts',
    method: 'get'
  })
}

// 获取子分类
export function getChildrenCategories(parentId) {
  return request({
    url: `/psy/category/children/${parentId}`,
    method: 'get'
  })
} 