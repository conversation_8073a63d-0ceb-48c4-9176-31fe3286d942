import request from '@/utils/request'

// 查询时间段定义列表
export function listTimeRange(query) {
  return request({
    url: '/system/timeRange/list',
    method: 'get',
    params: query
  })
}

// 查询所有有效的时间段定义
export function listActiveTimeRanges() {
  return request({
    url: '/system/timeRange/listActive',
    method: 'get'
  })
}

// 查询时间段定义详细信息
export function getTimeRange(id) {
  return request({
    url: `/system/timeRange/${id}`,
    method: 'get'
  })
}

// 根据时间查询所属时间段
export function getTimeRangeByHour(hour) {
  return request({
    url: `/system/timeRange/getByHour/${hour}`,
    method: 'get'
  })
}

// 新增时间段定义
export function addTimeRange(data) {
  return request({
    url: '/system/timeRange',
    method: 'post',
    data: data
  })
}

// 修改时间段定义
export function updateTimeRange(data) {
  return request({
    url: '/system/timeRange',
    method: 'put',
    data: data
  })
}

// 删除时间段定义
export function delTimeRange(ids) {
  return request({
    url: `/system/timeRange/${ids}`,
    method: 'delete'
  })
}

// 导出时间段定义
export function exportTimeRange(query) {
  return request({
    url: '/system/timeRange/export',
    method: 'post',
    params: query
  })
}

// 初始化默认时间段
export function initDefaultTimeRanges() {
  return request({
    url: '/system/timeRange/init',
    method: 'post'
  })
}
