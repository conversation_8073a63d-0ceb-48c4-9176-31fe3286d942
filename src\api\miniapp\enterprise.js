import request from '@/utils/request'

// ==================== 企业测评接口 ====================

// 查询企业可用量表
export function getAvailableScales(enterpriseId, query) {
  return request({
    url: '/miniapp/enterprise/assessment/scale/available',
    method: 'get',
    params: { enterpriseId, ...query }
  })
}

// 查询企业信息
export function getEnterpriseInfo(enterpriseId) {
  return request({
    url: '/miniapp/enterprise/assessment/enterprise/' + enterpriseId,
    method: 'get'
  })
}

// 查询测评计划列表
export function listAssessmentPlans(enterpriseId, query) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/list',
    method: 'get',
    params: { enterpriseId, ...query }
  })
}

// 创建测评计划
export function createAssessmentPlan(data) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/create',
    method: 'post',
    data: data
  })
}

// 启动测评计划
export function startAssessmentPlan(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/start',
    method: 'post'
  })
}

// 查询计划参与者
export function getPlanParticipants(planId, query) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/participants',
    method: 'get',
    params: query
  })
}

// 添加参与者
export function addPlanParticipants(planId, participants) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/participants/add',
    method: 'post',
    data: { participants }
  })
}

// 查询计划统计
export function getPlanStats(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/stats',
    method: 'get'
  })
}

// 查询企业使用统计
export function getEnterpriseUsageStats(enterpriseId, query) {
  return request({
    url: '/miniapp/enterprise/assessment/enterprise/' + enterpriseId + '/usage-stats',
    method: 'get',
    params: query
  })
}

// ==================== 企业测评管理接口 ====================

// 获取企业测评计划详情
export function getAssessmentPlanDetail(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId,
    method: 'get'
  })
}

// 修改测评计划
export function updateAssessmentPlan(planId, data) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId,
    method: 'put',
    data: data
  })
}

// 删除测评计划
export function deleteAssessmentPlan(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId,
    method: 'delete'
  })
}

// 暂停测评计划
export function pauseAssessmentPlan(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/pause',
    method: 'post'
  })
}

// 恢复测评计划
export function resumeAssessmentPlan(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/resume',
    method: 'post'
  })
}

// 结束测评计划
export function endAssessmentPlan(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/end',
    method: 'post'
  })
}

// 移除参与者
export function removePlanParticipants(planId, participantIds) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/participants/remove',
    method: 'post',
    data: { participantIds }
  })
}

// 获取参与者测评记录
export function getParticipantRecords(planId, participantId, query) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/participants/' + participantId + '/records',
    method: 'get',
    params: query
  })
}

// 导出计划报告
export function exportPlanReport(planId, format = 'excel') {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/export',
    method: 'post',
    data: { format },
    responseType: 'blob'
  })
}

// 获取计划进度统计
export function getPlanProgressStats(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/progress-stats',
    method: 'get'
  })
}

// 获取计划结果统计
export function getPlanResultStats(planId) {
  return request({
    url: '/miniapp/enterprise/assessment/plan/' + planId + '/result-stats',
    method: 'get'
  })
}

// ==================== 企业用户管理接口 ====================

// 查询企业用户列表
export function listEnterpriseUsers(enterpriseId, query) {
  return request({
    url: '/miniapp/enterprise/users/list',
    method: 'get',
    params: { enterpriseId, ...query }
  })
}

// 添加企业用户
export function addEnterpriseUser(enterpriseId, userData) {
  return request({
    url: '/miniapp/enterprise/users/add',
    method: 'post',
    data: { enterpriseId, ...userData }
  })
}

// 批量导入企业用户
export function batchImportUsers(enterpriseId, file) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('enterpriseId', enterpriseId)
  
  return request({
    url: '/miniapp/enterprise/users/batch-import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取用户导入模板
export function getUserImportTemplate() {
  return request({
    url: '/miniapp/enterprise/users/import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 删除企业用户
export function removeEnterpriseUser(enterpriseId, userId) {
  return request({
    url: '/miniapp/enterprise/users/' + userId,
    method: 'delete',
    params: { enterpriseId }
  })
}

// 修改企业用户信息
export function updateEnterpriseUser(enterpriseId, userId, userData) {
  return request({
    url: '/miniapp/enterprise/users/' + userId,
    method: 'put',
    data: { enterpriseId, ...userData }
  })
}

// 重置用户密码
export function resetUserPassword(enterpriseId, userId) {
  return request({
    url: '/miniapp/enterprise/users/' + userId + '/reset-password',
    method: 'post',
    data: { enterpriseId }
  })
}

// 查询用户测评历史
export function getUserAssessmentHistory(enterpriseId, userId, query) {
  return request({
    url: '/miniapp/enterprise/users/' + userId + '/assessment-history',
    method: 'get',
    params: { enterpriseId, ...query }
  })
}
