<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>时间槽定时任务管理</span>
        </div>
      </template>
      
      <!-- 任务状态显示 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="24">
          <el-alert
            title="定时任务状态"
            :description="taskStatus"
            type="info"
            show-icon
            :closable="false">
          </el-alert>
        </el-col>
      </el-row>

      <!-- 操作按钮区域 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Clock /></el-icon>
                <span style="margin-left: 8px;">生成时间槽</span>
              </div>
            </template>
            <div class="task-content">
              <p>为咨询师生成未来几天的时间槽</p>
              <el-form :model="generateForm" :inline="true">
                <el-form-item label="天数">
                  <el-input-number v-model="generateForm.days" :min="1" :max="30" style="width: 120px" />
                </el-form-item>
              </el-form>
              <el-button type="primary" @click="handleManualGenerate" v-hasPermi="['system:timeTask:generate']">
                手动生成
              </el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Delete /></el-icon>
                <span style="margin-left: 8px;">清理过期时间槽</span>
              </div>
            </template>
            <div class="task-content">
              <p>清理指定天数前的过期时间槽</p>
              <el-form :model="cleanForm" :inline="true">
                <el-form-item label="天数">
                  <el-input-number v-model="cleanForm.days" :min="1" :max="90" style="width: 120px" />
                </el-form-item>
              </el-form>
              <el-button type="danger" @click="handleManualClean" v-hasPermi="['system:timeTask:clean']">
                手动清理
              </el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Refresh /></el-icon>
                <span style="margin-left: 8px;">更新过期状态</span>
              </div>
            </template>
            <div class="task-content">
              <p>更新已过期时间槽的状态</p>
              <br>
              <el-button type="warning" @click="handleUpdateExpired" v-hasPermi="['system:timeTask:update']">
                手动更新
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 任务执行日志 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Document /></el-icon>
                <span style="margin-left: 8px;">任务执行日志</span>
                <el-button type="primary" size="small" @click="refreshStatus" style="float: right;">
                  刷新状态
                </el-button>
              </div>
            </template>
            <div class="log-content">
              <el-timeline>
                <el-timeline-item
                  v-for="(log, index) in taskLogs"
                  :key="index"
                  :timestamp="log.timestamp"
                  :type="log.type">
                  {{ log.content }}
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup name="TimeTask">
import { getTaskStatus, manualGenerate, manualClean, manualUpdateExpired } from "@/api/wechat/timeTask";
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import { Clock, Delete, Refresh, Document } from '@element-plus/icons-vue'

// 任务状态
const taskStatus = ref("正在获取任务状态...");

// 生成表单
const generateForm = reactive({
  days: 7
});

// 清理表单
const cleanForm = reactive({
  days: 7
});

// 任务日志
const taskLogs = ref([
  {
    timestamp: new Date().toLocaleString(),
    type: 'primary',
    content: '系统启动，定时任务管理器初始化完成'
  }
]);

/** 获取任务状态 */
function refreshStatus() {
  getTaskStatus().then(response => {
    taskStatus.value = response.data || response.msg || "任务状态正常";
    addLog('获取任务状态成功', 'success');
  }).catch(() => {
    taskStatus.value = "获取任务状态失败";
    addLog('获取任务状态失败', 'danger');
  });
}

/** 手动生成时间槽 */
function handleManualGenerate() {
  ElMessageBox.confirm(`确认要生成未来 ${generateForm.days} 天的时间槽吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  }).then(() => {
    manualGenerate(generateForm.days).then(response => {
      ElMessage.success(response.msg);
      addLog(`手动生成时间槽成功: ${response.msg}`, 'success');
    }).catch(error => {
      addLog(`手动生成时间槽失败: ${error.message}`, 'danger');
    });
  });
}

/** 手动清理过期时间槽 */
function handleManualClean() {
  ElMessageBox.confirm(`确认要清理 ${cleanForm.days} 天前的过期时间槽吗?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    manualClean(cleanForm.days).then(response => {
      ElMessage.success(response.msg);
      addLog(`手动清理过期时间槽成功: ${response.msg}`, 'success');
    }).catch(error => {
      addLog(`手动清理过期时间槽失败: ${error.message}`, 'danger');
    });
  });
}

/** 手动更新过期状态 */
function handleUpdateExpired() {
  ElMessageBox.confirm('确认要更新过期时间槽的状态吗?', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  }).then(() => {
    manualUpdateExpired().then(response => {
      ElMessage.success(response.msg);
      addLog(`手动更新过期状态成功: ${response.msg}`, 'success');
    }).catch(error => {
      addLog(`手动更新过期状态失败: ${error.message}`, 'danger');
    });
  });
}

/** 添加日志 */
function addLog(content, type = 'primary') {
  taskLogs.value.unshift({
    timestamp: new Date().toLocaleString(),
    type: type,
    content: content
  });
  
  // 保持最多20条日志
  if (taskLogs.value.length > 20) {
    taskLogs.value = taskLogs.value.slice(0, 20);
  }
}

onMounted(() => {
  refreshStatus();
});
</script>

<style scoped>
.card-header {
  display: flex;
  align-items: center;
}

.task-content {
  text-align: center;
  padding: 20px 0;
}

.task-content p {
  margin-bottom: 15px;
  color: #666;
}

.log-content {
  max-height: 400px;
  overflow-y: auto;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
