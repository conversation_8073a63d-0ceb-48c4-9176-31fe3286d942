<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>预约排班系统测试页面</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <span>页面导航测试</span>
            </template>
            <div class="test-buttons">
              <el-button type="primary" @click="goToPage('/wechat/dashboard')">仪表板</el-button>
              <el-button type="success" @click="goToPage('/wechat/timeRange')">时间段管理</el-button>
              <el-button type="info" @click="goToPage('/wechat/timeSlot')">时间槽管理</el-button>
              <el-button type="warning" @click="goToPage('/wechat/schedule')">排班管理</el-button>
              <el-button type="danger" @click="goToPage('/wechat/appointment')">预约管理</el-button>
              <el-button @click="goToPage('/wechat/timeTask')">定时任务</el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <span>API测试</span>
            </template>
            <div class="test-buttons">
              <el-button type="primary" @click="testTimeRangeAPI">测试时间段API</el-button>
              <el-button type="success" @click="testTimeSlotAPI">测试时间槽API</el-button>
              <el-button type="info" @click="testAppointmentAPI">测试预约API</el-button>
              <el-button type="warning" @click="testDashboardAPI">测试仪表板API</el-button>
              <el-button type="danger" @click="testTaskAPI">测试任务API</el-button>
              <el-button type="primary" @click="testConsultantAPI">测试咨询师API</el-button>
              <el-button type="success" @click="testSystemTimeSlotAPI">测试系统时间槽API</el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <span>测试结果</span>
            </template>
            <div class="test-results">
              <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
                <el-tag :type="result.success ? 'success' : 'danger'">
                  {{ result.message }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup name="Test">
import { ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'

// API导入
import { listActiveTimeRanges } from "@/api/wechat/timeRange";
import { listTimeSlot } from "@/api/wechat/timeSlot";
import { listAppointment } from "@/api/wechat/appointment";
import { getDashboardStats } from "@/api/wechat/dashboard";
import { getTaskStatus } from "@/api/wechat/timeTask";
import { listConsultant } from "@/api/wechat/consultation/consultant";
import { listSystemTimeSlot } from "@/api/wechat/systemTimeSlot";

const { proxy } = getCurrentInstance();

const testResults = ref([]);

/** 跳转到指定页面 */
function goToPage(path) {
  try {
    proxy.$router.push(path);
    addTestResult(`导航到 ${path}`, true);
  } catch (error) {
    addTestResult(`导航失败: ${error.message}`, false);
  }
}

/** 测试时间段API */
function testTimeRangeAPI() {
  // 模拟您提供的数据格式进行测试
  const mockData = [
    {
      "createBy": "",
      "createTime": "2025-07-09 10:54:02",
      "updateBy": "",
      "updateTime": "2025-07-09 10:54:02",
      "remark": "",
      "id": 20,
      "name": "上午",
      "iconUrl": "https://example.com/icons/morning.png",
      "startHour": 9,
      "endHour": 12,
      "delFlag": 0
    },
    {
      "createBy": "",
      "createTime": "2025-07-09 10:54:02",
      "updateBy": "",
      "updateTime": "2025-07-09 10:54:02",
      "remark": "",
      "id": 21,
      "name": "中午",
      "iconUrl": "https://example.com/icons/noon.png",
      "startHour": 12,
      "endHour": 14,
      "delFlag": 0
    }
  ];

  console.log('模拟时间段数据:', mockData);
  addTestResult('时间段数据格式验证成功', true);

  // 实际API测试
  listActiveTimeRanges().then(response => {
    addTestResult('时间段API测试成功', true);
    console.log('时间段API响应:', response);
  }).catch(error => {
    addTestResult(`时间段API测试失败: ${error.message}`, false);
    console.error('时间段API错误:', error);
  });
}

/** 测试时间槽API */
function testTimeSlotAPI() {
  const params = { pageNum: 1, pageSize: 10 };
  listTimeSlot(params).then(response => {
    addTestResult('时间槽API测试成功', true);
    console.log('时间槽API响应:', response);
    console.log('数据条数:', response.rows?.length || 0);
    console.log('总条数:', response.total || 0);

    // 测试数据结构
    if (response.rows && response.rows.length > 0) {
      const firstSlot = response.rows[0];
      console.log('第一条时间槽数据:', firstSlot);
      console.log('咨询师ID:', firstSlot.counselorId);
      console.log('日期:', firstSlot.dateKey);
      console.log('开始时间:', firstSlot.startTime);
      console.log('时间段信息:', firstSlot.timeRange);
    }
  }).catch(error => {
    addTestResult(`时间槽API测试失败: ${error.message}`, false);
    console.error('时间槽API错误:', error);
  });
}

/** 测试预约API */
function testAppointmentAPI() {
  const params = { pageNum: 1, pageSize: 10 };
  listAppointment(params).then(response => {
    addTestResult('预约API测试成功', true);
    console.log('预约API响应:', response);
  }).catch(error => {
    addTestResult(`预约API测试失败: ${error.message}`, false);
    console.error('预约API错误:', error);
  });
}

/** 测试仪表板API */
function testDashboardAPI() {
  getDashboardStats().then(response => {
    addTestResult('仪表板API测试成功', true);
    console.log('仪表板API响应:', response);
  }).catch(error => {
    addTestResult(`仪表板API测试失败: ${error.message}`, false);
    console.error('仪表板API错误:', error);
  });
}

/** 测试任务API */
function testTaskAPI() {
  getTaskStatus().then(response => {
    addTestResult('任务API测试成功', true);
    console.log('任务API响应:', response);
  }).catch(error => {
    addTestResult(`任务API测试失败: ${error.message}`, false);
    console.error('任务API错误:', error);
  });
}

/** 测试咨询师API */
function testConsultantAPI() {
  listConsultant().then(response => {
    addTestResult('咨询师API测试成功', true);
    console.log('咨询师API响应:', response);

    // 测试咨询师数据结构
    if (response.rows && response.rows.length > 0) {
      const firstConsultant = response.rows[0];
      console.log('第一个咨询师数据:', firstConsultant);
      console.log('咨询师ID:', firstConsultant.id);
      console.log('咨询师姓名:', firstConsultant.name);

      // 测试咨询师名称匹配功能
      const testCounselorId = firstConsultant.id;
      const counselorList = response.rows || response.data || [];
      const foundCounselor = counselorList.find(c => c.id === testCounselorId);
      console.log('咨询师匹配测试:', foundCounselor ? foundCounselor.name : '未找到');
    }
  }).catch(error => {
    addTestResult(`咨询师API测试失败: ${error.message}`, false);
    console.error('咨询师API错误:', error);
  });
}

/** 测试系统时间槽API */
function testSystemTimeSlotAPI() {
  const params = { pageNum: 1, pageSize: 10, centerId: 1 };
  listSystemTimeSlot(params).then(response => {
    addTestResult('系统时间槽API测试成功', true);
    console.log('系统时间槽API响应:', response);
    console.log('数据条数:', response.rows?.length || 0);
    console.log('总条数:', response.total || 0);

    // 测试数据结构
    if (response.rows && response.rows.length > 0) {
      const firstSlot = response.rows[0];
      console.log('第一条系统时间槽数据:', firstSlot);
      console.log('日期:', firstSlot.slotDate);
      console.log('开始时间:', firstSlot.startTime);
      console.log('结束时间:', firstSlot.endTime);
      console.log('可用咨询师数:', firstSlot.availableCounselors);
      console.log('总咨询师数:', firstSlot.totalCounselors);
      console.log('状态:', firstSlot.status);
    }
  }).catch(error => {
    addTestResult(`系统时间槽API测试失败: ${error.message}`, false);
    console.error('系统时间槽API错误:', error);
  });
}

/** 添加测试结果 */
function addTestResult(message, success) {
  testResults.value.unshift({
    message,
    success,
    timestamp: new Date().toLocaleTimeString()
  });

  // 保持最多10条结果
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10);
  }

  if (success) {
    ElMessage.success(message);
  } else {
    ElMessage.error(message);
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.test-results {
  max-height: 300px;
  overflow-y: auto;
}

.test-result-item {
  margin-bottom: 8px;
  padding: 5px;
  border-radius: 4px;
  background-color: #f5f5f5;
}
</style>
