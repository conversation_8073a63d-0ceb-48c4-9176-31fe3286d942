<template>
  <div class="progress-container">
    <svg :width="size" :height="size" viewBox="0 0 100 100">
      <!-- 定义渐变 - 使用角度渐变 -->
      <defs>
        <linearGradient id="purpleOrangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="#663399" />
          <stop offset="100%" stop-color="#FF6600" />
        </linearGradient>
      </defs>

      <!-- 进度条轨道 -->
      <circle cx="50" cy="50" :r="radius" stroke="#eee" stroke-width="20" fill="none" stroke-linecap="round" />

      <!-- 分段进度条 -->
      <template v-for="(segment, index) in segments" :key="index">
        <circle cx="50" cy="50" :r="radius" :stroke="getSegmentColor(index)" stroke-width="20" fill="none"
          stroke-linecap="round" :stroke-dasharray="segment.dasharray" :stroke-dashoffset="segment.dashoffset" />
      </template>
    </svg>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  progress: {
    type: Number,
    default: 0,
    validator: (v) => v >= 0 && v <= 100
  },
  size: {
    type: Number,
    default: 200
  },
  primaryColor: {
    type: String,
    default: '#663399'
  },
  secondaryColor: {
    type: String,
    default: '#FF6600'
  }
});

// 常量定义
const TOTAL_SEGMENTS = 12;
const SEGMENT_ANGLE = 360 / TOTAL_SEGMENTS;

// 计算属性
const radius = computed(() => 40);
const circumference = computed(() => 2 * Math.PI * radius.value);
const segmentLength = computed(() => circumference.value / TOTAL_SEGMENTS);

// 计算分段数据
const segments = computed(() => {
  const segments = [];
  const activeSegments = Math.ceil((props.progress / 100) * TOTAL_SEGMENTS);

  for (let i = 0; i < TOTAL_SEGMENTS; i++) {
    const isActive = i < activeSegments;
    const offset = i * segmentLength.value;

    if (isActive) {
      // 计算当前段的填充比例
      const segmentProgress = Math.min(
        1,
        (props.progress / 100) * TOTAL_SEGMENTS - i
      );

      segments.push({
        dasharray: `${segmentLength.value * segmentProgress}, ${circumference.value}`,
        dashoffset: offset
      });
    } else {
      segments.push({
        dasharray: '0, ' + circumference.value,
        dashoffset: 0
      });
    }
  }

  return segments;
});

// 获取分段颜色 - 改进渐变应用逻辑
const getSegmentColor = (index) => {
  // 前两段使用渐变，其余使用主色
  if (index === 0 || index === 1) {
    return 'url(#purpleOrangeGradient)';
  }
  return props.primaryColor;
};
</script>

<style scoped>
.progress-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>