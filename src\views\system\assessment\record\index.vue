<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="量表" prop="scaleId">
        <el-select v-model="queryParams.scaleId" placeholder="请选择量表" clearable style="width: 240px">
          <el-option v-for="scale in scaleOptions" :key="scale.id" :label="scale.scaleName" :value="scale.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option v-for="dict in test_status_options" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="测评时间" style="width: 308px">
        <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:assessment:record:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:assessment:record:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:assessment:record:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="DataAnalysis" @click="handleStats"
          v-hasPermi="['system:assessment:record:query']">统计分析</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="会话ID" align="center" prop="sessionId" width="180" :show-overflow-tooltip="true" />
      <el-table-column label="量表名称" align="center" width="150">
        <template #default="scope">
          <span>{{ getScaleNameById(scope.row.scaleId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" width="120">
        <template #default="scope">
          <span>{{ getUserNameById(scope.row.userId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总分" align="center" prop="totalScore" width="80" />
      <el-table-column label="满分" align="center" prop="maxScore" width="80" />
      <el-table-column label="得分率" align="center" width="80">
        <template #default="scope">
          <span>{{ scope.row.percentage }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="结果等级" align="center" prop="resultLevel" width="100">
        <template #default="scope">
          <el-tag :type="getLevelType(scope.row.resultLevel)">{{ scope.row.resultLevel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="用时" align="center" width="80">
        <template #default="scope">
          <span>{{ formatDuration(scope.row.duration) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <dict-tag :options="test_status_options" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:assessment:record:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:assessment:record:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="checkPermi(['system:assessment:record:edit'])" command="recalculate"
                  icon="Refresh">重新计算</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:record:query'])" command="answers"
                  icon="List">查看答案</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:record:query'])" command="report"
                  icon="Document">生成报告</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:record:remove'])" command="delete"
                  icon="Delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 修改测评记录对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="recordRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="总分" prop="totalScore">
          <el-input-number v-model="form.totalScore" :min="0" />
        </el-form-item>
        <el-form-item label="结果等级" prop="resultLevel">
          <el-input v-model="form.resultLevel" placeholder="请输入结果等级" />
        </el-form-item>
        <el-form-item label="结果描述" prop="resultDescription">
          <el-input v-model="form.resultDescription" type="textarea" placeholder="请输入结果描述" />
        </el-form-item>
        <el-form-item label="建议" prop="suggestions">
          <el-input v-model="form.suggestions" type="textarea" placeholder="请输入建议" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option v-for="dict in test_status_options" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 测评记录详情对话框 -->
    <el-dialog title="测评记录详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="会话ID">{{ detailForm.sessionId }}</el-descriptions-item>
        <el-descriptions-item label="量表名称">{{ getScaleNameById(detailForm.scaleId) }}</el-descriptions-item>
        <el-descriptions-item label="用户">{{ getUserNameById(detailForm.userId) }}</el-descriptions-item>
        <el-descriptions-item label="是否匿名">
          <dict-tag :options="yes_no_options" :value="detailForm.isAnonymous" />
        </el-descriptions-item>
        <el-descriptions-item label="总分">{{ detailForm.totalScore }}</el-descriptions-item>
        <el-descriptions-item label="满分">{{ detailForm.maxScore }}</el-descriptions-item>
        <el-descriptions-item label="得分率">{{ detailForm.percentage }}%</el-descriptions-item>
        <el-descriptions-item label="结果等级">
          <el-tag :type="getLevelType(detailForm.resultLevel)">{{ detailForm.resultLevel }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用时">{{ formatDuration(detailForm.duration) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="test_status_options" :value="detailForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ parseTime(detailForm.startTime) }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ parseTime(detailForm.endTime) }}</el-descriptions-item>
        <el-descriptions-item label="结果描述" :span="2">{{ detailForm.resultDescription }}</el-descriptions-item>
        <el-descriptions-item label="建议" :span="2">{{ detailForm.suggestions }}</el-descriptions-item>
      </el-descriptions>

      <!-- 维度得分 -->
      <el-divider content-position="center">维度得分</el-divider>
      <el-table :data="dimensionScores" v-if="dimensionScores.length > 0">
        <el-table-column label="维度" prop="dimension" />
        <el-table-column label="得分" prop="score" />
        <el-table-column label="满分" prop="maxScore" />
        <el-table-column label="得分率">
          <template #default="scope">
            <span>{{ ((scope.row.score / scope.row.maxScore) * 100).toFixed(1) }}%</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog title="统计分析" v-model="statsOpen" width="1000px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总测评次数" :value="statsData.totalCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="完成次数" :value="statsData.completedCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="完成率" :value="statsData.completionRate" suffix="%" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均得分" :value="statsData.avgScore" :precision="1" />
        </el-col>
      </el-row>

      <el-tabs v-model="statsActiveTab" class="mt-4">
        <el-tab-pane label="测评趋势" name="trend">
          <div ref="statsTrendChartRef" style="width: 100%; height: 400px;"></div>
        </el-tab-pane>
        <el-tab-pane label="得分分布" name="score">
          <div ref="statsScoreChartRef" style="width: 100%; height: 400px;"></div>
        </el-tab-pane>
        <el-tab-pane label="结果等级分布" name="level">
          <div ref="statsLevelChartRef" style="width: 100%; height: 400px;"></div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup name="Record">
import { listRecord, getRecord, delRecord, updateRecord, exportRecord, getRecordStats, recalculateResult, getRecordAnswers, generateRecordReport } from "@/api/system/assessment/record";
import { listScale } from "@/api/system/assessment/scale";
import { listUser } from "@/api/system/user";
import { checkPermi } from "@/utils/permission";
import * as echarts from 'echarts';

const { proxy } = getCurrentInstance();
const { test_status_options, yes_no_options } = proxy.useDict('test_status', 'sys_yes_no');

const recordList = ref([]);
const scaleOptions = ref([]);
const userList = ref([]);
const dimensionScores = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const statsOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const statsActiveTab = ref('trend');
const statsData = ref({});

// 图表实例
const statsTrendChart = ref(null);
const statsScoreChart = ref(null);
const statsLevelChart = ref(null);
const statsTrendChartRef = ref(null);
const statsScoreChartRef = ref(null);
const statsLevelChartRef = ref(null);

const data = reactive({
  form: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    scaleId: null,
    userId: null,
    status: null
  },
  rules: {
    totalScore: [
      { required: true, message: "总分不能为空", trigger: "blur" }
    ],
    resultLevel: [
      { required: true, message: "结果等级不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, detailForm, rules } = toRefs(data);

/** 查询测评记录列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != dateRange && '' != dateRange) {
    queryParams.value.params["beginTime"] = dateRange.value[0];
    queryParams.value.params["endTime"] = dateRange.value[1];
  }
  listRecord(queryParams.value).then(response => {
    recordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询量表列表 */
function getScaleList() {
  listScale().then(response => {
    scaleOptions.value = response.rows;
  });
}

/** 查询用户列表 */
function getUserList() {
  listUser().then(response => {
    userList.value = response.rows;
  });
}

/** 根据量表ID获取量表名称 */
function getScaleNameById(scaleId) {
  if (!scaleId) return '-';
  const scale = scaleOptions.value.find(item => item.id === scaleId);
  return scale ? scale.scaleName : `量表ID: ${scaleId}`;
}

/** 根据用户ID获取用户名称 */
function getUserNameById(userId) {
  if (!userId) return '-';
  const user = userList.value.find(item => item.userId === userId);
  return user ? (user.nickName || user.userName) : `用户ID: ${userId}`;
}

/** 获取等级标签类型 */
function getLevelType(level) {
  if (level === '正常') return 'success';
  if (level === '轻度') return 'warning';
  if (level === '中度') return 'danger';
  if (level === '重度') return 'danger';
  return 'info';
}

/** 格式化时长 */
function formatDuration(duration) {
  if (!duration) return '-';
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  return `${minutes}分${seconds}秒`;
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    totalScore: null,
    resultLevel: null,
    resultDescription: null,
    suggestions: null,
    status: 0
  };
  proxy.resetForm("recordRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getRecord(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改测评记录";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getRecord(_id).then(response => {
    detailForm.value = response.data;
    // 处理维度得分
    if (response.data.dimensionScores) {
      dimensionScores.value = Object.entries(response.data.dimensionScores).map(([dimension, score]) => ({
        dimension,
        score,
        maxScore: 100 // 假设满分为100，实际应从后端获取
      }));
    } else {
      dimensionScores.value = [];
    }
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["recordRef"].validate(valid => {
    if (valid) {
      updateRecord(form.value).then(response => {
        proxy.$modal.msgSuccess("修改成功");
        open.value = false;
        getList();
      });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除测评记录编号为"' + _ids + '"的数据项？').then(function () {
    return delRecord(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/assessment/record/export', {
    ...queryParams.value
  }, `record_${new Date().getTime()}.xlsx`)
}

/** 统计分析 */
function handleStats() {
  getRecordStats(queryParams.value).then(response => {
    statsData.value = response.data;
    statsOpen.value = true;

    nextTick(() => {
      initStatsTrendChart();
      initStatsScoreChart();
      initStatsLevelChart();
    });
  });
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'recalculate':
      handleRecalculate(row);
      break;
    case 'answers':
      handleAnswers(row);
      break;
    case 'report':
      handleReport(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
  }
}

/** 重新计算结果 */
function handleRecalculate(row) {
  proxy.$modal.confirm('是否确认重新计算测评记录"' + row.sessionId + '"的结果？').then(function () {
    return recalculateResult(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("重新计算成功");
  }).catch(() => { });
}

/** 查看答案 */
function handleAnswers(row) {
  const routeUrl = proxy.$router.resolve({
    path: '/assessment-system/answer',
    query: { recordId: row.id }
  });
  window.open(routeUrl.href, '_blank');
}

/** 生成报告 */
function handleReport(row) {
  proxy.$modal.confirm('是否生成测评报告？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    return generateRecordReport(row.id, 1);
  }).then(response => {
    // 这里可以显示报告或下载报告
    proxy.$modal.msgSuccess("报告生成成功");
    console.log('报告数据:', response.data);
  }).catch(() => {});
}

/** 初始化趋势图表 */
function initStatsTrendChart() {
  if (statsTrendChart.value) {
    statsTrendChart.value.dispose();
  }

  statsTrendChart.value = echarts.init(statsTrendChartRef.value);

  const option = {
    title: {
      text: '测评趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['测评次数', '完成次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '测评次数',
        type: 'line',
        data: [20, 25, 30, 35, 32, 40, 45]
      },
      {
        name: '完成次数',
        type: 'line',
        data: [18, 22, 28, 32, 30, 38, 42]
      }
    ]
  };

  statsTrendChart.value.setOption(option);
}

/** 初始化得分分布图表 */
function initStatsScoreChart() {
  if (statsScoreChart.value) {
    statsScoreChart.value.dispose();
  }

  statsScoreChart.value = echarts.init(statsScoreChartRef.value);

  const option = {
    title: {
      text: '得分分布'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-20', '21-40', '41-60', '61-80', '81-100']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '人数',
        type: 'bar',
        data: [8, 20, 35, 28, 15]
      }
    ]
  };

  statsScoreChart.value.setOption(option);
}

/** 初始化结果等级分布图表 */
function initStatsLevelChart() {
  if (statsLevelChart.value) {
    statsLevelChart.value.dispose();
  }

  statsLevelChart.value = echarts.init(statsLevelChartRef.value);

  const option = {
    title: {
      text: '结果等级分布'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '结果等级',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 45, name: '正常' },
          { value: 25, name: '轻度' },
          { value: 20, name: '中度' },
          { value: 10, name: '重度' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  statsLevelChart.value.setOption(option);
}

// 监听标签页切换
watch(statsActiveTab, (newVal) => {
  nextTick(() => {
    if (newVal === 'trend') {
      initStatsTrendChart();
    } else if (newVal === 'score') {
      initStatsScoreChart();
    } else if (newVal === 'level') {
      initStatsLevelChart();
    }
  });
});

// 监听窗口大小变化，重绘图表
window.addEventListener('resize', () => {
  if (statsTrendChart.value) {
    statsTrendChart.value.resize();
  }
  if (statsScoreChart.value) {
    statsScoreChart.value.resize();
  }
  if (statsLevelChart.value) {
    statsLevelChart.value.resize();
  }
});

onMounted(() => {
  getList();
  getScaleList();
  getUserList();
});

onUnmounted(() => {
  window.removeEventListener('resize', () => { });
  if (statsTrendChart.value) {
    statsTrendChart.value.dispose();
  }
  if (statsScoreChart.value) {
    statsScoreChart.value.dispose();
  }
  if (statsLevelChart.value) {
    statsLevelChart.value.dispose();
  }
});
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}
</style>
