<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="门店名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入门店名称" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="门店状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="门店状态" clearable style="width: 240px">
          <el-option label="正常" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="分店名称" prop="branchName">
        <el-input v-model="queryParams.branchName" placeholder="请输入分店名称" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['wechat:store:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['wechat:store:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['wechat:store:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['wechat:store:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="storeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="门店ID" align="center" prop="id" />
      <el-table-column label="门店名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="分店名称" align="center" prop="branchName" :show-overflow-tooltip="true" />
      <el-table-column label="门店地址" align="center" prop="address" :show-overflow-tooltip="true" />
      <el-table-column label="门店状态" align="center" prop="status">
        <template #default="scope">
          <el-switch v-model="scope.row.status" active-value="1" inactive-value="0"
            @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['wechat:store:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['wechat:store:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改门店对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="storeRef" :model="form" :rules="rules" label-width="100px">
        <el-tabs>
          <el-tab-pane label="基本信息">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="门店名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入门店名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="分店名称" prop="branchName">
                  <el-input v-model="form.branchName" placeholder="请输入分店名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="地图名称" prop="mapName">
                  <el-input v-model="form.mapName" placeholder="请输入地图显示名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="门店状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio value="1">正常</el-radio>
                    <el-radio value="0">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="地图地址" prop="mapAddress">
              <el-input v-model="form.mapAddress" placeholder="请输入地图详细地址" />
            </el-form-item>
            <el-form-item label="门店地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入门店地址" />
            </el-form-item>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="经度" prop="longitude">
                  <el-input-number v-model="form.longitude" :precision="6" placeholder="请输入经度" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="纬度" prop="latitude">
                  <el-input-number v-model="form.latitude" :precision="6" placeholder="请输入纬度" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="营业信息">
            <el-form-item label="营业日期">
              <el-card shadow="never" class="mb-4">
                <el-checkbox-group v-model="businessDaysArray">
                  <el-checkbox value="monday">周一</el-checkbox>
                  <el-checkbox value="tuesday">周二</el-checkbox>
                  <el-checkbox value="wednesday">周三</el-checkbox>
                  <el-checkbox value="thursday">周四</el-checkbox>
                  <el-checkbox value="friday">周五</el-checkbox>
                  <el-checkbox value="saturday">周六</el-checkbox>
                  <el-checkbox value="sunday">周日</el-checkbox>
                </el-checkbox-group>
              </el-card>
            </el-form-item>

            <el-form-item label="营业时间">
              <el-card shadow="never" class="mb-4">
                <template #header>
                  <div class="card-header">
                    <span>营业时段设置</span>
                    <el-button type="primary" link icon="Plus" @click="addBusinessHour">添加时段</el-button>
                  </div>
                </template>
                <div v-for="(hour, index) in form.businessHours" :key="index" class="business-hour-item">
                  <el-row :gutter="20" align="middle">
                    <el-col :span="8">
                      <el-time-picker v-model="hour.startTime" format="HH:mm:ss" value-format="HH:mm:ss"
                        placeholder="开始时间" style="width: 100%" />
                    </el-col>
                    <el-col :span="1" class="text-center">
                      <span>至</span>
                    </el-col>
                    <el-col :span="8">
                      <el-time-picker v-model="hour.endTime" format="HH:mm:ss" value-format="HH:mm:ss"
                        placeholder="结束时间" style="width: 100%" />
                    </el-col>
                    <el-col :span="5">
                      <el-checkbox v-model="hour.is24h" :true-value="1" :false-label="0">24小时</el-checkbox>
                    </el-col>
                    <el-col :span="2">
                      <el-button type="danger" link icon="Delete" @click="removeBusinessHour(index)" />
                    </el-col>
                  </el-row>
                </div>
                <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入备注" />
              </el-card>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="联系方式">
            <el-form-item label="联系电话">
              <el-card shadow="never" class="mb-4">
                <template #header>
                  <div class="card-header">
                    <span>联系电话设置</span>
                    <el-button type="primary" link icon="Plus" @click="addContact">添加电话</el-button>
                  </div>
                </template>
                <div v-for="(contact, index) in form.contacts" :key="index" class="contact-item">
                  <el-row :gutter="20" align="middle">
                    <el-col :span="20">
                      <el-input v-model="contact.phone" placeholder="请输入联系电话" />
                    </el-col>
                    <el-col :span="4" class="text-right">
                      <el-button type="danger" link icon="Delete" @click="removeContact(index)" />
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Store">
import { listStore, getStore, delStore, addStore, updateStore, updateStoreStatus } from "@/api/wechat/store";
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, getCurrentInstance, computed, nextTick } from 'vue'

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 门店表格数据
const storeList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  status: undefined,
  branchName: undefined
});

// 表单参数
const form = reactive({
  id: undefined,
  name: undefined,
  branchName: undefined,
  mapName: undefined,
  mapAddress: undefined,
  address: undefined,
  longitude: undefined,
  latitude: undefined,
  status: "1",
  contacts: [],
  businessDays: {},
  businessHours: [],
  remark: undefined
});

// 表单校验
const rules = reactive({
  name: [
    { required: true, message: "门店名称不能为空", trigger: "blur" }
  ],
  address: [
    { required: true, message: "门店地址不能为空", trigger: "blur" }
  ]
});

// 营业日期数组计算属性
const businessDaysArray = computed({
  get() {
    const days = [];
    if (form.businessDays.monday === 1) days.push('monday');
    if (form.businessDays.tuesday === 1) days.push('tuesday');
    if (form.businessDays.wednesday === 1) days.push('wednesday');
    if (form.businessDays.thursday === 1) days.push('thursday');
    if (form.businessDays.friday === 1) days.push('friday');
    if (form.businessDays.saturday === 1) days.push('saturday');
    if (form.businessDays.sunday === 1) days.push('sunday');
    return days;
  },
  set(value) {
    form.businessDays.monday = value.includes('monday') ? 1 : 0;
    form.businessDays.tuesday = value.includes('tuesday') ? 1 : 0;
    form.businessDays.wednesday = value.includes('wednesday') ? 1 : 0;
    form.businessDays.thursday = value.includes('thursday') ? 1 : 0;
    form.businessDays.friday = value.includes('friday') ? 1 : 0;
    form.businessDays.saturday = value.includes('saturday') ? 1 : 0;
    form.businessDays.sunday = value.includes('sunday') ? 1 : 0;
  }
});

/** 查询门店列表 */
function getList() {
  loading.value = true;
  listStore(queryParams).then(response => {
    storeList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  // 重置基本字段
  Object.assign(form, {
    id: undefined,
    name: undefined,
    branchName: undefined,
    mapName: undefined,
    mapAddress: undefined,
    address: undefined,
    longitude: undefined,
    latitude: undefined,
    status: "1",
    remark: undefined
  });

  // 重置复杂对象字段
  form.contacts.splice(0, form.contacts.length);
  Object.keys(form.businessDays).forEach(key => {
    delete form.businessDays[key];
  });
  form.businessHours.splice(0, form.businessHours.length);

  // 重置表单验证状态
  nextTick(() => {
    proxy.resetForm("storeRef");
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加门店";

  // 确保对话框打开后表单完全重置
  nextTick(() => {
    // 再次确保表单验证状态清除
    proxy.resetForm("storeRef");
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const storeId = row.id || ids.value[0];
  getStore(storeId).then(response => {
    console.log(response)
    const { store, businessDays, businessHours, contacts } = response.data;

    // 填充门店基本信息
    form.id = store.id;
    form.name = store.name;
    form.branchName = store.branchName;
    form.mapName = store.mapName;
    form.mapAddress = store.mapAddress;
    form.address = store.address;
    form.longitude = store.longitude;
    form.latitude = store.latitude;
    form.status = store.status;
    form.remark = store.remark;

    // 填充营业日期
    form.businessDays = businessDays || {};

    // 填充营业时间
    form.businessHours = businessHours || [];

    // 填充联系方式
    form.contacts = contacts || [];

    open.value = true;
    title.value = "修改门店";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["storeRef"].validate(valid => {
    if (valid) {
      // 构建提交数据
      const submitData = {
        store: {
          id: form.id,
          name: form.name,
          branchName: form.branchName,
          mapName: form.mapName,
          mapAddress: form.mapAddress,
          address: form.address,
          longitude: form.longitude,
          latitude: form.latitude,
          status: form.status,
          remark: form.remark
        },
        businessDays: form.businessDays,
        businessHours: form.businessHours,
        contacts: form.contacts
      };

      if (form.id != undefined) {
        updateStore(submitData).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addStore(submitData).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const storeIds = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除所选门店数据?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return delStore(storeIds);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => { });
}

/** 门店状态修改 */
function handleStatusChange(row) {
  let text = row.status === "1" ? "启用" : "停用";
  ElMessageBox.confirm('确认要"' + text + '""' + row.name + '"吗?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return updateStoreStatus(row.id, row.status);
  }).then(() => {
    ElMessage.success(text + "成功");
  }).catch(() => {
    row.status = row.status === "1" ? "0" : "1";
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/store/export", {
    ...queryParams
  }, "store_" + new Date().getTime() + ".xlsx");
}

/** 添加联系电话 */
function addContact() {
  form.contacts.push({
    phone: '',
    contactType: 'DEFAULT'
  });
}

/** 删除联系电话 */
function removeContact(index) {
  form.contacts.splice(index, 1);
}

/** 添加营业时间 */
function addBusinessHour() {
  form.businessHours.push({
    startTime: '09:00:00',
    endTime: '21:00:00',
    is24h: 0
  });
}

/** 删除营业时间 */
function removeBusinessHour(index) {
  form.businessHours.splice(index, 1);
}

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.business-hour-item,
.contact-item {
  margin-bottom: 12px;
}

.business-hour-item:last-child,
.contact-item:last-child {
  margin-bottom: 0;

  :deep .el-input__suffix {
    width: 30px;

    .el-input__suffix-inner {
      width: 30px;
    }
  }
}


.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}
</style>