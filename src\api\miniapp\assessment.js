import request from '@/utils/request'

// ==================== 量表相关接口 ====================

// 查询量表列表（小程序）
export function listScales(query) {
  return request({
    url: '/miniapp/user/psy-assessment/scale/list',
    method: 'get',
    params: query
  })
}

// 获取量表详情（小程序）
export function getScaleDetail(id) {
  return request({
    url: '/miniapp/user/psy-assessment/scale/' + id,
    method: 'get'
  })
}

// 搜索量表
export function searchScales(query) {
  return request({
    url: '/miniapp/user/psy-assessment/scale/search',
    method: 'get',
    params: query
  })
}

// 查询热门量表
export function getHotScales(limit = 10) {
  return request({
    url: '/miniapp/user/psy-assessment/scale/hot',
    method: 'get',
    params: { limit }
  })
}

// 查询免费量表
export function getFreeScales() {
  return request({
    url: '/miniapp/user/psy-assessment/scale/free',
    method: 'get'
  })
}

// 查询推荐量表
export function getRecommendScales(limit = 10) {
  return request({
    url: '/miniapp/user/psy-assessment/scale/recommend',
    method: 'get',
    params: { limit }
  })
}

// 查询最新量表
export function getLatestScales(limit = 10) {
  return request({
    url: '/miniapp/user/psy-assessment/scale/latest',
    method: 'get',
    params: { limit }
  })
}

// 查询收藏量表
export function getFavoriteScales(query) {
  return request({
    url: '/miniapp/user/psy-assessment/scale/favorite',
    method: 'get',
    params: query
  })
}

// ==================== 测评流程接口 ====================

// 开始测评
export function startAssessment(scaleId) {
  return request({
    url: '/miniapp/user/psy-assessment/start',
    method: 'post',
    data: { scaleId }
  })
}

// 获取题目
export function getQuestion(sessionId, questionNo) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/question/${questionNo}`,
    method: 'get'
  })
}

// 提交答案
export function submitAnswer(sessionId, data) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/answer`,
    method: 'post',
    data: data
  })
}

// 批量提交答案
export function batchSubmitAnswers(sessionId, answers) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/batch-answer`,
    method: 'post',
    data: { answers }
  })
}

// 获取下一题
export function getNextQuestion(sessionId) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/next`,
    method: 'get'
  })
}

// 获取上一题
export function getPreviousQuestion(sessionId) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/previous`,
    method: 'get'
  })
}

// 跳转到指定题目
export function jumpToQuestion(sessionId, questionNo) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/jump/${questionNo}`,
    method: 'get'
  })
}

// 保存进度
export function saveProgress(sessionId) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/save`,
    method: 'post'
  })
}

// 恢复测评
export function resumeAssessment(sessionId) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/resume`,
    method: 'post'
  })
}

// 暂停测评
export function pauseAssessment(sessionId) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/pause`,
    method: 'post'
  })
}

// 完成测评
export function completeAssessment(sessionId) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/complete`,
    method: 'post'
  })
}

// 放弃测评
export function abandonAssessment(sessionId, reason) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/abandon`,
    method: 'post',
    data: { reason }
  })
}

// 获取测评进度
export function getAssessmentProgress(sessionId) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/progress`,
    method: 'get'
  })
}

// 获取测评状态
export function getAssessmentStatus(sessionId) {
  return request({
    url: `/miniapp/user/psy-assessment/${sessionId}/status`,
    method: 'get'
  })
}

// ==================== 测评记录接口 ====================

// 查询测评记录列表
export function listAssessmentRecords(query) {
  return request({
    url: '/miniapp/user/psy-assessment/record/list',
    method: 'get',
    params: query
  })
}

// 查询量表测评记录
export function getScaleRecords(scaleId, query) {
  return request({
    url: '/miniapp/user/psy-assessment/record/scale/' + scaleId,
    method: 'get',
    params: query
  })
}

// 查询进行中的测评
export function getInProgressRecords() {
  return request({
    url: '/miniapp/user/psy-assessment/record/in-progress',
    method: 'get'
  })
}

// 查询已完成的测评
export function getCompletedRecords(query) {
  return request({
    url: '/miniapp/user/psy-assessment/record/completed',
    method: 'get',
    params: query
  })
}

// 获取测评记录详情
export function getRecordDetail(recordId) {
  return request({
    url: '/miniapp/user/psy-assessment/record/' + recordId,
    method: 'get'
  })
}

// 生成测评报告
export function generateReport(recordId, reportLevel = 1) {
  return request({
    url: '/miniapp/user/psy-assessment/record/' + recordId + '/report',
    method: 'post',
    data: { reportLevel }
  })
}

// 获取测评建议
export function getAssessmentSuggestions(recordId) {
  return request({
    url: '/miniapp/user/psy-assessment/record/' + recordId + '/suggestions',
    method: 'get'
  })
}

// ==================== 统计分析接口 ====================

// 用户测评统计
export function getUserAssessmentStats() {
  return request({
    url: '/miniapp/user/psy-assessment/stats',
    method: 'get'
  })
}
