import request from '@/utils/request'

// 查询冥想评价列表
export function listMeditationReview(query) {
  return request({
    url: '/system/meditationReview/list',
    method: 'get',
    params: query
  })
}

// 导出冥想评价列表
export function exportMeditationReview(query) {
  return request({
    url: '/system/meditationReview/export',
    method: 'post',
    params: query
  })
}

// 获取冥想评价详细信息
export function getMeditationReview(id) {
  return request({
    url: `/system/meditationReview/${id}`,
    method: 'get'
  })
}

// 获取冥想评价详细信息（包含冥想和用户信息）
export function getMeditationReviewDetails(id) {
  return request({
    url: `/system/meditationReview/details/${id}`,
    method: 'get'
  })
}

// 新增冥想评价
export function addMeditationReview(data) {
  return request({
    url: '/system/meditationReview',
    method: 'post',
    data: data
  })
}

// 修改冥想评价
export function updateMeditationReview(data) {
  return request({
    url: '/system/meditationReview',
    method: 'put',
    data: data
  })
}

// 删除冥想评价
export function delMeditationReview(ids) {
  return request({
    url: `/system/meditationReview/${ids}`,
    method: 'delete'
  })
}

// 根据冥想ID查询评价列表
export function getMeditationReviewsByMeditation(meditationId) {
  return request({
    url: `/system/meditationReview/meditation/${meditationId}`,
    method: 'get'
  })
}

// 根据用户ID查询评价列表
export function getMeditationReviewsByUser(userId) {
  return request({
    url: `/system/meditationReview/user/${userId}`,
    method: 'get'
  })
}

// 获取冥想评价统计信息
export function getMeditationReviewStatistics(meditationId) {
  return request({
    url: `/system/meditationReview/statistics/${meditationId}`,
    method: 'get'
  })
}
