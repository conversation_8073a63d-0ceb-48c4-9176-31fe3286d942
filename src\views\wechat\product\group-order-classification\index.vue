<template>
  <div class="app-container">
    <div class="search-wrapper" :class="{ collapsed: !showSearch }">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="queryParams.categoryName" placeholder="请输入分类名称" clearable style="width: 200px"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="分类状态" clearable style="width: 200px">
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['psy:category:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['psy:category:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['psy:category:remove']">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="categoryList" row-key="categoryId"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="分类名称" align="left" prop="categoryName" :show-overflow-tooltip="true" />
      <el-table-column label="排序" align="center" prop="orderNum" width="80" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="{ row }">
          <dict-tag :options="statusOptions" :value="row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="{ row }">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(row)"
            v-hasPermi="['psy:category:edit']">修改</el-button>
          <el-button link type="primary" icon="Plus" @click="handleAdd(row)"
            v-hasPermi="['psy:category:add']">新增</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(row)"
            v-hasPermi="['psy:category:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改分类对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="上级分类">
          <el-tree-select v-model="form.parentId" :data="categoryOptions"
            :props="{ label: 'categoryName', value: 'categoryId', children: 'children' }" value-key="categoryId"
            placeholder="选择上级分类" check-strictly clearable />
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="显示排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listCategory, getCategory, delCategory, addCategory, updateCategory } from '@/api/wechat/category'
import { useDict } from '@/utils/dict'

const { sys_normal_disable: statusOptions } = useDict('sys_normal_disable')

// 遮罩层
const loading = ref(false)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 分类表格数据
const categoryList = ref([])
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 分类树选项
const categoryOptions = ref([])

// 查询参数
const queryParams = reactive({
  categoryName: undefined,
  status: undefined
})

// 表单参数
const form = reactive({
  categoryId: undefined,
  parentId: 0,
  categoryName: undefined,
  orderNum: 0,
  status: '0'
})

// 表单校验
const rules = {
  categoryName: [
    { required: true, message: '分类名称不能为空', trigger: 'blur' }
  ],
  orderNum: [
    { required: true, message: '显示排序不能为空', trigger: 'blur' }
  ]
}

// 表单引用
const formRef = ref(null)
const queryFormRef = ref(null)

/** 查询分类列表 */
const getList = async () => {
  loading.value = true
  try {
    const response = await listCategory(queryParams)
    categoryList.value = response.data
    categoryOptions.value = [{
      categoryId: 0,
      categoryName: '主类目',
      children: response.data
    }]
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 选择条数  */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.categoryId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = (row) => {
  reset()
  if (row != null && row.categoryId) {
    form.parentId = row.categoryId
  }
  open.value = true
  title.value = '添加分类'
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset()
  const categoryId = row?.categoryId || ids.value[0]
  const response = await getCategory(categoryId)
  Object.assign(form, response.data)
  open.value = true
  title.value = '修改分类'
}

/** 提交按钮 */
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.categoryId !== undefined) {
          await updateCategory(form)
          ElMessage.success('修改成功')
        } else {
          await addCategory(form)
          ElMessage.success('新增成功')
        }
        open.value = false
        getList()
      } catch (error) {
        console.error('操作失败', error)
      }
    }
  })
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const categoryIds = row?.categoryId || ids.value
  try {
    await ElMessageBox.confirm('是否确认删除所选分类？')
    await delCategory(categoryIds)
    await getList()
    ElMessage.success('删除成功')
  } catch (error) {
    console.error('删除失败', error)
  }
}

/** 表单重置 */
const reset = () => {
  form.categoryId = undefined
  form.parentId = 0
  form.categoryName = undefined
  form.orderNum = 0
  form.status = '0'
  formRef.value?.resetFields()
}

/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 10px;
}

.search-wrapper {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.search-wrapper.collapsed {
  height: 0;
  padding: 0;
  overflow: hidden;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 8px;
}

:deep(.el-form-item__label) {
  font-size: 13px;
  padding-right: 4px;
}

:deep(.el-input__wrapper) {
  padding-left: 8px;
  padding-right: 8px;
}

.dialog-footer {
  text-align: center;
  padding-top: 10px;
}
</style>