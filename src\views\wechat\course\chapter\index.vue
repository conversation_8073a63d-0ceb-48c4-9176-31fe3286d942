<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="章节名称" prop="chapterTitle">
        <el-input v-model="queryParams.chapterTitle" placeholder="请输入章节名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="所属课程" prop="courseId">
        <el-select v-model="queryParams.courseId" placeholder="请选择课程" clearable style="width: 240px">
          <el-option v-for="course in courseList" :key="course.id" :label="course.title" :value="course.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:chapter:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:chapter:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="chapterList" row-key="id" :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column label="章节名称" align="left" prop="chapterTitle" :show-overflow-tooltip="true" />
      <el-table-column label="所属课程" align="center" prop="courseName">
        <template #default="scope">
          <span>{{ getCourseName(scope.row.courseId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="chapterOrder" width="100" />
      <el-table-column label="内容类型" align="center" prop="contentType" width="100">
        <template #default="scope">
          <dict-tag :options="sys_content_type" :value="scope.row.contentType" />
        </template>
      </el-table-column>
      <el-table-column label="时长(秒)" align="center" prop="duration" width="100" />
      <el-table-column label="试听" align="center" prop="isTrial" width="80">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isTrial" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:chapter:edit']">修改</el-button>
          <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)"
            v-hasPermi="['system:chapter:add']">新增</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:chapter:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改章节对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="chapterRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级章节" prop="parentId">
              <el-tree-select v-model="form.parentId" :data="chapterOptions"
                :props="{ value: 'id', label: 'chapterTitle', children: 'children' }" value-key="id"
                placeholder="选择上级章节" check-strictly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="章节名称" prop="chapterTitle">
              <el-input v-model="form.chapterTitle" placeholder="请输入章节名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属课程" prop="courseId">
              <el-select v-model="form.courseId" placeholder="请选择课程" style="width: 100%">
                <el-option v-for="course in courseList" :key="course.id" :label="course.title" :value="course.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="chapterOrder">
              <el-input-number v-model="form.chapterOrder" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内容类型" prop="contentType">
              <el-select v-model="form.contentType" placeholder="请选择内容类型" style="width: 100%">
                <el-option label="视频" value="0" />
                <el-option label="音频" value="1" />
                <el-option label="文档" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="时长(秒)" prop="duration">
              <el-input-number v-model="form.duration" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否试听" prop="isTrial">
              <el-radio-group v-model="form.isTrial">
                <el-radio :value="0">否</el-radio>
                <el-radio :value="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="视频地址" prop="mediaUrl">
              <el-input v-model="form.mediaUrl" placeholder="请输入视频地址" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="章节内容" prop="chapterContent">
              <editor v-model="form.chapterContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Chapter">
import { listChapter, listAllChapter, getChapter, delChapter, addChapter, updateChapter, exportChapter, getChapterTree } from "@/api/wechat/course/chapter";
import { listCourse } from "@/api/wechat/course/course";

const { proxy } = getCurrentInstance();
const { sys_chapter_type, sys_normal_disable, sys_yes_no, sys_content_type } = proxy.useDict('sys_chapter_type', 'sys_normal_disable', 'sys_yes_no', 'sys_content_type');

const chapterList = ref([]);
const courseList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const isExpandAll = ref(false);
const refreshTable = ref(true);
const chapterOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    chapterTitle: null,
    courseId: null,
    status: null
  },
  rules: {
    parentId: [
      { required: true, message: "上级章节不能为空", trigger: "blur" }
    ],
    chapterTitle: [
      { required: true, message: "章节名称不能为空", trigger: "blur" }
    ],
    courseId: [
      { required: true, message: "所属课程不能为空", trigger: "change" }
    ],
    chapterOrder: [
      { required: true, message: "显示排序不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询章节列表 */
function getList() {
  loading.value = true;
  listChapter(queryParams.value).then(response => {
    chapterList.value = proxy.handleTree(response.rows, "id", "parentId");
    loading.value = false;
  });
}

/** 查询课程列表 */
function getCourseList() {
  listCourse().then(response => {
    courseList.value = response.rows || response.data || [];
  });
}

/** 查询章节下拉树结构 */
function getTreeselect() {
  listAllChapter().then(response => {
    chapterOptions.value = [];
    const data = { id: 0, chapterTitle: '主类目', children: [] };
    data.children = proxy.handleTree(response.data, "id", "parentId");
    chapterOptions.value.push(data);
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    courseId: null,
    parentId: 0,
    chapterTitle: null,
    chapterContent: null,
    contentType: "0",
    duration: null,
    chapterOrder: 0,
    level: 1,
    isTrial: 0,
    mediaUrl: null,
    status: "0",
    remark: null
  };
  proxy.resetForm("chapterRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  getTreeselect();
  if (row != null && row.id) {
    form.value.parentId = row.id;
  } else {
    form.value.parentId = 0;
  }
  open.value = true;
  title.value = "添加章节";
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getTreeselect();
  if (row != null) {
    form.value.parentId = row.parentId;
  }
  getChapter(row.id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改章节";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["chapterRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        console.log(form.value);

        updateChapter(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addChapter(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.title + '"的数据项？').then(function () {
    return delChapter(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('chapter/export', {
    ...queryParams.value
  }, `chapter_${new Date().getTime()}.xlsx`)
}

function getCourseName(courseId) {
  const course = courseList.value.find(course => course.id === courseId);
  return course.title ? course.title : '未知课程';
}

onMounted(() => {
  getList();
  getCourseList();
});
</script>
