import request from '@/utils/request'

// 查询咨询师列表
export function listConsultant(query) {
  return request({
    url: '/system/consultant/list',
    method: 'get',
    params: query
  })
}

// 获取所有咨询师简单信息
export function listAllSimpleConsultants() {
  return request({
    url: '/system/consultant/allSimpleList',
    method: 'get'
  })
}

// 查询咨询师详细
export function getConsultant(id) {
  return request({
    url: '/system/consultant/admin/' + id,
    method: 'get'
  })
}

// 新增咨询师
export function addConsultant(data) {
  return request({
    url: '/system/consultant',
    method: 'post',
    data: data
  })
}

// 修改咨询师
export function updateConsultant(data) {
  return request({
    url: '/system/consultant',
    method: 'put',
    data: data
  })
}

// 删除咨询师
export function delConsultant(id) {
  return request({
    url: '/system/consultant/' + id,
    method: 'delete'
  })
}

// 更新咨询师工作状态
export function updateConsultantWorkStatus(id, status) {
  return request({
    url: '/system/consultant/' + id + '/workStatus/' + status,
    method: 'put'
  })
}

// 更新咨询师审核状态
export function updateConsultantAuditStatus(id, status, reason) {
  return request({
    url: '/system/consultant/' + id + '/auditStatus/' + status,
    method: 'put',
    data: { reason }
  })
}

// 分步保存 - 基本信息
export function saveBasicInfo(data) {
  return request({
    url: '/system/consultant/basic',
    method: 'put',
    data: data
  })
}

// 分步保存 - 服务信息
export function saveServiceInfo(data) {
  return request({
    url: '/system/consultant/service',
    method: 'put',
    data: data
  })
}

// 分步保存 - 资质信息
export function saveQualificationInfo(data) {
  return request({
    url: '/system/consultant/qualification',
    method: 'put',
    data: data
  })
}

// 分步保存 - 教育培训信息
export function saveEducationInfo(data) {
  return request({
    url: '/system/consultant/education',
    method: 'put',
    data: data
  })
}


// 获取咨询领域列表
export function listExpertise() {
  return request({
    url: '/system/expertise/tree',
    method: 'get'
  })
} 