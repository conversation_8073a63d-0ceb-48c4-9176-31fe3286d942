import request from '@/utils/request'

// 查询热门搜索列表
export function listHotSearch(query) {
  return request({
    url: '/system/search/hot/list',
    method: 'get',
    params: query
  })
}

// 查询热门搜索详细
export function getHotSearch(id) {
  return request({
    url: '/system/search/hot/' + id,
    method: 'get'
  })
}

// 新增热门搜索
export function addHotSearch(data) {
  return request({
    url: '/system/search/hot',
    method: 'post',
    data: data
  })
}

// 修改热门搜索
export function updateHotSearch(data) {
  return request({
    url: '/system/search/hot',
    method: 'put',
    data: data
  })
}

// 删除热门搜索
export function delHotSearch(ids) {
  return request({
    url: '/system/search/hot/' + ids,
    method: 'delete'
  })
}

// 导出热门搜索
export function exportHotSearch(query) {
  return request({
    url: '/system/search/hot/export',
    method: 'post',
    params: query
  })
}
