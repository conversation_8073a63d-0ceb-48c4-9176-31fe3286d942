<template>
  <div class="app-container">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" :disabled="!isEdit" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" :disabled="!isEdit" placeholder="请选择性别">
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出生年份" prop="birthDate">
              <el-date-picker v-model="form.birthDate" type="year" :disabled="!isEdit" placeholder="选择年份"
                value-format="YYYY" :clearable="false" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="form.phone" :disabled="!isEdit" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="微信号" prop="wechat">
              <el-input v-model="form.wechat" :disabled="!isEdit" placeholder="请输入微信号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在地" prop="location">
              <el-input v-model="form.location" :disabled="!isEdit" placeholder="请输入所在地" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="form.address" :disabled="!isEdit" placeholder="请输入详细地址" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="从业时间" prop="startYear">
              <el-date-picker v-model="form.startYear" type="year" :disabled="!isEdit" placeholder="请选择从业时间"
                value-format="YYYY" :clearable="false" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工作状态" prop="workStatus">
              <el-select v-model="form.workStatus" :disabled="!isEdit" placeholder="请选择工作状态">
                <el-option label="可预约" value="0" />
                <el-option label="休息" value="1" />
                <el-option label="离职" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">头像与证件照</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="头像照片" prop="imageUrl">
              <ImageUpload v-if="isEdit" v-model="form.imageUrl" :limit="1" />
              <el-image v-else :src="form.imageUrl" fit="cover" class="avatar" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证正面" prop="idFrontImg">
              <ImageUpload v-if="isEdit" v-model="form.idFrontImg" :limit="1" />
              <el-image v-else :src="form.idFrontImg" fit="cover" class="avatar" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证反面" prop="idBackImg">
              <ImageUpload v-if="isEdit" v-model="form.idBackImg" :limit="1" />
              <el-image v-else :src="form.idBackImg" fit="cover" class="avatar" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">咨询信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="咨询价格" prop="price">
              <el-input-number v-model="form.price" :disabled="!isEdit" :min="0" :step="10" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低价格" prop="minFee">
              <el-input-number v-model="form.minFee" :disabled="!isEdit" :min="0" :step="10" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最高价格" prop="maxFee">
              <el-input-number v-model="form.maxFee" :disabled="!isEdit" :min="0" :step="10" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="咨询总人数" prop="totalCases">
              <el-input v-model="form.totalCases" :disabled="!isEdit" placeholder="请输入咨询总人数" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务人次" prop="serviceCount">
              <el-input v-model="form.serviceCount" :disabled="!isEdit" placeholder="请输入服务人次" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务时长" prop="serviceHours">
              <el-input v-model="form.serviceHours" :disabled="!isEdit" placeholder="请输入服务时长" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="咨询师等级" prop="consultantLevel">
              <el-select v-model="form.consultantLevel" :disabled="!isEdit" placeholder="请选择咨询师等级">
                <el-option v-for="level in allConsultantLevels" :key="level.value" :label="level.label"
                  :value="level.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务方式" prop="serviceMethods">
              <el-checkbox-group v-model="selectedServiceMethods" :disabled="!isEdit">
                <el-checkbox v-for="method in allServiceMethods" :key="method.value" :value="method.value">
                  {{ method.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="咨询风格" prop="consultStyles">
              <el-checkbox-group v-model="selectedConsultStyles" :disabled="!isEdit">
                <el-checkbox v-for="style in allConsultStyles" :key="style.value" :value="style.value">
                  {{ style.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="擅长群体" prop="goodGroups">
              <el-checkbox-group v-model="selectedGoodGroups" :disabled="!isEdit">
                <el-checkbox v-for="group in allGoodGroups" :key="group.value" :value="group.value">
                  {{ group.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="特色咨询" prop="specialMethods">
              <el-checkbox-group v-model="selectedSpecialMethods" :disabled="!isEdit">
                <el-checkbox v-for="method in allSpecialMethods" :key="method.value" :value="method.value">
                  {{ method.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="擅长领域" prop="expertises">
              <el-tree ref="expertiseTree" :data="expertiseOptions" show-checkbox node-key="id"
                :props="{ label: 'typeName', children: 'children' }" :default-checked-keys="selectedExpertises"
                :disabled="!isEdit" @check="handleExpertiseChange" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="其他服务">
              <el-checkbox v-model="form.canTeach" :disabled="!isEdit">可带教</el-checkbox>
              <el-checkbox v-model="form.canTravel" :disabled="!isEdit">可出诊</el-checkbox>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">个人简介</span>
          </div>
        </template>
        <el-form-item prop="personalIntro">
          <el-input v-model="form.personalIntro" :disabled="!isEdit" type="textarea" :rows="6" placeholder="请输入个人简介" />
        </el-form-item>
      </el-card>

      <!-- <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">咨询风格</span>
          </div>
        </template>
        <el-form-item prop="consultStyles">
          <el-checkbox-group v-model="selectedConsultStyles" :disabled="!isEdit">
            <el-checkbox v-for="style in allConsultStyles" :key="style.value" :value="style.value">
              {{ style.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">擅长群体</span>
          </div>
        </template>
        <el-form-item prop="goodGroups">
          <el-checkbox-group v-model="selectedGoodGroups" :disabled="!isEdit">
            <el-checkbox v-for="group in allGoodGroups" :key="group.value" :value="group.value">
              {{ group.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card> -->

      <!-- <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">咨询师等级</span>
          </div>
        </template>
        <el-form-item prop="consultantLevel">
          <el-select v-model="form.consultantLevel" :disabled="!isEdit" placeholder="请选择咨询师等级">
            <el-option v-for="level in allConsultantLevels" :key="level.value" :label="level.label"
              :value="level.value" />
          </el-select>
        </el-form-item>
      </el-card> -->

      <!-- <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">服务方式</span>
          </div>
        </template>
        <el-form-item prop="serviceMethods">
          <el-checkbox-group v-model="selectedServiceMethods" :disabled="!isEdit">
            <el-checkbox v-for="method in allServiceMethods" :key="method.value" :value="method.value">
              {{ method.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card> -->

      <!-- <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">专长领域</span>
          </div>
        </template>
        <el-form-item prop="expertises">
          <el-tree ref="expertiseTree" :data="expertiseOptions" show-checkbox node-key="id"
            :props="{ label: 'typeName', children: 'children' }" :default-checked-keys="selectedExpertises"
            :disabled="!isEdit" @check="handleExpertiseChange" />
        </el-form-item>
      </el-card> -->

      <!-- <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">特色咨询</span>
          </div>
        </template>
        <el-form-item prop="specialMethods">
          <el-checkbox-group v-model="selectedSpecialMethods" :disabled="!isEdit">
            <el-checkbox v-for="method in allSpecialMethods" :key="method.value" :value="method.value">
              {{ method.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card> -->

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">证书信息</span>
            <el-button v-if="isEdit" type="primary" link @click="handleAddCertificate">
              <el-icon>
                <plus />
              </el-icon>添加证书
            </el-button>
          </div>
        </template>
        <el-table :data="form.certificates" border>
          <el-table-column label="证书名称" prop="name">
            <template #default="scope">
              <el-input v-if="isEdit" v-model="scope.row.name" placeholder="请输入证书名称" />
              <span v-else>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="证书编号" prop="number">
            <template #default="scope">
              <el-input v-if="isEdit" v-model="scope.row.number" placeholder="请输入证书编号" />
              <span v-else>{{ scope.row.number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发证日期" prop="issueDate">
            <template #default="scope">
              <el-date-picker v-if="isEdit" v-model="scope.row.issueDate" type="date" placeholder="选择日期"
                value-format="YYYY-MM-DD" />
              <span v-else>{{ formatDate(scope.row.issueDate) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="有效期至" prop="expireDate">
            <template #default="scope">
              <el-date-picker v-if="isEdit" v-model="scope.row.expireDate" type="date" placeholder="选择日期"
                value-format="YYYY-MM-DD" />
              <span v-else>{{ formatDate(scope.row.expireDate) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="证书图片" prop="imageUrl">
            <template #default="scope">
              <ImageUpload v-if="isEdit" v-model="scope.row.imageUrl" :limit="1" />
              <el-image v-else-if="scope.row.imageUrl" :src="scope.row.imageUrl" fit="cover" class="certificate" />
            </template>
          </el-table-column>
          <el-table-column v-if="isEdit" label="操作" width="120">
            <template #default="scope">
              <el-button type="danger" link @click="removeCertificate(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">教育经历</span>
            <el-button v-if="isEdit" type="primary" link @click="handleAddEducation">
              <el-icon>
                <plus />
              </el-icon>添加教育经历
            </el-button>
          </div>
        </template>
        <el-table :data="form.educations" border>
          <el-table-column label="学校" prop="school">
            <template #default="scope">
              <el-input v-if="isEdit" v-model="scope.row.school" placeholder="请输入学校名称" />
              <span v-else>{{ scope.row.school }}</span>
            </template>
          </el-table-column>
          <el-table-column label="专业" prop="major">
            <template #default="scope">
              <el-input v-if="isEdit" v-model="scope.row.major" placeholder="请输入专业名称" />
              <span v-else>{{ scope.row.major }}</span>
            </template>
          </el-table-column>
          <el-table-column label="学历" prop="degree">
            <template #default="scope">
              <el-select v-if="isEdit" v-model="scope.row.degree" placeholder="请选择学历">
                <el-option label="专科" value="专科" />
                <el-option label="本科" value="本科" />
                <el-option label="硕士" value="硕士" />
                <el-option label="博士" value="博士" />
              </el-select>
              <span v-else>{{ scope.row.degree }}</span>
            </template>
          </el-table-column>
          <el-table-column label="起止年份">
            <template #default="scope">
              <div v-if="isEdit">
                <el-input v-model="scope.row.startYear" style="width: 80px" /> -
                <el-input v-model="scope.row.endYear" style="width: 80px" />
              </div>
              <span v-else>{{ scope.row.startYear }} - {{ scope.row.endYear }}</span>
            </template>
          </el-table-column>
          <el-table-column label="证书图片" prop="certificate">
            <template #default="scope">
              <ImageUpload v-if="isEdit" v-model="scope.row.certificate" :limit="1" />
              <el-image v-else-if="scope.row.certificate" :src="scope.row.certificate" fit="cover"
                class="certificate" />
            </template>
          </el-table-column>
          <el-table-column v-if="isEdit" label="操作" width="120">
            <template #default="scope">
              <el-button type="danger" link @click="removeEducation(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">督导经历</span>
            <el-button v-if="isEdit" type="primary" link @click="handleAddSupervision">
              <el-icon>
                <plus />
              </el-icon>添加督导经历
            </el-button>
          </div>
        </template>
        <el-table :data="form.supervisions" border>
          <el-table-column label="督导类型" prop="type">
            <template #default="scope">
              <el-select v-if="isEdit" v-model="scope.row.type" placeholder="请选择督导类型">
                <el-option label="个体督导" value="个体督导" />
                <el-option label="团体督导" value="团体督导" />
              </el-select>
              <span v-else>{{ scope.row.type }}</span>
            </template>
          </el-table-column>
          <el-table-column label="督导时长" prop="hours">
            <template #default="scope">
              <el-input-number v-if="isEdit" v-model="scope.row.hours" :min="0" />
              <span v-else>{{ scope.row.hours }}小时</span>
            </template>
          </el-table-column>
          <el-table-column label="督导师" prop="supervisor">
            <template #default="scope">
              <el-input v-if="isEdit" v-model="scope.row.supervisor" placeholder="请输入督导师姓名" />
              <span v-else>{{ scope.row.supervisor }}</span>
            </template>
          </el-table-column>
          <el-table-column label="起止日期">
            <template #default="scope">
              <div v-if="isEdit">
                <el-date-picker :model-value="[scope.row.startDate, scope.row.endDate]" type="daterange"
                  range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                  @update:model-value="(val) => handleDateRangeChange(val, scope.row)" />
              </div>
              <div v-else>
                {{ formatDateRange(scope.row.startDate, scope.row.endDate) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="证书图片" prop="certificate">
            <template #default="scope">
              <ImageUpload v-if="isEdit" v-model="scope.row.certificate" :limit="1" />
              <el-image v-else-if="scope.row.certificate" :src="scope.row.certificate" fit="cover"
                class="certificate" />
            </template>
          </el-table-column>
          <el-table-column v-if="isEdit" label="操作" width="120">
            <template #default="scope">
              <el-button type="danger" link @click="removeSupervision(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="title">培训经历</span>
            <el-button v-if="isEdit" type="primary" link @click="handleAddTraining">
              <el-icon>
                <plus />
              </el-icon>添加培训经历
            </el-button>
          </div>
        </template>
        <el-table :data="form.trainings" border>
          <el-table-column label="培训名称" prop="name">
            <template #default="scope">
              <el-input v-if="isEdit" v-model="scope.row.name" placeholder="请输入培训名称" />
              <span v-else>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="培训机构" prop="organizer">
            <template #default="scope">
              <el-input v-if="isEdit" v-model="scope.row.organizer" placeholder="请输入培训机构" />
              <span v-else>{{ scope.row.organizer }}</span>
            </template>
          </el-table-column>
          <el-table-column label="培训时长" prop="hours">
            <template #default="scope">
              <el-input-number v-if="isEdit" v-model="scope.row.hours" :min="0" />
              <span v-else>{{ scope.row.hours }}小时</span>
            </template>
          </el-table-column>
          <el-table-column label="起止日期">
            <template #default="scope">
              <el-date-picker v-if="isEdit" v-model="scope.row.dateRange" type="daterange" range-separator="-"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD"
                @change="(val) => handleDateRangeChange(val, scope.row)" />
              <span v-else>{{ formatDateRange(scope.row.startDate, scope.row.endDate) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="证书图片" prop="certificate">
            <template #default="scope">
              <ImageUpload v-if="isEdit" v-model="scope.row.certificate" :limit="1" />
              <el-image v-else-if="scope.row.certificate" :src="scope.row.certificate" fit="cover"
                class="certificate" />
            </template>
          </el-table-column>
          <el-table-column v-if="isEdit" label="操作" width="120">
            <template #default="scope">
              <el-button type="danger" link @click="removeTraining(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="bottom-buttons">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup name="ConsultantDetail">
import { ref, onMounted, computed, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import ImageUpload from '@/components/ImageUpload'
import {
  getConsultant,
  addConsultant,
  updateConsultant,
  listExpertise
} from "@/api/wechat/consultation/consultant"
import { formatDate } from '@/utils/index'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const formRef = ref()
const expertiseTree = ref()
const isEdit = ref(false)

/** 表单校验规则 */
const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  birthDate: [{ required: true, message: '请输入出生年份', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  imageUrl: [{ required: true, message: '请上传头像照片', trigger: 'change' }],
  idFrontImg: [{ required: true, message: '请上传身份证正面照片', trigger: 'change' }],
  idBackImg: [{ required: true, message: '请上传身份证反面照片', trigger: 'change' }],
  price: [{ required: true, message: '请输入咨询价格', trigger: 'blur' }],
  personalIntro: [{ required: true, message: '请输入个人简介', trigger: 'blur' }]
}

/** 表单初始化 */
const form = ref({
  id: undefined,
  name: '',
  gender: '',
  birthDate: 0,
  phone: '',
  wechat: '',
  location: '',
  address: '',
  startYear: '',
  workStatus: '0',
  auditStatus: '0',
  imageUrl: '',
  idFrontImg: '',
  idBackImg: '',
  price: 0,
  minFee: 0,
  maxFee: 0,
  totalCases: '',
  serviceCount: '',
  serviceHours: '',
  canTeach: false,
  canTravel: false,
  personalIntro: '',
  consultStyles: [],
  serviceMethods: [],
  expertises: [],
  specialMethods: [],
  goodGroups: [],
  consultantLevel: '',
  certificates: [],
  educations: [],
  supervisions: [],
  trainings: []
})



/** 重置表单 */
function resetForm() {
  Object.assign(form.value, {
    id: undefined,
    name: '',
    gender: '',
    birthDate: new Date().getFullYear(),
    phone: '',
    wechat: '',
    location: '',
    address: '',
    startYear: new Date().getFullYear(),
    workStatus: '0',
    auditStatus: '0',
    imageUrl: '',
    idFrontImg: '',
    idBackImg: '',
    price: 0,
    minFee: 0,
    maxFee: 0,
    totalCases: '',
    serviceCount: '',
    serviceHours: '',
    canTeach: false,
    canTravel: false,
    personalIntro: '',
    consultStyles: [],
    serviceMethods: [],
    expertises: [],
    specialMethods: [],
    goodGroups: [],
    consultantLevel: '',
    certificates: [],
    educations: [],
    supervisions: [],
    trainings: []
  })
}

/** 获取咨询师详情 */
async function getDetail(id) {
  const response = await getConsultant(id)
  const data = response.data
  // 类型转换
  data.birthDate = String(data.birthDate || new Date().getFullYear())
  data.startYear = String(data.startYear || new Date().getFullYear())
  data.price = Number(data.price) || 0
  data.minFee = Number(data.minFee) || 0
  data.maxFee = Number(data.maxFee) || 0

  // 处理培训经历的日期范围
  if (data.trainings?.length) {
    data.trainings = data.trainings.map(training => ({
      ...training,
      dateRange: [training.startDate, training.endDate]
    }))
  }

  Object.assign(form.value, data)
}

/** 返回按钮操作 */
function goBack() {
  router.go(-1)
}

/** 提交表单 */
function handleSubmit() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.value.id) {
          await updateConsultant(form.value)
          ElMessage.success('修改成功')
        } else {
          await addConsultant(form.value)
          ElMessage.success('新增成功')
        }
        goBack()
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
  })
}



const dateRange = ref([])

function handleDateRangeChange(val, row) {
  if (val) {
    row.startDate = val[0]
    row.endDate = val[1]
  } else {
    row.startDate = null
    row.endDate = null
  }
}

function formatDateRange(startDate, endDate) {
  if (!startDate || !endDate) return ''
  return `${formatDate(startDate)} - ${formatDate(endDate)}`
}

// 获取字典数据
const {
  psy_service_type,
  psy_characteristic_consultation,
  psy_consulting_style,
  psy_good_groups,
  psy_consultant_level
} = proxy.useDict(
  "psy_service_type",
  "psy_characteristic_consultation",
  "psy_consulting_style",
  "psy_good_groups",
  "psy_consultant_level"
);

// 数据字典选项
const allServiceMethods = computed(() => psy_service_type.value)
const allSpecialMethods = computed(() => psy_characteristic_consultation.value)
const allConsultStyles = computed(() => psy_consulting_style.value)
const allGoodGroups = computed(() => psy_good_groups.value)
const allConsultantLevels = computed(() => psy_consultant_level.value)

// 已选择的服务方式
const selectedServiceMethods = computed({
  get: () => form.value.serviceMethods?.map(method => method.dictValue) || [],
  set: (values) => {
    form.value.serviceMethods = values.map(value => {
      const method = allServiceMethods.value.find(m => m.value === value)
      return {
        dictValue: method.value,
        dictLabel: method.label,
        dictType: 'psy_service_type'
      }
    })
  }
})

// 已选择的特色咨询
const selectedSpecialMethods = computed({
  get: () => form.value.specialMethods?.map(method => method.dictValue) || [],
  set: (values) => {
    form.value.specialMethods = values.map(value => {
      const method = allSpecialMethods.value.find(m => m.value === value)
      return {
        dictValue: method.value,
        dictLabel: method.label,
        dictType: 'psy_characteristic_consultation'
      }
    })
  }
})

// 已选择的咨询风格
const selectedConsultStyles = computed({
  get: () => form.value.consultStyles?.map(style => style.dictValue) || [],
  set: (values) => {
    form.value.consultStyles = values.map(value => {
      const style = allConsultStyles.value.find(s => s.value === value)
      return {
        dictValue: style.value,
        dictLabel: style.label,
        dictType: 'psy_consulting_style'
      }
    })
  }
})

// 已选择的擅长群体
const selectedGoodGroups = computed({
  get: () => form.value.goodGroups?.map(group => group.dictValue) || [],
  set: (values) => {
    form.value.goodGroups = values.map(value => {
      const group = allGoodGroups.value.find(g => g.value === value)
      return {
        dictValue: group.value,
        dictLabel: group.label,
        dictType: 'psy_good_groups'
      }
    })
  }
})

// 专长领域选项
const expertiseOptions = ref([])

// 已选择的专长领域
const selectedExpertises = computed(() => {
  const selected = []
  form.value.expertises?.forEach(expertise => {
    selected.push(expertise.id)
    expertise.children?.forEach(child => {
      selected.push(child.id)
    })
  })
  return selected
})

// 初始化数据
const initData = async () => {
  const response = await listExpertise()
  if (response.code === 200 && response.data) {
    // 直接使用返回的树形结构数据
    expertiseOptions.value = response.data.map(item => {
      const { id, typeName, children } = item
      return {
        id,
        typeName,
        children: children?.map(child => {
          const { id, typeName } = child
          return { id, typeName }
        })
      }
    })
  }
}

// 处理专长领域选择变化
const handleExpertiseChange = (val) => {
  // 获取树组件当前选中的节点
  const checkedNodes = expertiseTree.value.getCheckedNodes()
  const selected = []

  // 根据选中的节点构建树形结构
  checkedNodes.forEach(node => {
    if (!node.parentId) { // 如果是父节点
      const existingParent = selected.find(p => p.id === node.id)
      if (!existingParent) {
        selected.push({
          id: node.id,
          typeName: node.typeName,
          children: []
        })
      }
    } else { // 如果是子节点
      const parent = selected.find(p => p.id === node.parentId)
      if (parent) {
        parent.children.push({
          id: node.id,
          typeName: node.typeName,
          parentId: node.parentId
        })
      } else {
        // 如果父节点还不存在，先创建父节点
        const parentNode = expertiseOptions.value.find(p => p.id === node.parentId)
        if (parentNode) {
          selected.push({
            id: parentNode.id,
            typeName: parentNode.typeName,
            children: [{
              id: node.id,
              typeName: node.typeName,
              parentId: node.parentId
            }]
          })
        }
      }
    }
  })

  form.value.expertises = selected
}

// 初始化
onMounted(async () => {
  await initData()

  if (route.params.id) {
    getDetail(route.params.id)
    isEdit.value = true
  } else {
    // 新增时重置表单
    resetForm()
    isEdit.value = true
  }
})

// 添加证书
function handleAddCertificate() {
  form.value.certificates = form.value.certificates || []
  form.value.certificates.push({
    name: '',
    number: '',
    issueDate: null,
    expireDate: null,
    imageUrl: null
  })
}

// 删除证书
function removeCertificate(index) {
  form.value.certificates.splice(index, 1)
}

// 添加教育经历
function handleAddEducation() {
  form.value.educations = form.value.educations || []
  form.value.educations.push({
    school: '',
    major: '',
    degree: '',
    startYear: '',
    endYear: '',
    certificate: null
  })
}

// 删除教育经历
function removeEducation(index) {
  form.value.educations.splice(index, 1)
}

// 添加督导经历
function handleAddSupervision() {
  form.value.supervisions = form.value.supervisions || []
  form.value.supervisions.push({
    type: '',
    hours: 0,
    supervisor: '',
    startDate: null,
    endDate: null,
    certificate: null
  })
}

// 删除督导经历
function removeSupervision(index) {
  form.value.supervisions.splice(index, 1)
}

// 添加培训经历
function handleAddTraining() {
  form.value.trainings = form.value.trainings || []
  form.value.trainings.push({
    name: '',
    organizer: '',
    hours: 0,
    startDate: null,
    endDate: null,
    certificate: null
  })
}

// 删除培训经历
function removeTraining(index) {
  form.value.trainings.splice(index, 1)
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .box-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        font-weight: bold;
      }
    }

    :deep(.el-date-editor.el-input),
    :deep(.el-date-editor--daterange) {
      width: 100%;
    }
  }

  .avatar-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 200px;
    height: 200px;
    text-align: center;
    line-height: 200px;
  }

  .avatar {
    width: 200px;
    height: 200px;
    display: block;
  }

  .certificate-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }

  .certificate-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    text-align: center;
    line-height: 80px;
  }

  .certificate {
    width: 80px;
    height: 80px;
    display: block;
    object-fit: cover;
  }

  .bottom-buttons {
    margin-top: 20px;
    text-align: center;
  }
}
</style>