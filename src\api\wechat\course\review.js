import request from '@/utils/request'

// 查询评价列表
export function listReview(query) {
  return request({
    url: '/system/review/list',
    method: 'get',
    params: query
  })
}

// 导出评价列表
export function exportReview(query) {
  return request({
    url: '/system/review/export',
    method: 'post',
    params: query
  })
}

// 获取评价详细信息
export function getReview(id) {
  return request({
    url: `/system/review/${id}`,
    method: 'get'
  })
}

// 获取评价详细信息（包含课程和用户信息）
export function getReviewDetails(id) {
  return request({
    url: `/system/review/details/${id}`,
    method: 'get'
  })
}

// 新增评价
export function addReview(data) {
  return request({
    url: '/system/review',
    method: 'post',
    data: data
  })
}

// 修改评价
export function updateReview(data) {
  return request({
    url: '/system/review',
    method: 'put',
    data: data
  })
}

// 删除评价
export function delReview(ids) {
  return request({
    url: `/system/review/${ids}`,
    method: 'delete'
  })
}

// 根据课程ID查询评价列表
export function getReviewsByCourse(courseId) {
  return request({
    url: `/system/review/course/${courseId}`,
    method: 'get'
  })
}

// 根据用户ID查询评价列表
export function getReviewsByUser(userId) {
  return request({
    url: `/system/review/user/${userId}`,
    method: 'get'
  })
}
