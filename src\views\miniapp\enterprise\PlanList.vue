<template>
  <div class="plan-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>测评计划管理</h1>
        <p>管理企业测评计划，跟踪测评进度和结果</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createPlan">
          <el-icon><Plus /></el-icon>
          创建计划
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-bar">
      <el-form :model="queryParams" inline>
        <el-form-item label="计划名称">
          <el-input
            v-model="queryParams.planName"
            placeholder="请输入计划名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="草稿" :value="0" />
            <el-option label="进行中" :value="1" />
            <el-option label="已暂停" :value="2" />
            <el-option label="已完成" :value="3" />
            <el-option label="已取消" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 计划列表 -->
    <div class="plan-grid" v-loading="loading">
      <div 
        v-for="plan in planList" 
        :key="plan.id"
        class="plan-card"
        @click="viewPlanDetail(plan)"
      >
        <div class="plan-header">
          <div class="plan-title">
            <h3>{{ plan.planName }}</h3>
            <el-tag :type="getStatusType(plan.status)">{{ getStatusText(plan.status) }}</el-tag>
          </div>
          <div class="plan-actions" @click.stop>
            <el-dropdown @command="handleCommand">
              <el-button text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="plan.status === 0" :command="`start-${plan.id}`">启动计划</el-dropdown-item>
                  <el-dropdown-item v-if="plan.status === 1" :command="`pause-${plan.id}`">暂停计划</el-dropdown-item>
                  <el-dropdown-item v-if="plan.status === 2" :command="`resume-${plan.id}`">恢复计划</el-dropdown-item>
                  <el-dropdown-item v-if="[1, 2].includes(plan.status)" :command="`end-${plan.id}`">结束计划</el-dropdown-item>
                  <el-dropdown-item :command="`edit-${plan.id}`">编辑计划</el-dropdown-item>
                  <el-dropdown-item :command="`participants-${plan.id}`">参与者管理</el-dropdown-item>
                  <el-dropdown-item :command="`stats-${plan.id}`">统计报告</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${plan.id}`" divided>删除计划</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div class="plan-content">
          <div class="plan-info">
            <div class="info-item">
              <span class="label">量表：</span>
              <span class="value">{{ plan.scaleName }}</span>
            </div>
            <div class="info-item">
              <span class="label">参与人数：</span>
              <span class="value">{{ plan.participantCount }}人</span>
            </div>
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDate(plan.createTime) }}</span>
            </div>
            <div class="info-item" v-if="plan.deadline">
              <span class="label">截止时间：</span>
              <span class="value">{{ formatDate(plan.deadline) }}</span>
            </div>
          </div>

          <div class="plan-progress">
            <div class="progress-header">
              <span>完成进度</span>
              <span>{{ plan.completedCount }}/{{ plan.participantCount }}</span>
            </div>
            <el-progress 
              :percentage="getProgressPercentage(plan)" 
              :color="getProgressColor(plan)"
            />
          </div>

          <div class="plan-description" v-if="plan.description">
            <p>{{ plan.description }}</p>
          </div>
        </div>

        <div class="plan-footer">
          <div class="plan-stats">
            <div class="stat-item">
              <span class="stat-value">{{ plan.completedCount }}</span>
              <span class="stat-label">已完成</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ plan.inProgressCount }}</span>
              <span class="stat-label">进行中</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ plan.notStartedCount }}</span>
              <span class="stat-label">未开始</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 创建计划对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建测评计划"
      width="600px"
      @close="resetCreateForm"
    >
      <el-form ref="createFormRef" :model="createForm" :rules="createRules" label-width="100px">
        <el-form-item label="计划名称" prop="planName">
          <el-input v-model="createForm.planName" placeholder="请输入计划名称" />
        </el-form-item>
        <el-form-item label="选择量表" prop="scaleId">
          <el-select v-model="createForm.scaleId" placeholder="请选择量表" style="width: 100%">
            <el-option
              v-for="scale in availableScales"
              :key="scale.id"
              :label="scale.name"
              :value="scale.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="截止时间" prop="deadline">
          <el-date-picker
            v-model="createForm.deadline"
            type="datetime"
            placeholder="请选择截止时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="计划描述">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入计划描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreate">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, MoreFilled } from '@element-plus/icons-vue'
import {
  listAssessmentPlans,
  createAssessmentPlan,
  startAssessmentPlan,
  pauseAssessmentPlan,
  resumeAssessmentPlan,
  endAssessmentPlan,
  deleteAssessmentPlan,
  getAvailableScales
} from '@/api/miniapp/enterprise'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const planList = ref([])
const total = ref(0)
const createDialogVisible = ref(false)
const availableScales = ref([])

const queryParams = reactive({
  pageNum: 1,
  pageSize: 12,
  planName: '',
  status: '',
  dateRange: []
})

const createForm = reactive({
  planName: '',
  scaleId: '',
  deadline: '',
  description: ''
})

const createRules = {
  planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
  scaleId: [{ required: true, message: '请选择量表', trigger: 'change' }]
}

// 方法
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getStatusType = (status) => {
  const typeMap = {
    0: 'info',     // 草稿
    1: 'success',  // 进行中
    2: 'warning',  // 已暂停
    3: 'success',  // 已完成
    4: 'danger'    // 已取消
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    0: '草稿',
    1: '进行中',
    2: '已暂停',
    3: '已完成',
    4: '已取消'
  }
  return textMap[status] || '未知'
}

const getProgressPercentage = (plan) => {
  if (plan.participantCount === 0) return 0
  return Math.round((plan.completedCount / plan.participantCount) * 100)
}

const getProgressColor = (plan) => {
  const percentage = getProgressPercentage(plan)
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  if (percentage >= 40) return '#f56c6c'
  return '#909399'
}

const getList = async () => {
  try {
    loading.value = true
    const params = { ...queryParams }
    if (params.dateRange && params.dateRange.length === 2) {
      params.startDate = params.dateRange[0]
      params.endDate = params.dateRange[1]
    }
    delete params.dateRange

    const response = await listAssessmentPlans(1, params) // 假设企业ID为1
    planList.value = response.rows
    total.value = response.total
  } catch (error) {
    ElMessage.error('加载计划列表失败')
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 12,
    planName: '',
    status: '',
    dateRange: []
  })
  getList()
}

const createPlan = async () => {
  try {
    const response = await getAvailableScales(1) // 假设企业ID为1
    availableScales.value = response.data
    createDialogVisible.value = true
  } catch (error) {
    ElMessage.error('加载可用量表失败')
  }
}

const handleCreate = async () => {
  try {
    await createAssessmentPlan({
      ...createForm,
      enterpriseId: 1 // 假设企业ID为1
    })
    ElMessage.success('创建成功')
    createDialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('创建失败')
  }
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    planName: '',
    scaleId: '',
    deadline: '',
    description: ''
  })
}

const handleCommand = async (command) => {
  const [action, planId] = command.split('-')
  
  try {
    switch (action) {
      case 'start':
        await startAssessmentPlan(planId)
        ElMessage.success('计划已启动')
        break
      case 'pause':
        await pauseAssessmentPlan(planId)
        ElMessage.success('计划已暂停')
        break
      case 'resume':
        await resumeAssessmentPlan(planId)
        ElMessage.success('计划已恢复')
        break
      case 'end':
        await endAssessmentPlan(planId)
        ElMessage.success('计划已结束')
        break
      case 'edit':
        router.push(`/enterprise-assessment/plan/${planId}/edit`)
        return
      case 'participants':
        router.push(`/enterprise-assessment/plan/${planId}/participants`)
        return
      case 'stats':
        router.push(`/enterprise-assessment/plan/${planId}/stats`)
        return
      case 'delete':
        await ElMessageBox.confirm('确定要删除这个计划吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteAssessmentPlan(planId)
        ElMessage.success('删除成功')
        break
    }
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const viewPlanDetail = (plan) => {
  router.push(`/enterprise-assessment/plan/${plan.id}`)
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.plan-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.plan-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.plan-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.plan-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 16px 0 16px;
}

.plan-title h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.plan-content {
  padding: 16px;
}

.plan-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item .label {
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
}

.plan-progress {
  margin-bottom: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.plan-description p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.plan-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.plan-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #666;
}
</style>
