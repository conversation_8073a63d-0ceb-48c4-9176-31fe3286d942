<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true" label-width="80px">
      <el-form-item label="图片名称" prop="imageName">
        <el-input v-model="queryParams.imageName" placeholder="请输入图片名称" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="排序号" prop="sortNum">
        <el-input-number v-model="queryParams.sortNum" placeholder="请输入排序号" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item> -->
      <!-- <el-form-item label="图片类型" prop="imageType">
        <el-select v-model="queryParams.imageType" placeholder="请选择图片类型" clearable style="width: 200px">
          <el-option v-for="dict in psy_image_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="状态" prop="enableStatus">
        <el-switch v-model="queryParams.enableStatus" active-value="0" inactive-value="1" active-text="正常"
          inactive-text="停用" />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 图片资源表格 -->
    <el-table v-loading="loading" :data="imageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="ID" prop="id" width="80" align="center" /> -->
      <el-table-column label="排序号" prop="sortNum" width="80" align="center" />
      <el-table-column label="图片名称" prop="imageName" :show-overflow-tooltip="true" />
      <el-table-column label="图片" prop="imageUrl" width="180" align="center">
        <template #default="scope">
          <div style="display: flex; gap: 4px;">
            <ImagePreview v-for="(img, idx) in (scope.row.imageUrl ? scope.row.imageUrl.split(',') : [])" :key="idx"
              :src="img" width="40" height="40" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="显示模式" prop="displayMode" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="displayModeType(row.displayMode)">{{ displayModeText(row.displayMode) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="100">
        <template #default="{ row }">
          <el-switch v-model="row.enableStatus" active-value="0" inactive-value="1"
            @change="handleStatusChange(row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" prop="createTime" width="180" align="center" />
      <el-table-column label="备注" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="图片名称" prop="imageName">
          <el-input v-model="form.imageName" placeholder="请输入图片名称" />
        </el-form-item>
        <el-form-item label="排序号" prop="sortNum">
          <el-input-number v-model="form.sortNum" :min="0" placeholder="请输入排序号" />
        </el-form-item>
        <el-form-item label="图片" prop="imageUrl">
          <ImageUpload v-model="form.imageUrl" :limit="1" />
        </el-form-item>
        <el-form-item label="显示模式" prop="displayMode">
          <el-radio-group v-model="form.displayMode">
            <el-radio value="scaleToFill">缩放填充</el-radio>
            <el-radio value="aspectFit">保持比例</el-radio>
            <el-radio value="aspectFill">保持比例填充</el-radio>
            <el-radio value="widthFix">宽度不变</el-radio>
            <el-radio value="heightFix">高度不变</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="预览效果">
          <div class="preview-container">
            <img v-if="form.imageUrl && form.imageUrl.length > 0"
              :src="Array.isArray(form.imageUrl) ? form.imageUrl[0] : form.imageUrl" class="preview-image" :style="{
                objectFit: getObjectFit(form.displayMode),
                width: form.displayMode === 'widthFix' ? '200px' : 'auto',
                height: form.displayMode === 'heightFix' ? '150px' : 'auto',
                maxWidth: '100%',
                maxHeight: '300px'
              }" />
            <div v-else class="preview-placeholder">上传图片后在此处预览</div>
          </div>
        </el-form-item>
        <el-form-item label="状态" prop="enableStatus">
          <el-switch v-model="form.enableStatus" active-value="0" inactive-value="1" active-text="正常"
            inactive-text="停用" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue'
import { listImageResource, getImageResource, addImageResource, updateImageResource, delImageResource } from '@/api/wechat/Image-management'
const { proxy } = getCurrentInstance()
// const { psy_image_type } = proxy.useDict('psy_image_type')

const imageList = ref([])
const loading = ref(false)
const showSearch = ref(true)
const total = ref(0)
const open = ref(false)
const title = ref('')
const ids = ref([])
const single = ref(true)
const multiple = ref(true)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    imageName: undefined,
    // imageType: undefined,
    sortNum: undefined,
    enableStatus: undefined
  },
  rules: {
    imageName: [{ required: true, message: '图片名称不能为空', trigger: 'blur' }],
    imageUrl: [{ required: true, message: '请上传图片', trigger: 'change' }],
    sortNum: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
    enableStatus: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }
})

const { form, queryParams, rules } = toRefs(data)

function getList() {
  loading.value = true
  listImageResource(queryParams.value).then(res => {
    imageList.value = res.data
    total.value = res.data?.length || 0
    loading.value = false
  })
}

function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

function handleAdd() {
  title.value = '新增图片'
  open.value = true
  form.value = { enableStatus: '0', imageUrl: [], sortNum: 0, displayMode: 'aspectFit' }
}

function handleUpdate(row) {
  const id = row?.id || ids.value[0]
  getImageResource(id).then(res => {
    const data = { ...res.data }
    data.imageUrl = data.imageUrl ? data.imageUrl.split(',') : []
    form.value = data
    title.value = '编辑图片'
    open.value = true
  })
}

function handleDelete(row) {
  const delIds = row?.id ? [row.id] : ids.value
  proxy.$modal.confirm('是否确认删除选中的图片资源？').then(() => {
    return delImageResource(delIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess('删除成功')
  })
}

function submitForm() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      const submitData = {
        ...form.value,
        imageUrl: Array.isArray(form.value.imageUrl) ? form.value.imageUrl.join(',') : form.value.imageUrl
      }
      const api = form.value.id ? updateImageResource : addImageResource
      api(submitData).then(() => {
        proxy.$modal.msgSuccess('操作成功')
        open.value = false
        getList()
      })
    }
  })
}

function cancel() {
  open.value = false
}

function handleStatusChange(row) {
  let text = row.enableStatus === "0" ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.imageName + '"吗?').then(() => {
    return updateImageResource(row);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(() => {
    row.enableStatus = row.enableStatus === "0" ? "1" : "0";
  });
}

// 显示模式文本
function displayModeText(mode) {
  const modeMap = {
    'scaleToFill': '缩放填充',
    'aspectFit': '保持比例',
    'aspectFill': '保持比例填充',
    'widthFix': '宽度不变',
    'heightFix': '高度不变'
  }
  return modeMap[mode] || '未知'
}

// 显示模式标签类型
function displayModeType(mode) {
  const typeMap = {
    'scaleToFill': 'warning',
    'aspectFit': '',
    'aspectFill': 'success',
    'widthFix': 'info',
    'heightFix': 'info'
  }
  return typeMap[mode] || 'info'
}

// 获取 object-fit 样式
function getObjectFit(mode) {
  const fitMap = {
    'scaleToFill': 'fill',
    'aspectFit': 'contain',
    'aspectFill': 'cover',
    'widthFix': 'contain',
    'heightFix': 'contain'
  }
  return fitMap[mode] || 'contain'
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.mb8 {
  margin-bottom: 8px;
}

.preview-container {
  border: 1px solid #dcdfe6;
  padding: 10px;
  border-radius: 4px;
  min-height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f7fa;
  width: 100%;
}

.preview-placeholder {
  color: #909399;
  font-size: 14px;
}

.preview-image {
  display: block;
}
</style>
