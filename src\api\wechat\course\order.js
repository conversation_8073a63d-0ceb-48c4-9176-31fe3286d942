import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/system/order/list',
    method: 'get',
    params: query
  })
}

// 导出订单列表
export function exportOrder(query) {
  return request({
    url: '/system/order/export',
    method: 'post',
    params: query
  })
}

// 获取订单详细信息
export function getOrder(id) {
  return request({
    url: `/system/order/${id}`,
    method: 'get'
  })
}

// 获取订单详细信息（包含课程和用户信息）
export function getOrderDetails(id) {
  return request({
    url: `/system/order/details/${id}`,
    method: 'get'
  })
}

// 根据订单号查询订单
export function getOrderByOrderNo(orderNo) {
  return request({
    url: `/system/order/orderNo/${orderNo}`,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/system/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/system/order',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder(ids) {
  return request({
    url: `/system/order/${ids}`,
    method: 'delete'
  })
}

// 更新订单支付状态
export function updateOrderPaymentStatus(orderNo, status, paymentMethod, transactionId) {
  return request({
    url: `/system/order/payment/${orderNo}`,
    method: 'put',
    params: {
      status: status,
      paymentMethod: paymentMethod,
      transactionId: transactionId
    }
  })
}

// 订单退款
export function refundOrder(orderNo, refundAmount) {
  return request({
    url: `/system/order/refund/${orderNo}`,
    method: 'put',
    params: {
      refundAmount: refundAmount
    }
  })
}

// 生成订单号
export function generateOrderNo() {
  return request({
    url: '/system/order/generateOrderNo',
    method: 'get'
  })
}
