<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="量表" prop="scaleId">
        <el-select v-model="queryParams.scaleId" placeholder="请选择量表" clearable style="width: 240px">
          <el-option v-for="scale in scaleOptions" :key="scale.id" :label="scale.scaleName" :value="scale.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="评分" prop="rating">
        <el-select v-model="queryParams.rating" placeholder="请选择评分" clearable style="width: 240px">
          <el-option label="1星" value="1" />
          <el-option label="2星" value="2" />
          <el-option label="3星" value="3" />
          <el-option label="4星" value="4" />
          <el-option label="5星" value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option v-for="dict in review_status_options" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="评价时间" style="width: 308px">
        <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="Check" @click="handleBatchAudit(1)" :disabled="multiple"
          v-hasPermi="['system:assessment:review:edit']">批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Close" @click="handleBatchAudit(2)" :disabled="multiple"
          v-hasPermi="['system:assessment:review:edit']">批量拒绝</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:assessment:review:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reviewList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="量表名称" align="center" width="150">
        <template #default="scope">
          <span>{{ getScaleNameById(scope.row.scaleId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" width="120">
        <template #default="scope">
          <span>{{ getUserNameById(scope.row.userId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评分" align="center" prop="rating" width="120">
        <template #default="scope">
          <el-rate v-model="scope.row.rating" disabled show-score text-color="#ff9900" score-template="{value}" />
        </template>
      </el-table-column>
      <el-table-column label="评价内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="是否匿名" align="center" prop="isAnonymous" width="80">
        <template #default="scope">
          <dict-tag :options="yes_no_options" :value="scope.row.isAnonymous" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="review_status_options" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="评价时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:assessment:review:query']">详情</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="scope.row.status === 0 && checkPermi(['system:assessment:review:edit'])"
                  command="audit" icon="Check">审核</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status === 1 && checkPermi(['system:assessment:review:edit'])"
                  command="reply" icon="ChatDotRound">回复</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:review:remove'])" command="delete"
                  icon="Delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 评价详情对话框 -->
    <el-dialog title="评价详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="量表名称">{{ getScaleNameById(detailForm.scaleId) }}</el-descriptions-item>
        <el-descriptions-item label="用户">{{ getUserNameById(detailForm.userId) }}</el-descriptions-item>
        <el-descriptions-item label="评分">
          <el-rate v-model="detailForm.rating" disabled show-score text-color="#ff9900" score-template="{value}" />
        </el-descriptions-item>
        <el-descriptions-item label="是否匿名">
          <dict-tag :options="yes_no_options" :value="detailForm.isAnonymous" />
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="review_status_options" :value="detailForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="评价时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="评价内容" :span="2">{{ detailForm.content }}</el-descriptions-item>
        <el-descriptions-item v-if="detailForm.auditTime" label="审核时间" :span="2">{{ parseTime(detailForm.auditTime)
        }}</el-descriptions-item>
        <el-descriptions-item v-if="detailForm.auditRemark" label="审核备注" :span="2">{{ detailForm.auditRemark
        }}</el-descriptions-item>
        <el-descriptions-item v-if="detailForm.replyContent" label="回复内容" :span="2">{{ detailForm.replyContent
        }}</el-descriptions-item>
        <el-descriptions-item v-if="detailForm.replyTime" label="回复时间" :span="2">{{ parseTime(detailForm.replyTime)
        }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="评价审核" v-model="auditOpen" width="600px" append-to-body>
      <el-form ref="auditRef" :model="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="评价内容">
          <div class="review-content">{{ auditForm.content }}</div>
        </el-form-item>
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :value="1">通过</el-radio>
            <el-radio :value="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitAudit">确 定</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 回复对话框 -->
    <el-dialog title="评价回复" v-model="replyOpen" width="600px" append-to-body>
      <el-form ref="replyRef" :model="replyForm" :rules="replyRules" label-width="100px">
        <el-form-item label="评价内容">
          <div class="review-content">{{ replyForm.content }}</div>
        </el-form-item>
        <el-form-item label="回复内容" prop="replyContent">
          <el-input v-model="replyForm.replyContent" type="textarea" placeholder="请输入回复内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitReply">确 定</el-button>
          <el-button @click="replyOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Review">
import { listReview, getReview, delReview, exportReview, auditReview, replyReview } from "@/api/system/assessment/review";
import { listScale } from "@/api/system/assessment/scale";
import { listUser } from "@/api/system/user";
import { checkPermi } from "@/utils/permission";

const { proxy } = getCurrentInstance();
const { review_status_options, yes_no_options } = proxy.useDict('review_status', 'sys_yes_no');

const reviewList = ref([]);
const scaleOptions = ref([]);
const userList = ref([]);
const detailOpen = ref(false);
const auditOpen = ref(false);
const replyOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    scaleId: null,
    userId: null,
    rating: null,
    status: null
  },
  detailForm: {},
  auditForm: {
    id: null,
    status: 1,
    auditRemark: null,
    content: null
  },
  replyForm: {
    id: null,
    replyContent: null,
    content: null
  },
  auditRules: {
    status: [
      { required: true, message: "审核结果不能为空", trigger: "change" }
    ],
    auditRemark: [
      { required: true, message: "审核备注不能为空", trigger: "blur" }
    ]
  },
  replyRules: {
    replyContent: [
      { required: true, message: "回复内容不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, detailForm, auditForm, replyForm, auditRules, replyRules } = toRefs(data);

/** 查询评价列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  if (null != dateRange && '' != dateRange) {
    queryParams.value.params["beginTime"] = dateRange.value[0];
    queryParams.value.params["endTime"] = dateRange.value[1];
  }
  listReview(queryParams.value).then(response => {
    reviewList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询量表列表 */
function getScaleList() {
  listScale().then(response => {
    scaleOptions.value = response.rows;
  });
}

/** 查询用户列表 */
function getUserList() {
  listUser().then(response => {
    userList.value = response.rows;
  });
}

/** 根据量表ID获取量表名称 */
function getScaleNameById(scaleId) {
  if (!scaleId) return '-';
  const scale = scaleOptions.value.find(item => item.id === scaleId);
  return scale ? scale.scaleName : `量表ID: ${scaleId}`;
}

/** 根据用户ID获取用户名称 */
function getUserNameById(userId) {
  if (!userId) return '-';
  const user = userList.value.find(item => item.userId === userId);
  return user ? (user.nickName || user.userName) : `用户ID: ${userId}`;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 详情按钮操作 */
function handleDetail(row) {
  getReview(row.id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'audit':
      handleAudit(row);
      break;
    case 'reply':
      handleReply(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
  }
}

/** 审核按钮操作 */
function handleAudit(row) {
  auditForm.value = {
    id: row.id,
    status: 1,
    auditRemark: null,
    content: row.content
  };
  auditOpen.value = true;
}

/** 批量审核操作 */
function handleBatchAudit(status) {
  if (ids.value.length === 0) {
    proxy.$modal.msgError("请选择需要审核的评价");
    return;
  }

  const statusText = status === 1 ? "通过" : "拒绝";
  proxy.$modal.prompt(`请输入批量${statusText}的审核备注`, "审核备注").then(({ value }) => {
    const promises = ids.value.map(id => {
      return auditReview(id, status, value);
    });

    Promise.all(promises).then(() => {
      getList();
      proxy.$modal.msgSuccess(`批量${statusText}成功`);
    }).catch(() => { });
  }).catch(() => { });
}

/** 回复按钮操作 */
function handleReply(row) {
  replyForm.value = {
    id: row.id,
    replyContent: row.replyContent || null,
    content: row.content
  };
  replyOpen.value = true;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除评价编号为"' + _ids + '"的数据项？').then(function () {
    return delReview(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/assessment/review/export', {
    ...queryParams.value
  }, `review_${new Date().getTime()}.xlsx`)
}

/** 提交审核 */
function submitAudit() {
  proxy.$refs["auditRef"].validate(valid => {
    if (valid) {
      auditReview(auditForm.value.id, auditForm.value.status, auditForm.value.auditRemark).then(response => {
        proxy.$modal.msgSuccess("审核成功");
        auditOpen.value = false;
        getList();
      });
    }
  });
}

/** 提交回复 */
function submitReply() {
  proxy.$refs["replyRef"].validate(valid => {
    if (valid) {
      replyReview(replyForm.value.id, replyForm.value.replyContent).then(response => {
        proxy.$modal.msgSuccess("回复成功");
        replyOpen.value = false;
        getList();
      });
    }
  });
}

onMounted(() => {
  getList();
  getScaleList();
  getUserList();
});
</script>

<style scoped>
.review-content {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  min-height: 60px;
  max-height: 150px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
