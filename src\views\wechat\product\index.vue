<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true" label-width="68px">
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-tree-select v-model="queryParams.categoryId" :data="categoryOptions"
          :props="{ label: 'categoryName', value: 'categoryId', children: 'children' }" value-key="categoryId"
          placeholder="请选择分类" clearable style="width: 240px" />
      </el-form-item>
      <el-form-item label="服务方式" prop="serviceMethod">
        <el-select v-model="queryParams.serviceMethod" placeholder="请选择服务方式" clearable style="width: 240px">
          <el-option v-for="dict in psy_service_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="disableFlag">
        <el-select v-model="queryParams.disableFlag" placeholder="产品状态" clearable style="width: 240px">
          <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
          start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['psy:product:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['psy:product:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['psy:product:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['psy:product:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 产品表格 -->
    <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="产品ID" prop="productId" width="120" />
      <el-table-column label="产品名称" prop="productName" :show-overflow-tooltip="true" width="400" />
      <el-table-column label="原价" align="center" width="120">
        <template #default="{ row }">￥{{ row.originalPrice?.toFixed(2) }}</template>
      </el-table-column>
      <el-table-column label="折扣价" align="center" width="120">
        <template #default="{ row }">￥{{ row.discountPrice?.toFixed(2) }}</template>
      </el-table-column>
      <el-table-column label="服务方式" width="140">
        <template #default="{ row }">
          {{ formatServiceMethod(row.serviceMethod) }}
        </template>
      </el-table-column>
      <el-table-column label="有效期(天)" prop="validityPeriod" align="center" width="120" />
      <el-table-column label="状态" align="center" width="100">
        <template #default="{ row }">
          <el-switch v-model="row.disableFlag" active-value="0" inactive-value="1"
            @change="handleStatusChange(row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="{ row }">
          <span>{{ parseTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 新增/修改对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务方式" prop="serviceMethod">
              <el-select v-model="form.serviceMethod" multiple placeholder="请选择服务方式" style="width: 100%">
                <el-option v-for="item in psy_service_type" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原价" prop="originalPrice">
              <el-input-number v-model="form.originalPrice" :min="0.01" :precision="2" :step="100"
                controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折扣率" prop="discountRate">
              <el-input-number v-model="form.discountRate" :min="0" :max="1" :step="0.1" :precision="2"
                controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <!-- <el-form-item label="服务项目" prop="serviceItems">
              <el-select v-model="form.serviceItems" multiple filterable placeholder="请选择服务项目" style="width: 100%">
                <el-option v-for="item in serviceOptions" :key="item.itemId" :label="item.itemName"
                  :value="item.itemId" />
              </el-select>
            </el-form-item> -->
          </el-col>
          <el-col :span="24">
            <el-form-item label="产品描述">
              <el-input v-model="form.supplementInfo" type="textarea" :rows="3" placeholder="请输入产品描述" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 新增服务项目区块 -->
        <div class="service-item-container" v-for="(item, index) in form.serviceItems" :key="item.itemId">
          <div class="service-item-header">
            <span class="item-title">{{ item.itemName }}</span>
            <el-button type="danger" size="small" icon="Delete" circle @click="removeServiceItem(index)" class="ml-2" />
          </div>

          <!-- 服务项目表单 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="项目名称" :prop="`serviceItems[${index}].category`" required>
                <el-input v-model="item.itemName" placeholder="请输入项目名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目分类" :prop="`serviceItems[${index}].category`" required>
                <el-select v-model="item.category" placeholder="请选择分类" style="width:100%">
                  <el-option v-for="opt in psy_project_classification" :key="opt.value" :label="opt.label"
                    :value="opt.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="门店售价" :prop="`serviceItems[${index}].price`" required>
                <el-input-number v-model="item.price" :min="0.01" :precision="2" controls-position="right"
                  style="width:100%">
                  <template #append>元</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="包含数量" :prop="`serviceItems[${index}].quantity`" required>
                <el-input v-model="item.quantity" placeholder="例: 一份" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 服务内容子项 -->
          <div class="service-content-container">
            <div class="content-item" v-for="(content, cIndex) in item.contents" :key="content.contentId">
              <el-row :gutter="20">
                <el-col :span="10">
                  <el-form-item :label="`内容${cIndex + 1}`" :prop="`serviceItems[${index}].contents[${cIndex}].content`">
                    <el-input v-model="content.content" placeholder="服务内容名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="持续时间" :prop="`serviceItems[${index}].contents[${cIndex}].duration`">
                    <el-input v-model="content.duration" placeholder="例: 60分钟" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-button type="danger" icon="Delete" circle size="small" @click="removeContent(index, cIndex)" />
                </el-col>
              </el-row>
            </div>
            <el-button type="primary" size="small" plain icon="Plus" @click="addContent(index)">
              添加服务内容
            </el-button>
          </div>
        </div>

        <!-- 添加服务项目按钮 -->
        <div class="mt-16">
          <el-button type="primary" plain icon="Plus" @click="addServiceItem">
            添加服务项目
          </el-button>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getCurrentInstance, ref, reactive, nextTick, onMounted } from 'vue'
import { listProduct, getProduct, addProduct, updateProduct, delProduct } from '@/api/wechat/product'
import { optionselect as getDictOptionselect, getType } from "@/api/system/dict/type";
import { useRouter } from 'vue-router'
import { listCategory } from '@/api/wechat/category'

const { proxy } = getCurrentInstance()
const { sys_normal_disable, psy_project_classification, psy_service_type } = proxy.useDict('sys_normal_disable', 'psy_project_classification', 'psy_service_type')
const serviceCategories = ref([])
const typeOptions = ref([])
const productList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const dateRange = ref([])
const serviceOptions = ref([])
const router = useRouter()

// 分类选项
const categoryOptions = ref([])

// 表单验证规则
const rules = reactive({
  productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  serviceMethod: [{ required: true, message: '请选择服务方式', trigger: 'change' }],
  originalPrice: [
    { required: true, message: '请输入产品价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于0' }
  ]
})

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productName: undefined,
    categoryId: undefined,
    serviceMethod: undefined,
    disableFlag: undefined
  }
})

const { queryParams, form } = toRefs(data)

/** 查询产品列表 */
function getList() {
  loading.value = true
  listProduct(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    productList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 状态修改 */
function handleStatusChange(row) {
  const text = row.disableFlag === '0' ? '启用' : '停用'
  proxy.$modal.confirm(`确认要${text}"${row.productName}"吗?`).then(() => {
    return updateProduct({ ...row, disableFlag: row.disableFlag })
  }).then(() => {
    proxy.$modal.msgSuccess(`${text}成功`)
  }).catch(() => {
    row.disableFlag = row.disableFlag === '0' ? '1' : '0'
  })
}

// 在setup脚本中添加以下方法
const addServiceItem = () => {
  form.value.serviceItems.push({
    itemId: null,
    category: '',
    price: 0,
    quantity: 1,
    contents: [{
      content: '',
      duration: '',
      delFlag: '0'
    }]
  })
}

const removeServiceItem = (index) => {
  form.value.serviceItems.splice(index, 1)
}

const addContent = (itemIndex) => {
  form.value.serviceItems[itemIndex].contents.push({
    content: '',
    duration: '',
    delFlag: '0'
  })
}

const removeContent = (itemIndex, contentIndex) => {
  form.value.serviceItems[itemIndex].contents.splice(contentIndex, 1)
}


/** 删除按钮操作 */
function handleDelete(row) {
  const productIds = row.productId || ids.value
  proxy.$modal.confirm(`是否确认删除产品ID为"${productIds}"的数据项?`).then(() => {
    return delProduct(productIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess('删除成功')
  }).catch(() => { })
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.productId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  router.push('/wechat/product/detail')
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const productId = row.productId || ids.value[0]
  router.push(`/wechat/product/detail?id=${productId}`)
}

/** 加载服务项目选项 */
function loadServiceOptions() {
  // listServiceItems().then(response => {
  //   serviceOptions.value = response.rows
  // })
}

/** 提交表单 */
function submitForm() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      const submitData = {
        ...form.value,
        serviceMethod: form.value.serviceMethod.join(',')
      }
      const api = form.value.productId ? updateProduct : addProduct
      api(submitData).then(() => {
        proxy.$modal.msgSuccess('操作成功')
        open.value = false
        getList()
      })
    }
  })
}

function cancel() {
  open.value = false
}

/** 重置表单 */
function reset() {
  form.value = {
    productId: undefined,
    productName: undefined,
    serviceMethod: undefined,
    originalPrice: 0,
    discountRate: 1,
    serviceItems: [],
    supplementInfo: undefined
  }
  proxy.resetForm('formRef')
}

// 修改格式化方法
function formatServiceMethod(methodStr) {
  if (!methodStr) return '-'
  const methods = methodStr.split(',')
  return methods.map(m => {
    const found = psy_service_type.value.find(item => item.value === m)
    return found ? found.label : m
  }).join(' , ')
}

/** 查询字典类型列表 */
function getTypeList() {
  getDictOptionselect().then(response => {
    typeOptions.value = response.data;
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/role/export", {
    ...queryParams.value,
  }, `role_${new Date().getTime()}.xlsx`);
}

// 格式化方法
const formatDict = (value) => {
  const dict = typeOptions.value.find(d => d.dictType === value)
  if (dict) {
    const { [value]: dictValue } = proxy.useDict(value)
  }
  return dict?.dictName || value
}

const formatServiceDirection = (dictType, values) => {
  if (!values || !dictType) return '-'
  const { [dictType]: dictValue } = proxy.useDict(dictType)
  return values.split(',')
    .map(v => {
      const item = [dictValue][0].value.find(d => d.value == v)
      return item?.label || v
    })
    .join(', ')
}

// 获取分类列表
const getCategoryList = async () => {
  try {
    const res = await listCategory()
    categoryOptions.value = res.data
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 在onMounted中调用
onMounted(() => {
  getList()
  getCategoryList()
})

getTypeList()
</script>

<style scoped lang="scss">
.service-item-container {
  margin: 16px 0;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.service-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.item-title {
  font-weight: 600;
  color: #303133;
}

.service-content-container {
  margin: 12px 0 0 24px;
  padding-left: 24px;
  border-left: 2px solid #f0f2f5;
}

.content-item {
  margin-bottom: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.mt-16 {
  margin-top: 16px;
}

.ml-2 {
  margin-left: 8px;
}
</style>