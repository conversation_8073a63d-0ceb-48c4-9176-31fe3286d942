# 导出接口路径修正说明

## 问题概述

在项目的多个页面中，导出功能的接口路径缺少 `system/` 前缀，导致导出功能无法正常工作。需要统一修正这些接口路径。

## 问题原因

后端导出接口统一使用 `system/` 作为路径前缀，但前端部分页面在调用导出接口时没有添加这个前缀，导致接口路径不匹配。

## 修正内容

### 1. 咨询系统相关导出

#### 咨询记录导出
**文件**：`src/views/wechat/consultation/record/index.vue`
```javascript
// 修正前
proxy.download('consultationRecord/export', {

// 修正后  
proxy.download('system/consultationRecord/export', {
```

#### 咨询师评价导出
**文件**：`src/views/wechat/consultation/review/index.vue`
```javascript
// 修正前
proxy.download('consultantReview/export', {

// 修正后
proxy.download('system/consultantReview/export', {
```

#### 中断记录导出
**文件**：`src/views/wechat/consultation/interruption/index.vue`
```javascript
// 修正前
proxy.download('consultantInterruption/export', {

// 修正后
proxy.download('system/consultantInterruption/export', {
```

### 2. 课程系统相关导出

#### 课程导出
**文件**：`src/views/wechat/course/course/index.vue`
```javascript
// 修正前
proxy.download('course/export', {

// 修正后
proxy.download('system/course/export', {
```

#### 课程讲师导出
**文件**：`src/views/wechat/course/instructor/index.vue`
```javascript
// 修正前
proxy.download('instructor/export', {

// 修正后
proxy.download('system/instructor/export', {
```

#### 课程订单导出
**文件**：`src/views/wechat/course/order/index.vue`
```javascript
// 修正前
proxy.download('order/export', {

// 修正后
proxy.download('system/order/export', {
```

#### 课程评价导出
**文件**：`src/views/wechat/course/review/index.vue`
```javascript
// 修正前
proxy.download('review/export', {

// 修正后
proxy.download('system/review/export', {
```

### 3. 冥想系统相关导出

#### 冥想导出
**文件**：`src/views/wechat/meditation/meditation/index.vue`
```javascript
// 修正前
proxy.download('meditation/export', {

// 修正后
proxy.download('system/meditation/export', {
```

#### 冥想订单导出
**文件**：`src/views/wechat/meditation/order/index.vue`
```javascript
// 修正前
proxy.download('meditationOrder/export', {

// 修正后
proxy.download('system/meditationOrder/export', {
```

#### 冥想评价导出
**文件**：`src/views/wechat/meditation/review/index.vue`
```javascript
// 修正前
proxy.download('meditationReview/export', {

// 修正后
proxy.download('system/meditationReview/export', {
```

### 4. 其他系统相关导出

#### 门店导出
**文件**：`src/views/wechat/store/index.vue`
```javascript
// 修正前
proxy.download("wechat/store/export", {

// 修正后
proxy.download("system/store/export", {
```

## 已经正确的导出接口

以下接口已经使用了正确的 `system/` 前缀，无需修改：

### 系统管理模块
- `system/dict/type/export` - 字典类型导出
- `system/dict/data/export` - 字典数据导出
- `system/config/export` - 参数配置导出
- `system/post/export` - 岗位导出
- `system/user/importTemplate` - 用户导入模板
- `system/role/export` - 角色导出

### 监控模块
- `monitor/operlog/export` - 操作日志导出
- `monitor/logininfor/export` - 登录日志导出
- `monitor/job/export` - 定时任务导出
- `monitor/jobLog/export` - 调度日志导出

### 微信模块
- `system/appointment/export` - 预约导出
- `system/timeRange/export` - 时间段导出
- `system/consultant/export` - 咨询师导出

## 修正统计

### 修正的文件数量
- **总计修正文件**：9个
- **咨询系统**：3个文件
- **课程系统**：4个文件
- **冥想系统**：3个文件
- **其他系统**：1个文件

### 修正的接口数量
- **总计修正接口**：10个导出接口

## 接口路径规范

### 标准格式
```javascript
proxy.download("system/模块名/export", {
  ...queryParams.value
}, `文件名_${new Date().getTime()}.xlsx`)
```

### 路径组成
- **前缀**：`system/` - 统一的系统前缀
- **模块名**：具体的业务模块名称
- **操作**：`export` - 导出操作

### 文件命名规范
- **格式**：`模块名_时间戳.xlsx`
- **示例**：`course_1703123456789.xlsx`

## 验证方法

### 1. 功能测试
1. 进入各个管理页面
2. 点击"导出"按钮
3. 验证文件是否正常下载
4. 检查导出的Excel文件内容是否正确

### 2. 网络请求检查
1. 打开浏览器开发者工具
2. 切换到Network标签
3. 点击导出按钮
4. 检查请求URL是否包含正确的 `system/` 前缀
5. 验证响应状态码是否为200

### 3. 错误日志检查
- 检查浏览器控制台是否有404错误
- 检查后端日志是否有接口不存在的错误

## 注意事项

### 1. 接口一致性
- 确保前端调用的接口路径与后端提供的接口路径完全一致
- 注意大小写敏感性

### 2. 参数传递
- 确保查询参数正确传递给导出接口
- 验证日期范围、筛选条件等参数的格式

### 3. 文件下载
- 确保浏览器允许文件下载
- 检查下载文件的完整性

### 4. 权限验证
- 确保用户有相应模块的导出权限
- 检查JWT token的有效性

## 后续维护

### 1. 新增导出功能
在添加新的导出功能时，请遵循以下规范：
```javascript
// 标准模板
function handleExport() {
  proxy.download("system/模块名/export", {
    ...queryParams.value
  }, `模块名_${new Date().getTime()}.xlsx`)
}
```

### 2. 代码审查
- 在代码审查时检查导出接口路径是否正确
- 确保新增的导出功能遵循统一的路径规范

### 3. 文档更新
- 及时更新API文档中的接口路径
- 保持前后端接口文档的一致性

## 总结

通过统一修正导出接口路径，解决了以下问题：

1. **功能可用性**：所有导出功能现在都能正常工作
2. **路径规范性**：统一使用 `system/` 前缀，符合后端接口规范
3. **维护便利性**：建立了清晰的接口路径规范，便于后续维护
4. **用户体验**：用户可以正常使用所有模块的导出功能

这次修正确保了系统导出功能的完整性和一致性，为用户提供了可靠的数据导出服务。
