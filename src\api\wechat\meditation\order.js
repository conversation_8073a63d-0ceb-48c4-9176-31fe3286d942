import request from '@/utils/request'

// 查询冥想订单列表
export function listMeditationOrder(query) {
  return request({
    url: '/system/meditationOrder/list',
    method: 'get',
    params: query
  })
}

// 导出冥想订单列表
export function exportMeditationOrder(query) {
  return request({
    url: '/system/meditationOrder/export',
    method: 'post',
    params: query
  })
}

// 获取冥想订单详细信息
export function getMeditationOrder(id) {
  return request({
    url: `/system/meditationOrder/${id}`,
    method: 'get'
  })
}

// 获取冥想订单详细信息（包含冥想和用户信息）
export function getMeditationOrderDetails(id) {
  return request({
    url: `/system/meditationOrder/details/${id}`,
    method: 'get'
  })
}

// 根据订单号查询冥想订单
export function getMeditationOrderByOrderNo(orderNo) {
  return request({
    url: `/system/meditationOrder/orderNo/${orderNo}`,
    method: 'get'
  })
}

// 新增冥想订单
export function addMeditationOrder(data) {
  return request({
    url: '/system/meditationOrder',
    method: 'post',
    data: data
  })
}

// 修改冥想订单
export function updateMeditationOrder(data) {
  return request({
    url: '/system/meditationOrder',
    method: 'put',
    data: data
  })
}

// 删除冥想订单
export function delMeditationOrder(ids) {
  return request({
    url: `/system/meditationOrder/${ids}`,
    method: 'delete'
  })
}

// 更新冥想订单支付状态
export function updateMeditationOrderPaymentStatus(orderNo, status, paymentMethod, transactionId) {
  return request({
    url: `/system/meditationOrder/payment/${orderNo}`,
    method: 'put',
    params: {
      status: status,
      paymentMethod: paymentMethod,
      transactionId: transactionId
    }
  })
}

// 冥想订单退款
export function refundMeditationOrder(orderNo, refundAmount) {
  return request({
    url: `/system/meditationOrder/refund/${orderNo}`,
    method: 'put',
    params: {
      refundAmount: refundAmount
    }
  })
}

// 生成冥想订单号
export function generateMeditationOrderNo() {
  return request({
    url: '/system/meditationOrder/generateOrderNo',
    method: 'get'
  })
}
