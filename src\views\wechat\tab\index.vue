<template>
  <div class="app-container">
    <div class="search-wrapper" :class="{ collapsed: !showSearch }">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入菜单名称" clearable style="width: 200px"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="权限" prop="permissions">
          <el-select v-model="queryParams.permissions" placeholder="请选择权限" clearable style="width: 200px">
            <el-option label="普通用户" value="wxuser" />
            <el-option label="咨询师" value="consultant" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="菜单状态" clearable style="width: 200px">
            <el-option v-for="dictData in normal_disabled_state" :key="dictData.value" :label="dictData.label"
              :value="dictData.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['wechat:tabbar:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['wechat:tabbar:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['wechat:tabbar:remove']">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="menuList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="菜单序号" align="center" prop="index" width="80" />
      <el-table-column label="菜单名称" align="center" prop="name" min-width="120" :show-overflow-tooltip="true" />
      <!-- <el-table-column label="图标" align="center" width="80">
        <template #default="{ row }">
          <el-image style="width: 25px; height: 25px" :src="row.img" fit="contain" />
        </template>
</el-table-column>
<el-table-column label="选中图标" align="center" width="80">
  <template #default="{ row }">
          <el-image style="width: 25px; height: 25px" :src="row.acImg" fit="contain" />
        </template>
</el-table-column> -->
      <el-table-column label="路由地址" align="center" prop="path" min-width="180" :show-overflow-tooltip="true" />
      <el-table-column label="权限标识" align="center" min-width="150" :show-overflow-tooltip="true">
        <template #default="{ row }">
          <el-tag v-if="row.permissions.includes('wxuser')" type="success" style="margin: 2px">普通用户</el-tag>
          <el-tag v-if="row.permissions.includes('consultant')" type="warning" style="margin: 2px">咨询师</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sortOrder" width="60" />
      <el-table-column label="状态" align="center" width="80">
        <template #default="{ row }">
          <el-switch v-model="row.status" active-value="0" inactive-value="1" @change="handleStatusChange(row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(row)"
              v-hasPermi="['wechat:tabbar:edit']"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(row)"
              v-hasPermi="['wechat:tabbar:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" /> -->

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="菜单序号" prop="index">
          <el-input-number v-model="form.index" controls-position="right" :min="1" :max="5" />
        </el-form-item>
        <el-form-item label="图标路径" prop="img">
          <el-input v-model="form.img" placeholder="请输入图标路径">
            <!-- <template #append>
              <el-image v-if="form.img" style="width: 25px; height: 25px" :src="form.img" fit="contain" />
            </template> -->
          </el-input>
        </el-form-item>
        <el-form-item label="选中图标路径" prop="acImg">
          <el-input v-model="form.acImg" placeholder="请输入选中状态图标路径">
            <!-- <template #append>
              <el-image v-if="form.acImg" style="width: 25px; height: 25px" :src="form.acImg" fit="contain" />
            </template> -->
          </el-input>
        </el-form-item>
        <el-form-item label="路由地址" prop="path">
          <el-input v-model="form.path" placeholder="请输入路由地址" />
        </el-form-item>
        <el-form-item label="权限标识" prop="permissions">
          <el-checkbox-group v-model="permissionsList">
            <el-checkbox label="wxuser" value="wxuser">普通用户</el-checkbox>
            <el-checkbox label="consultant" value="consultant">咨询师</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="显示排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in normal_disabled_state" :key="dict.value" :value="dict.value">{{ dict.label
              }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listMenu, addMenu, updateMenu, delMenu } from "@/api/wechat/tab"
import { useDict } from '@/utils/dict'

// 字典数据
const { sys_normal_disable, normal_disabled_state } = useDict('sys_normal_disable', 'normal_disabled_state')

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 菜单表格数据
const menuList = ref([])
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 权限列表
const permissionsList = ref([])

// 查询参数
const queryParams = reactive({
  name: undefined,
  permissions: undefined,
  status: undefined
})

// 表单参数
const form = reactive({
  id: undefined,
  index: 1,
  name: undefined,
  img: undefined,
  acImg: undefined,
  path: undefined,
  permissions: undefined,
  sortOrder: 0,
  status: "0"
})

// 表单校验规则
const rules = {
  name: [
    { required: true, message: "菜单名称不能为空", trigger: "blur" }
  ],
  index: [
    { required: true, message: "菜单序号不能为空", trigger: "blur" }
  ],
  img: [
    { required: true, message: "图标路径不能为空", trigger: "blur" }
  ],
  acImg: [
    { required: true, message: "选中图标路径不能为空", trigger: "blur" }
  ],
  path: [
    { required: true, message: "路由地址不能为空", trigger: "blur" }
  ],
  permissions: [
    { required: true, message: "权限标识不能为空", trigger: "change" }
  ]
}

// 表单引用
const formRef = ref(null)
const queryFormRef = ref(null)

// 监听权限列表变化，更新表单的permissions字段
watch(permissionsList, (value) => {
  form.permissions = value.join(',')
}, { deep: true })

/** 查询菜单列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await listMenu(queryParams)
    menuList.value = res.rows
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 选择条数  */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = "添加菜单"
}

/** 修改按钮操作 */
const handleUpdate = (row) => {
  reset()
  const menuId = row?.id || ids.value[0]
  const selectedRow = row || menuList.value.find(item => item.id === menuId)
  if (selectedRow) {
    Object.assign(form, selectedRow)
    // 手动触发权限列表更新
    if (selectedRow.permissions) {
      permissionsList.value = selectedRow.permissions.split(',').map(item => item.trim())
    }
    open.value = true
    title.value = "修改菜单"
  }
}

/** 提交按钮 */
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.id !== undefined) {
          await updateMenu(form)
          ElMessage.success("修改成功")
        } else {
          await addMenu(form)
          ElMessage.success("新增成功")
        }
        open.value = false
        getList()
      } catch (error) {
        console.error("操作失败", error)
      }
    }
  })
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const menuIds = row?.id || ids.value
  try {
    await ElMessageBox.confirm(`是否确认删除菜单编号为"${menuIds}"的数据项？`)
    await delMenu(menuIds)
    await getList()
    ElMessage.success("删除成功")
  } catch (error) {
    console.error("删除失败", error)
  }
}

/** 状态变更操作 */
const handleStatusChange = async (row) => {
  try {
    const statusText = row.status === "0" ? "启用" : "停用"
    await ElMessageBox.confirm(`确认要${statusText}菜单"${row.name}"吗？`)
    await updateMenu(row)
    ElMessage.success(`${statusText}成功`)
  } catch (error) {
    // 如果用户取消或操作失败，恢复原状态
    row.status = row.status === "0" ? "1" : "0"
    if (error !== 'cancel') {
      console.error("状态更新失败", error)
      ElMessage.error("状态更新失败")
    }
  }
}

/** 表单重置 */
const reset = () => {
  // 重置权限列表
  permissionsList.value = []

  // 重置表单数据
  Object.assign(form, {
    id: undefined,
    index: 1,
    name: undefined,
    img: undefined,
    acImg: undefined,
    path: undefined,
    permissions: undefined,
    sortOrder: 0,
    status: "0"
  })

  // 重置表单验证状态
  nextTick(() => {
    formRef.value?.resetFields()
  })
}

/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}


// 初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>
.el-tag {
  margin-right: 4px;
}

.el-tag+.el-tag {
  margin-left: 0;
}

.el-form-item {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  white-space: nowrap;
}

:deep(.el-dialog) {
  padding: 20px;
}

:deep(.el-input-number) {
  width: 120px;
}

:deep(.el-radio-group) {
  width: 100%;
}

:deep(.el-radio) {
  margin-right: 20px;
  margin-bottom: 10px;
}

.app-container {
  padding: 10px;
}

.search-wrapper {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  transition: all 0.3s;
}

.search-wrapper.collapsed {
  height: 0;
  padding: 0;
  overflow: hidden;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 12px;
  margin-bottom: 8px;
}

:deep(.el-form-item__label) {
  font-size: 13px;
  padding-right: 4px;
}

:deep(.el-input__wrapper) {
  padding-left: 8px;
  padding-right: 8px;
}
</style>
