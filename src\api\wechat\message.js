import request from '@/utils/request'

// 获取历史消息
export function getMessageHistory(conversationId, pageNum = 1, pageSize = 20) {
  return request({
    url: '/system/message/list/' + conversationId,
    method: 'GET',
    params: {
      pageNum,
      pageSize
    }
  })
}

// 分页获取会话列表
export function getMessageSessions() {
  return request({
    url: '/system/message/conversations',
    method: 'GET'
  })
}

// 获取咨询师会话列表
export function getConsultantSessions() {
  return request({
    url: '/system/message/consultant/conversations',
    method: 'GET'
  })
}

// 创建会话
export function createConversation(consultantId) {
  return request({
    url: `/system/message/conversation/${consultantId}`,
    method: 'POST'
  })
}

// 标记会话已读
export function markSessionRead(conversationId, isUser) {
  return request({
    url: `/system/message/read/all/${conversationId}`,
    method: 'PUT',
    data: {
      isUser
    }
  })
}

// 发送文本消息
export const sendMessageApi = (data) => {
  return request({
    url: '/system/message/send',
    method: 'POST',
    data
  })
}

// 发送图片消息
export function sendImage(file, receiverId) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('receiverId', receiverId)
  return request({
    url: '/system/message/sendImage',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 发送文件消息
export function sendFile(file, receiverId) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('receiverId', receiverId)
  return request({
    url: '/system/message/sendFile',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 撤回消息
export function withdrawMessage(messageId) {
  return request({
    url: `/system/message/withdraw/${messageId}`,
    method: 'PUT'
  })
}

// 标记消息已读
export function markMessageRead(messageId) {
  return request({
    url: `/system/message/read/${messageId}`,
    method: 'PUT'
  })
}

// 删除消息
export function deleteMessage(id) {
  return request({
    url: `/system/message/${id}`,
    method: 'DELETE'
  })
}

// 获取未读消息数量
export function getUnreadCount() {
  return request({
    url: '/system/message/unread/count',
    method: 'GET'
  })
}

// =================== 管理员专用接口 ===================

// 管理员获取所有会话列表
export function getAllConversations() {
  return request({
    url: '/system/message/admin/conversations',
    method: 'GET'
  })
}

// 获取会话详情
export function getConversationDetail(conversationId) {
  return request({
    url: `/system/message/conversation/${conversationId}`,
    method: 'GET'
  })
}

// 管理员获取会话详情
export function getAdminConversationDetail(conversationId) {
  return request({
    url: `/system/message/admin/conversation/${conversationId}`,
    method: 'GET'
  })
}
