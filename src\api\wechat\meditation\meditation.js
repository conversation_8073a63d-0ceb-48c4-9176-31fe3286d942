import request from '@/utils/request'

// 查询冥想列表
export function listMeditation(query) {
  return request({
    url: '/system/meditation/list',
    method: 'get',
    params: query
  })
}

// 导出冥想列表
export function exportMeditation(query) {
  return request({
    url: '/system/meditation/export',
    method: 'post',
    params: query
  })
}

// 获取冥想详细信息
export function getMeditation(id) {
  return request({
    url: `/system/meditation/${id}`,
    method: 'get'
  })
}

// 获取冥想详细信息（包含分类等）
export function getMeditationDetails(id) {
  return request({
    url: `/system/meditation/details/${id}`,
    method: 'get'
  })
}

// 新增冥想
export function addMeditation(data) {
  return request({
    url: '/system/meditation',
    method: 'post',
    data: data
  })
}

// 修改冥想
export function updateMeditation(data) {
  return request({
    url: '/system/meditation',
    method: 'put',
    data: data
  })
}

// 删除冥想
export function delMeditation(ids) {
  return request({
    url: `/system/meditation/${ids}`,
    method: 'delete'
  })
}

// 发布冥想
export function publishMeditation(id) {
  return request({
    url: `/system/meditation/publish/${id}`,
    method: 'put'
  })
}

// 下架冥想
export function unpublishMeditation(id) {
  return request({
    url: `/system/meditation/unpublish/${id}`,
    method: 'put'
  })
}

// 更新冥想评分信息
export function updateMeditationRating(id) {
  return request({
    url: `/system/meditation/updateRating/${id}`,
    method: 'put'
  })
}

// 根据分类ID查询冥想列表
export function getMeditationsByCategory(categoryId) {
  return request({
    url: `/system/meditation/category/${categoryId}`,
    method: 'get'
  })
}

// 获取冥想播放统计信息
export function getMeditationStatistics(id) {
  return request({
    url: `/system/meditation/statistics/${id}`,
    method: 'get'
  })
}
