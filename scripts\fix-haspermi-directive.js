/**
 * 修复 Element Plus 组件上的 v-hasPermi 指令问题
 * 将 v-hasPermi 指令替换为 v-if + checkPermi 函数
 */

const fs = require('fs');
const path = require('path');

// 需要修复的组件列表
const PROBLEMATIC_COMPONENTS = [
  'el-dropdown-item',
  'el-menu-item',
  'el-tab-pane',
  'el-step',
  'el-timeline-item',
  'el-carousel-item'
];

// 创建正则表达式来匹配需要修复的模式
function createFixPatterns() {
  const patterns = [];
  
  PROBLEMATIC_COMPONENTS.forEach(component => {
    // 匹配 <el-component ... v-hasPermi="[...]" ...>
    patterns.push({
      component,
      regex: new RegExp(
        `(<${component}[^>]*?)\\s+v-hasPermi="\\[([^\\]]+)\\]"([^>]*>)`,
        'g'
      )
    });
  });
  
  return patterns;
}

// 修复单个文件
function fixFile(filePath) {
  console.log(`正在处理文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  let needsImport = false;
  
  const patterns = createFixPatterns();
  
  patterns.forEach(({ component, regex }) => {
    const matches = [...content.matchAll(regex)];
    
    if (matches.length > 0) {
      console.log(`  发现 ${matches.length} 个 ${component} 需要修复`);
      hasChanges = true;
      needsImport = true;
      
      matches.forEach(match => {
        const [fullMatch, beforePermi, permissions, afterPermi] = match;
        
        // 构建新的标签
        const newTag = `${beforePermi} v-if="checkPermi([${permissions}])"${afterPermi}`;
        
        content = content.replace(fullMatch, newTag);
        console.log(`    修复: ${component} v-hasPermi="[${permissions}]"`);
      });
    }
  });
  
  // 如果需要导入 checkPermi 函数
  if (needsImport && !content.includes('import { checkPermi }')) {
    // 查找现有的 import 语句
    const importRegex = /import\s+{[^}]+}\s+from\s+["']@\/api\/[^"']+["'];?\s*$/gm;
    const lastImportMatch = [...content.matchAll(importRegex)].pop();
    
    if (lastImportMatch) {
      const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
      const importStatement = '\nimport { checkPermi } from "@/utils/permission";';
      
      content = content.slice(0, insertPosition) + importStatement + content.slice(insertPosition);
      console.log('  添加了 checkPermi 导入语句');
    } else {
      console.log('  警告: 无法找到合适的位置插入 import 语句，请手动添加');
    }
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ 文件已修复: ${filePath}`);
  } else {
    console.log(`  ⏭️  文件无需修复: ${filePath}`);
  }
  
  return hasChanges;
}

// 递归查找 .vue 文件
function findVueFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (item.endsWith('.vue')) {
        files.push(fullPath);
      }
    });
  }
  
  traverse(dir);
  return files;
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, '../src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('错误: 找不到 src 目录');
    process.exit(1);
  }
  
  console.log('开始修复 v-hasPermi 指令问题...\n');
  
  const vueFiles = findVueFiles(srcDir);
  console.log(`找到 ${vueFiles.length} 个 Vue 文件\n`);
  
  let fixedCount = 0;
  
  vueFiles.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n修复完成!`);
  console.log(`总共修复了 ${fixedCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('\n注意事项:');
    console.log('1. 请检查修复后的代码是否正确');
    console.log('2. 确保所有需要的 import 语句都已添加');
    console.log('3. 运行项目测试权限控制功能是否正常');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  fixFile,
  findVueFiles,
  createFixPatterns
};
