# 课程管理系统配置说明

## 概述
已为PC端后台管理系统创建了完整的课程管理系统功能，包括以下模块：

1. **课程管理** - 课程信息的增删改查、发布/下架、统计更新
2. **章节管理** - 课程章节的树形结构管理、排序
3. **讲师管理** - 讲师信息管理、头像上传
4. **订单管理** - 课程订单管理、支付状态更新、退款处理
5. **评价管理** - 课程评价管理、按课程和用户查询

## 已创建的文件

### 前端API文件
```
src/api/wechat/course.js        # 课程API
src/api/wechat/chapter.js       # 章节API
src/api/wechat/instructor.js    # 讲师API
src/api/wechat/order.js         # 订单API
src/api/wechat/review.js        # 评价API
```

### 前端页面文件（已重新组织到course文件夹下）
```
src/views/wechat/course/course/index.vue      # 课程管理
src/views/wechat/course/chapter/index.vue     # 章节管理
src/views/wechat/course/instructor/index.vue  # 讲师管理
src/views/wechat/course/order/index.vue       # 订单管理
src/views/wechat/course/review/index.vue      # 评价管理
```

## 需要在后端添加的菜单配置

由于项目使用动态路由，需要在后端数据库的菜单表中添加以下菜单配置：

### 1. 课程管理系统主菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('课程管理系统', 0, 7, 'course-system', NULL, 'M', '0', '0', NULL, 'education', 'admin', NOW(), '', NULL, '课程管理系统');
```

### 2. 课程管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('课程管理', [课程管理系统菜单ID], 1, 'course', 'wechat/course/course/index', 'C', '0', '0', 'system:course:list', 'skill', 'admin', NOW(), '', NULL, '课程信息管理');

-- 课程管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('课程查询', [课程管理菜单ID], 1, '', '', 'F', '0', '0', 'system:course:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('课程新增', [课程管理菜单ID], 2, '', '', 'F', '0', '0', 'system:course:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('课程修改', [课程管理菜单ID], 3, '', '', 'F', '0', '0', 'system:course:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('课程删除', [课程管理菜单ID], 4, '', '', 'F', '0', '0', 'system:course:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('课程导出', [课程管理菜单ID], 5, '', '', 'F', '0', '0', 'system:course:export', '#', 'admin', NOW(), '', NULL, '');
```

### 3. 章节管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('章节管理', [课程管理系统菜单ID], 2, 'chapter', 'wechat/course/chapter/index', 'C', '0', '0', 'system:chapter:list', 'tree-table', 'admin', NOW(), '', NULL, '课程章节管理');

-- 章节管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('章节查询', [章节管理菜单ID], 1, '', '', 'F', '0', '0', 'system:chapter:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('章节新增', [章节管理菜单ID], 2, '', '', 'F', '0', '0', 'system:chapter:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('章节修改', [章节管理菜单ID], 3, '', '', 'F', '0', '0', 'system:chapter:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('章节删除', [章节管理菜单ID], 4, '', '', 'F', '0', '0', 'system:chapter:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('章节导出', [章节管理菜单ID], 5, '', '', 'F', '0', '0', 'system:chapter:export', '#', 'admin', NOW(), '', NULL, '');
```

### 4. 讲师管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('讲师管理', [课程管理系统菜单ID], 3, 'instructor', 'wechat/course/instructor/index', 'C', '0', '0', 'system:instructor:list', 'peoples', 'admin', NOW(), '', NULL, '讲师信息管理');

-- 讲师管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('讲师查询', [讲师管理菜单ID], 1, '', '', 'F', '0', '0', 'system:instructor:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('讲师新增', [讲师管理菜单ID], 2, '', '', 'F', '0', '0', 'system:instructor:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('讲师修改', [讲师管理菜单ID], 3, '', '', 'F', '0', '0', 'system:instructor:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('讲师删除', [讲师管理菜单ID], 4, '', '', 'F', '0', '0', 'system:instructor:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('讲师导出', [讲师管理菜单ID], 5, '', '', 'F', '0', '0', 'system:instructor:export', '#', 'admin', NOW(), '', NULL, '');
```

### 5. 订单管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('订单管理', [课程管理系统菜单ID], 4, 'order', 'wechat/course/order/index', 'C', '0', '0', 'system:order:list', 'shopping', 'admin', NOW(), '', NULL, '课程订单管理');

-- 订单管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单查询', [订单管理菜单ID], 1, '', '', 'F', '0', '0', 'system:order:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单新增', [订单管理菜单ID], 2, '', '', 'F', '0', '0', 'system:order:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单修改', [订单管理菜单ID], 3, '', '', 'F', '0', '0', 'system:order:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单删除', [订单管理菜单ID], 4, '', '', 'F', '0', '0', 'system:order:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('订单导出', [订单管理菜单ID], 5, '', '', 'F', '0', '0', 'system:order:export', '#', 'admin', NOW(), '', NULL, '');
```

### 6. 评价管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('评价管理', [课程管理系统菜单ID], 5, 'review', 'wechat/course/review/index', 'C', '0', '0', 'system:review:list', 'star', 'admin', NOW(), '', NULL, '课程评价管理');

-- 评价管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价查询', [评价管理菜单ID], 1, '', '', 'F', '0', '0', 'system:review:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价新增', [评价管理菜单ID], 2, '', '', 'F', '0', '0', 'system:review:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价修改', [评价管理菜单ID], 3, '', '', 'F', '0', '0', 'system:review:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价删除', [评价管理菜单ID], 4, '', '', 'F', '0', '0', 'system:review:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('评价导出', [评价管理菜单ID], 5, '', '', 'F', '0', '0', 'system:review:export', '#', 'admin', NOW(), '', NULL, '');
```

## 菜单配置说明

### 菜单类型说明
- `M`: 目录（Directory）
- `C`: 菜单（Menu）
- `F`: 按钮（Button）

### 权限标识说明
- `system:course:list` - 课程列表查询权限
- `system:course:query` - 课程详情查询权限
- `system:course:add` - 课程新增权限
- `system:course:edit` - 课程修改权限
- `system:course:remove` - 课程删除权限
- `system:course:export` - 课程导出权限

其他模块的权限标识类似，将 `course` 替换为对应的模块名即可。

## 功能特性

### 课程管理
- ✅ 课程信息的增删改查
- ✅ 课程封面图片上传
- ✅ 富文本编辑器支持课程描述和详情
- ✅ 课程发布/下架功能
- ✅ 课程统计信息更新
- ✅ 课程评分更新
- ✅ 按分类和讲师筛选课程

### 章节管理
- ✅ 树形结构显示章节层级
- ✅ 章节排序功能
- ✅ 按课程筛选章节
- ✅ 富文本编辑器支持章节内容
- ✅ 视频地址配置

### 讲师管理
- ✅ 讲师头像上传
- ✅ 讲师详细信息管理
- ✅ 富文本编辑器支持个人简介
- ✅ 评分显示
- ✅ 工作经历和教育背景管理

### 订单管理
- ✅ 订单信息的增删改查
- ✅ 支付状态更新
- ✅ 退款处理
- ✅ 订单号自动生成
- ✅ 多种支付方式支持
- ✅ 订单详情查看

### 评价管理
- ✅ 评价信息的增删改查
- ✅ 星级评分显示
- ✅ 按课程和用户筛选评价
- ✅ 评价状态管理

## 使用说明

1. 首先在后端数据库中添加上述菜单配置
2. 重启后端服务
3. 使用管理员账号登录系统
4. 在侧边栏中找到"课程管理系统"菜单
5. 按照以下顺序进行配置：
   - 讲师管理：添加讲师信息
   - 课程管理：创建课程并关联讲师
   - 章节管理：为课程添加章节内容
   - 订单管理：处理课程订单
   - 评价管理：管理课程评价

## 注意事项

1. 确保后端Controller的路径与前端API调用路径一致
2. 权限标识需要与后端注解中的权限字符匹配
3. 菜单的component路径要与实际的Vue文件路径对应
4. 建议先在测试环境中验证所有功能正常后再部署到生产环境
5. 需要确保数据库中存在对应的字典类型数据（如课程状态、支付方式等）

## 后端Controller要求

需要确保后端已实现以下Controller中的所有接口：
- `PsyCourseController`
- `PsyCourseChapterController` 
- `PsyCourseInstructorController`
- `PsyCourseOrderController`
- `PsyCourseReviewController`

所有接口都应该按照提供的后端代码实现，确保前后端接口一致。
