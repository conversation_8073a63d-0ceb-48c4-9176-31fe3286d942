import request from '@/utils/request'

// 查询图片资源列表
export function listImageResource(query) {
  return request({
    url: '/wechat/imageResource/list',
    method: 'get',
    params: query
  })
}

// 获取图片资源详细信息
export function getImageResource(id) {
  return request({
    url: `/wechat/imageResource/${id}`,
    method: 'get'
  })
}

// 新增图片资源
export function addImageResource(data) {
  return request({
    url: '/wechat/imageResource',
    method: 'post',
    data: data
  })
}

// 修改图片资源
export function updateImageResource(data) {
  return request({
    url: '/wechat/imageResource',
    method: 'put',
    data: data
  })
}

// 删除图片资源
export function delImageResource(ids) {
  return request({
    url: `/wechat/imageResource/${ids}`,
    method: 'delete'
  })
}

// 查询图片与业务的关联关系
export function listImageRelation(query) {
  return request({
    url: '/wechat/imageResource/relation/list',
    method: 'get',
    params: query
  })
}

// 新增图片与业务的关联关系
export function addImageRelation(data) {
  return request({
    url: '/wechat/imageResource/relation',
    method: 'post',
    data: data
  })
}

// 删除图片与业务的关联关系
export function delImageRelation(data) {
  return request({
    url: '/wechat/imageResource/relation',
    method: 'delete',
    data: data
  })
}
