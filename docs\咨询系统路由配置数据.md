# 咨询系统路由配置数据

## 概述
以下是咨询系统的路由配置数据，需要在后端菜单管理中添加这些菜单项，系统会自动生成动态路由。

## 菜单层级结构

```
咨询系统 (一级菜单)
├── 咨询记录 (二级菜单)
├── 咨询师评价 (二级菜单)
└── 中断记录 (二级菜单)
```

## 详细配置数据

### 1. 一级菜单：咨询系统

```json
{
  "menuName": "咨询系统",
  "parentId": 0,
  "orderNum": 6,
  "path": "consultation",
  "component": "",
  "query": "",
  "isFrame": 1,
  "isCache": 0,
  "menuType": "M",
  "visible": "0",
  "status": "0",
  "icon": "message",
  "createBy": "admin",
  "remark": "咨询系统菜单"
}
```

### 2. 二级菜单：咨询记录

```json
{
  "menuName": "咨询记录",
  "parentId": "[咨询系统菜单ID]",
  "orderNum": 1,
  "path": "record",
  "component": "wechat/consultation/record/index",
  "query": "",
  "isFrame": 1,
  "isCache": 0,
  "menuType": "C",
  "visible": "0",
  "status": "0",
  "perms": "system:consultationRecord:list",
  "icon": "list",
  "createBy": "admin",
  "remark": "咨询记录菜单"
}
```

### 3. 二级菜单：咨询师评价

```json
{
  "menuName": "咨询师评价",
  "parentId": "[咨询系统菜单ID]",
  "orderNum": 2,
  "path": "review",
  "component": "wechat/consultation/review/index",
  "query": "",
  "isFrame": 1,
  "isCache": 0,
  "menuType": "C",
  "visible": "0",
  "status": "0",
  "perms": "system:consultantReview:list",
  "icon": "star",
  "createBy": "admin",
  "remark": "咨询师评价菜单"
}
```

### 4. 二级菜单：中断记录

```json
{
  "menuName": "中断记录",
  "parentId": "[咨询系统菜单ID]",
  "orderNum": 3,
  "path": "interruption",
  "component": "wechat/consultation/interruption/index",
  "query": "",
  "isFrame": 1,
  "isCache": 0,
  "menuType": "C",
  "visible": "0",
  "status": "0",
  "perms": "system:consultantInterruption:list",
  "icon": "warning",
  "createBy": "admin",
  "remark": "中断记录菜单"
}
```

## 权限配置数据

### 咨询记录权限

```json
[
  {
    "menuName": "咨询记录查询",
    "parentId": "[咨询记录菜单ID]",
    "orderNum": 1,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultationRecord:query",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询记录新增",
    "parentId": "[咨询记录菜单ID]",
    "orderNum": 2,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultationRecord:add",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询记录修改",
    "parentId": "[咨询记录菜单ID]",
    "orderNum": 3,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultationRecord:edit",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询记录删除",
    "parentId": "[咨询记录菜单ID]",
    "orderNum": 4,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultationRecord:remove",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询记录导出",
    "parentId": "[咨询记录菜单ID]",
    "orderNum": 5,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultationRecord:export",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  }
]
```

### 咨询师评价权限

```json
[
  {
    "menuName": "咨询师评价查询",
    "parentId": "[咨询师评价菜单ID]",
    "orderNum": 1,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantReview:query",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询师评价新增",
    "parentId": "[咨询师评价菜单ID]",
    "orderNum": 2,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantReview:add",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询师评价修改",
    "parentId": "[咨询师评价菜单ID]",
    "orderNum": 3,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantReview:edit",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询师评价删除",
    "parentId": "[咨询师评价菜单ID]",
    "orderNum": 4,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantReview:remove",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询师评价导出",
    "parentId": "[咨询师评价菜单ID]",
    "orderNum": 5,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantReview:export",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询师评价审核",
    "parentId": "[咨询师评价菜单ID]",
    "orderNum": 6,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantReview:audit",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "咨询师评价回复",
    "parentId": "[咨询师评价菜单ID]",
    "orderNum": 7,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantReview:reply",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  }
]
```

### 中断记录权限

```json
[
  {
    "menuName": "中断记录查询",
    "parentId": "[中断记录菜单ID]",
    "orderNum": 1,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantInterruption:query",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "中断记录新增",
    "parentId": "[中断记录菜单ID]",
    "orderNum": 2,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantInterruption:add",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "中断记录修改",
    "parentId": "[中断记录菜单ID]",
    "orderNum": 3,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantInterruption:edit",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "中断记录删除",
    "parentId": "[中断记录菜单ID]",
    "orderNum": 4,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantInterruption:remove",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  },
  {
    "menuName": "中断记录导出",
    "parentId": "[中断记录菜单ID]",
    "orderNum": 5,
    "path": "",
    "component": "",
    "query": "",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "F",
    "visible": "0",
    "status": "0",
    "perms": "system:consultantInterruption:export",
    "icon": "#",
    "createBy": "admin",
    "remark": ""
  }
]
```

## 字段说明

- **menuName**: 菜单名称
- **parentId**: 父菜单ID，0表示一级菜单
- **orderNum**: 显示顺序
- **path**: 路由地址
- **component**: 组件路径
- **query**: 路由参数
- **isFrame**: 是否为外链（0是 1否）
- **isCache**: 是否缓存（0缓存 1不缓存）
- **menuType**: 菜单类型（M目录 C菜单 F按钮）
- **visible**: 菜单状态（0显示 1隐藏）
- **status**: 菜单状态（0正常 1停用）
- **perms**: 权限标识
- **icon**: 菜单图标
- **createBy**: 创建者
- **remark**: 备注

## 添加步骤

1. 在后台管理系统的菜单管理中，按照上述配置添加菜单项
2. 为相应的角色分配菜单权限
3. 系统会自动生成动态路由，前端页面即可正常访问

## 注意事项

1. **组件路径**：component字段中的路径对应前端views目录下的组件路径
2. **权限标识**：perms字段要与前端页面中的v-hasPermi指令保持一致
3. **菜单图标**：icon字段使用Element Plus图标名称
4. **父子关系**：添加子菜单时，parentId要设置为父菜单的实际ID
