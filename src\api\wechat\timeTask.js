import request from '@/utils/request'

// 获取定时任务状态
export function getTaskStatus() {
  return request({
    url: '/system/timeTask/status',
    method: 'get'
  })
}

// 手动触发生成时间槽
export function manualGenerate(days = 7) {
  return request({
    url: '/system/timeTask/generate',
    method: 'post',
    params: {
      days: days
    }
  })
}

// 手动触发清理过期时间槽
export function manualClean(days = 7) {
  return request({
    url: '/system/timeTask/clean',
    method: 'post',
    params: {
      days: days
    }
  })
}

// 手动触发更新过期时间槽状态
export function manualUpdateExpired() {
  return request({
    url: '/system/timeTask/updateExpired',
    method: 'post'
  })
}
