# 预约排班系统PC端后台管理配置说明

## 更新说明 (2025-07-09)
根据后端返回的实际数据结构，已更新时间段管理页面：

### 时间段数据结构变更
- 使用 `startHour` 和 `endHour` (整数) 替代 `startTime` 和 `endTime`
- 使用 `delFlag` (0=正常, 1=已删除) 替代 `status` 字段
- 添加 `iconUrl` 字段支持图标显示
- 移除 `duration` 和 `sort` 字段，时长通过 `endHour - startHour` 计算

### 实际数据格式示例
```json
{
  "id": 20,
  "name": "上午",
  "iconUrl": "https://example.com/icons/morning.png",
  "startHour": 9,
  "endHour": 12,
  "delFlag": 0,
  "createTime": "2025-07-09 10:54:02",
  "remark": ""
}
```

## 概述
已为PC端后台管理系统创建了完整的预约系统和排班系统管理功能，包括以下模块：

1. **仪表板** - 预约排班系统概览
2. **时间段管理** - 定义可预约的时间段
3. **时间槽管理** - 管理具体的时间槽
4. **排班管理** - 综合排班视图（表格+日历）
5. **预约管理** - 查看和管理所有预约
6. **定时任务管理** - 管理时间槽相关的定时任务

## 已创建的文件

### 前端页面文件
```
src/views/wechat/dashboard/index.vue        # 仪表板
src/views/wechat/timeRange/index.vue        # 时间段管理
src/views/wechat/timeSlot/index.vue         # 时间槽管理（集成三种视图）
src/views/wechat/systemTimeSlot/index.vue   # 系统时间槽管理（集成三种视图）
src/views/wechat/schedule/index.vue         # 排班管理
src/views/wechat/appointment/index.vue      # 预约管理
src/views/wechat/timeTask/index.vue         # 定时任务管理
```

### 时间槽管理 - 三合一视图

针对您提到的四千多条时间槽数据展示问题，我在 `timeSlot/index.vue` 中集成了三种展示方案，通过顶部按钮切换：

#### 🔄 视图切换
- **列表按钮**：传统表格视图
- **网格按钮**：日历网格视图（推荐）
- **统计按钮**：数据分析视图

#### 📋 列表视图
- **传统表格**：支持分页、筛选、排序
- **批量操作**：支持批量修改状态
- **详细信息**：显示完整的时间槽信息
- **适用场景**：详细数据管理，批量操作

#### 🗓️ 网格视图（推荐）
- **日期标签页**：按日期分组展示
- **时间网格**：横轴为咨询师，纵轴为时间段
- **状态色彩**：不同颜色表示不同状态
- **交互式**：点击查看详情，悬停显示提示
- **适用场景**：日常排班管理，直观查看某天所有咨询师的时间安排

#### 📊 统计视图
- **统计卡片**：总数、可用、已预约、不可用数量
- **多维度图表**：按日期、咨询师、状态、时间段统计
- **热力图**：咨询师×时间分布热力图
- **详细表格**：每个咨询师的利用率统计
- **适用场景**：数据分析，工作量统计，决策支持

### 系统时间槽管理 - 三合一视图

系统时间槽是预设的时间模板，用于快速生成咨询师的个人时间槽。同样集成了三种展示方案：

#### 🔄 视图切换
- **列表按钮**：传统表格视图
- **网格按钮**：日历网格视图（推荐）
- **统计按钮**：数据分析视图

#### 📋 列表视图
- **传统表格**：支持分页、筛选、排序
- **批量操作**：支持批量修改状态
- **详细信息**：显示完整的系统时间槽信息
- **适用场景**：详细数据管理，批量操作

#### 🗓️ 网格视图（推荐）
- **日期选择器**：选择具体日期查看该日的系统时间槽
- **简化网格**：显示时间段、可用咨询师数、总咨询师数、状态
- **状态色彩**：不同颜色表示不同状态
- **适用场景**：日常系统时间槽管理，快速查看可用性

#### 📊 统计视图
- **统计卡片**：总数、可用、已预约、不可用数量
- **多维度图表**：
  - 按日期统计柱状图
  - 状态分布环形图
  - 可用性趋势线图
  - 时间段分布饼图
- **详细统计表格**：每日的利用率统计
- **适用场景**：系统时间槽分析，容量规划

#### 🛠️ 特殊功能
- **生成系统时间槽**：批量生成指定日期范围的系统时间槽
- **重新生成**：重新生成已存在的系统时间槽
- **清理过期**：清理指定日期之前的过期系统时间槽
- **更新统计**：更新可用性统计数据

### API文件
```
src/api/wechat/dashboard.js      # 仪表板API
src/api/wechat/timeRange.js      # 时间段API
src/api/wechat/timeSlot.js       # 时间槽API
src/api/wechat/appointment.js    # 预约API
src/api/wechat/timeTask.js       # 定时任务API
```

## 需要在后端添加的菜单配置

由于项目使用动态路由，需要在后端数据库的菜单表中添加以下菜单配置：

### 1. 预约排班系统主菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('预约排班系统', 0, 6, 'appointment-system', NULL, 'M', '0', '0', NULL, 'calendar', 'admin', NOW(), '', NULL, '预约排班系统管理');
```

### 2. 仪表板菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('仪表板', [预约排班系统菜单ID], 1, 'dashboard', 'wechat/dashboard/index', 'C', '0', '0', 'system:dashboard:view', 'dashboard', 'admin', NOW(), '', NULL, '预约排班仪表板');
```

### 3. 时间段管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间段管理', [预约排班系统菜单ID], 2, 'timeRange', 'wechat/timeRange/index', 'C', '0', '0', 'system:timeRange:list', 'time', 'admin', NOW(), '', NULL, '时间段定义管理');

-- 时间段管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间段查询', [时间段管理菜单ID], 1, '', '', 'F', '0', '0', 'system:timeRange:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间段新增', [时间段管理菜单ID], 2, '', '', 'F', '0', '0', 'system:timeRange:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间段修改', [时间段管理菜单ID], 3, '', '', 'F', '0', '0', 'system:timeRange:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间段删除', [时间段管理菜单ID], 4, '', '', 'F', '0', '0', 'system:timeRange:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间段导出', [时间段管理菜单ID], 5, '', '', 'F', '0', '0', 'system:timeRange:export', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间段初始化', [时间段管理菜单ID], 6, '', '', 'F', '0', '0', 'system:timeRange:init', '#', 'admin', NOW(), '', NULL, '');
```

### 4. 时间槽管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('时间槽管理', [预约排班系统菜单ID], 3, 'timeSlot', 'wechat/timeSlot/index', 'C', '0', '0', 'system:timeSlot:list', 'time-range', 'admin', NOW(), '', NULL, '时间槽管理（集成三种视图）');
```

### 5. 系统时间槽管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('系统时间槽管理', [预约排班系统菜单ID], 4, 'systemTimeSlot', 'wechat/systemTimeSlot/index', 'C', '0', '0', 'system:systemTimeSlot:list', 'clock', 'admin', NOW(), '', NULL, '系统时间槽管理（集成三种视图）');

-- 时间槽管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间槽查询', [时间槽管理菜单ID], 1, '', '', 'F', '0', '0', 'system:timeSlot:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间槽新增', [时间槽管理菜单ID], 2, '', '', 'F', '0', '0', 'system:timeSlot:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间槽修改', [时间槽管理菜单ID], 3, '', '', 'F', '0', '0', 'system:timeSlot:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间槽删除', [时间槽管理菜单ID], 4, '', '', 'F', '0', '0', 'system:timeSlot:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间槽生成', [时间槽管理菜单ID], 5, '', '', 'F', '0', '0', 'system:timeSlot:generate', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间槽批量生成', [时间槽管理菜单ID], 6, '', '', 'F', '0', '0', 'system:timeSlot:generateAll', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('时间槽清理', [时间槽管理菜单ID], 7, '', '', 'F', '0', '0', 'system:timeSlot:clean', '#', 'admin', NOW(), '', NULL, '');
```

### 6. 排班管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('排班管理', [预约排班系统菜单ID], 5, 'schedule', 'wechat/schedule/index', 'C', '0', '0', 'system:schedule:view', 'peoples', 'admin', NOW(), '', NULL, '排班管理');
```

### 7. 预约管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('预约管理', [预约排班系统菜单ID], 6, 'appointment', 'wechat/appointment/index', 'C', '0', '0', 'system:appointment:list', 'form', 'admin', NOW(), '', NULL, '预约管理');

-- 预约管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('预约查询', [预约管理菜单ID], 1, '', '', 'F', '0', '0', 'system:appointment:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('预约确认', [预约管理菜单ID], 2, '', '', 'F', '0', '0', 'system:appointment:confirm', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('预约取消', [预约管理菜单ID], 3, '', '', 'F', '0', '0', 'system:appointment:cancel', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('预约修改', [预约管理菜单ID], 4, '', '', 'F', '0', '0', 'system:appointment:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('预约导出', [预约管理菜单ID], 5, '', '', 'F', '0', '0', 'system:appointment:export', '#', 'admin', NOW(), '', NULL, '');
```

### 8. 定时任务管理菜单
```sql
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('定时任务管理', [预约排班系统菜单ID], 7, 'timeTask', 'wechat/timeTask/index', 'C', '0', '0', 'system:timeTask:query', 'job', 'admin', NOW(), '', NULL, '定时任务管理');

-- 定时任务管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('任务生成', [定时任务管理菜单ID], 1, '', '', 'F', '0', '0', 'system:timeTask:generate', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('任务清理', [定时任务管理菜单ID], 2, '', '', 'F', '0', '0', 'system:timeTask:clean', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('任务更新', [定时任务管理菜单ID], 3, '', '', 'F', '0', '0', 'system:timeTask:update', '#', 'admin', NOW(), '', NULL, '');
```

## 功能特性

### 1. 仪表板
- 今日预约统计
- 待确认预约数量
- 可用时间槽统计
- 在线咨询师数量
- 今日排班和预约列表
- 预约趋势图表
- 咨询师工作量统计
- 快捷操作入口

### 2. 时间段管理
- 时间段的增删改查
- 时间段状态管理
- 初始化默认时间段
- 时间段导出功能

### 3. 时间槽管理
- 时间槽的增删改查
- 批量生成时间槽
- 清理过期时间槽
- 时间槽状态管理
- 公开/私有设置

### 4. 排班管理
- 表格视图和日历视图切换
- 按咨询师、日期、状态筛选
- 批量生成排班
- 批量修改状态
- 时间槽详情查看

### 5. 预约管理
- 预约列表查看
- 预约状态管理（确认/取消）
- 批量操作
- 预约详情查看
- 预约数据导出

### 6. 定时任务管理
- 任务状态监控
- 手动触发任务
- 任务执行日志
- 参数配置

## 后端接口要求

需要确保后端已实现以下Controller中的所有接口：
- `PsyTimeRangeController`
- `PsyTimeSlotController` 
- `PsyTimeTaskController`
- 预约管理相关接口
- 仪表板统计接口

## 安装依赖

确保项目已安装以下依赖：
```bash
npm install echarts  # 用于图表显示
```

## 使用说明

1. 首先在后端数据库中添加上述菜单配置
2. 重启后端服务
3. 使用管理员账号登录系统
4. 在侧边栏中找到"预约排班系统"菜单
5. 按照以下顺序进行配置：
   - 时间段管理：初始化默认时间段
   - 时间槽管理：为咨询师生成时间槽
   - 排班管理：查看和调整排班
   - 预约管理：处理用户预约

## 注意事项

1. 确保后端Controller的路径与前端API调用路径一致
2. 权限标识需要与后端注解中的权限字符匹配
3. 菜单的component路径要与实际的Vue文件路径对应
4. 建议先在测试环境中验证所有功能正常后再部署到生产环境
