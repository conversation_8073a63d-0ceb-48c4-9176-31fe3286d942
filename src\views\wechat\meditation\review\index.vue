<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="冥想名称" prop="meditationName">
        <el-input v-model="queryParams.meditationName" placeholder="请输入冥想名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="评分" prop="rating">
        <el-select v-model="queryParams.rating" placeholder="请选择评分" clearable style="width: 240px">
          <el-option label="1星" value="1" />
          <el-option label="2星" value="2" />
          <el-option label="3星" value="3" />
          <el-option label="4星" value="4" />
          <el-option label="5星" value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:meditationReview:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:meditationReview:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:meditationReview:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:meditationReview:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reviewList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="评价ID" align="center" prop="id" width="80" />
      <el-table-column label="冥想名称" align="center" prop="meditationName" :show-overflow-tooltip="true" />
      <el-table-column label="用户名称" align="center" prop="userName" />
      <el-table-column label="评分" align="center" prop="rating" width="120">
        <template #default="scope">
          <el-rate v-model="scope.row.rating" disabled show-score text-color="#ff9900" />
        </template>
      </el-table-column>
      <el-table-column label="评价内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="评价时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:meditationReview:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:meditationReview:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:meditationReview:remove']">删除</el-button>
          <el-button link type="primary" icon="DataAnalysis" @click="handleStatistics(scope.row)"
            v-hasPermi="['system:meditationReview:list']">统计</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改评价对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="reviewRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="冥想" prop="meditationId">
              <el-select v-model="form.meditationId" placeholder="请选择冥想" style="width: 100%">
                <el-option v-for="meditation in meditationList" :key="meditation.id" :label="meditation.title"
                  :value="meditation.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评分" prop="rating">
              <el-rate v-model="form.rating" show-score text-color="#ff9900" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="评价内容" prop="content">
              <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入评价内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 评价详情对话框 -->
    <el-dialog title="评价详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="冥想名称">{{ detailForm.meditationName }}</el-descriptions-item>
        <el-descriptions-item label="用户名称">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="评分" :span="2">
          <el-rate v-model="detailForm.rating" disabled show-score text-color="#ff9900" />
        </el-descriptions-item>
        <el-descriptions-item label="评价内容" :span="2">{{ detailForm.content }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="sys_normal_disable" :value="detailForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="评价时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 评价统计对话框 -->
    <el-dialog title="冥想评价统计" v-model="statisticsOpen" width="800px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span>总体统计</span>
              </div>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="平均评分">
                <el-rate v-model="statisticsData.avgRating" disabled show-score text-color="#ff9900" />
              </el-descriptions-item>
              <el-descriptions-item label="总评价数">{{ statisticsData.totalCount }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span>星级分布</span>
              </div>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="5星">{{ statisticsData.rating5Count }}</el-descriptions-item>
              <el-descriptions-item label="4星">{{ statisticsData.rating4Count }}</el-descriptions-item>
              <el-descriptions-item label="3星">{{ statisticsData.rating3Count }}</el-descriptions-item>
              <el-descriptions-item label="2星">{{ statisticsData.rating2Count }}</el-descriptions-item>
              <el-descriptions-item label="1星">{{ statisticsData.rating1Count }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script setup name="MeditationReview">
import { listMeditationReview, getMeditationReview, delMeditationReview, addMeditationReview, updateMeditationReview, exportMeditationReview, getMeditationReviewStatistics } from "@/api/wechat/meditation/review";
import { listMeditation } from "@/api/wechat/meditation/meditation";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

const reviewList = ref([]);
const meditationList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const statisticsOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  statisticsData: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    meditationName: null,
    userName: null,
    rating: null,
    status: null
  },
  rules: {
    meditationId: [
      { required: true, message: "冥想不能为空", trigger: "change" }
    ],
    rating: [
      { required: true, message: "评分不能为空", trigger: "change" }
    ],
    content: [
      { required: true, message: "评价内容不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, statisticsData, rules } = toRefs(data);

/** 查询评价列表 */
function getList() {
  loading.value = true;
  listMeditationReview(queryParams.value).then(response => {
    reviewList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询冥想列表 */
function getMeditationList() {
  listMeditation().then(response => {
    meditationList.value = response.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    meditationId: null,
    userId: null,
    rating: 5,
    content: null,
    status: "0",
    remark: null
  };
  proxy.resetForm("reviewRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评价";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getMeditationReview(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改评价";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getMeditationReview(_id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 统计按钮操作 */
function handleStatistics(row) {
  getMeditationReviewStatistics(row.meditationId).then(response => {
    statisticsData.value = response.data;
    statisticsOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["reviewRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateMeditationReview(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMeditationReview(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除评价编号为"' + _ids + '"的数据项？').then(function () {
    return delMeditationReview(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/meditationReview/export', {
    ...queryParams.value
  }, `meditation_review_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  getMeditationList();
});
</script>
