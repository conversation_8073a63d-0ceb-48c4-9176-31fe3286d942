# Element Plus el-radio 属性修正说明

## 概述
根据 Element Plus 的更新，`el-radio` 组件的 `label` 属性即将弃用，应该使用 `value` 属性替代。已对项目中所有使用 `el-radio` 的地方进行了修正。

## 修正原因
- Element Plus 官方宣布 `el-radio` 的 `label` 属性即将弃用
- 新版本推荐使用 `value` 属性来设置单选框的值
- 提前修正可以避免未来版本升级时的兼容性问题

## 修正内容

### 1. 课程管理页面 (src/views/wechat/course/course/index.vue)
```vue
<!-- 修正前 -->
<el-radio :label="0">付费</el-radio>
<el-radio :label="1">免费</el-radio>

<!-- 修正后 -->
<el-radio :value="0">付费</el-radio>
<el-radio :value="1">免费</el-radio>
```

### 2. 章节管理页面 (src/views/wechat/course/chapter/index.vue)
```vue
<!-- 修正前 -->
<el-radio :label="0">否</el-radio>
<el-radio :label="1">是</el-radio>

<!-- 修正后 -->
<el-radio :value="0">否</el-radio>
<el-radio :value="1">是</el-radio>
```

### 3. 讲师管理页面 (src/views/wechat/course/instructor/index.vue)
```vue
<!-- 修正前 -->
<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">
  {{ dict.label }}
</el-radio>

<!-- 修正后 -->
<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">
  {{ dict.label }}
</el-radio>
```

### 4. 课程评价管理页面 (src/views/wechat/course/review/index.vue)
```vue
<!-- 修正前 -->
<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">
  {{ dict.label }}
</el-radio>

<!-- 修正后 -->
<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">
  {{ dict.label }}
</el-radio>
```

### 5. 冥想评价管理页面 (src/views/wechat/meditation/review/index.vue)
```vue
<!-- 修正前 -->
<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">
  {{ dict.label }}
</el-radio>

<!-- 修正后 -->
<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">
  {{ dict.label }}
</el-radio>
```

## 修正统计

### 已修正的文件
1. ✅ `src/views/wechat/course/course/index.vue` - 2处修正
2. ✅ `src/views/wechat/course/chapter/index.vue` - 2处修正  
3. ✅ `src/views/wechat/course/instructor/index.vue` - 1处修正
4. ✅ `src/views/wechat/course/review/index.vue` - 1处修正
5. ✅ `src/views/wechat/meditation/review/index.vue` - 1处修正

### 无需修正的文件
- `src/views/wechat/course/order/index.vue` - 未使用 `el-radio` 的 `:label` 属性
- `src/views/wechat/meditation/meditation/index.vue` - 未使用 `el-radio` 的 `:label` 属性
- `src/views/wechat/meditation/order/index.vue` - 未使用 `el-radio` 的 `:label` 属性

## 修正类型

### 1. 字典数据循环类型
```vue
<!-- 适用于状态选择等使用字典数据的场景 -->
<el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">
  {{ dict.label }}
</el-radio>
```

### 2. 固定值类型
```vue
<!-- 适用于是否选择、付费类型等固定选项的场景 -->
<el-radio :value="0">否</el-radio>
<el-radio :value="1">是</el-radio>
```

## 功能验证

### 验证要点
1. **数据绑定正常**：`v-model` 绑定的值能正确更新
2. **选中状态正确**：单选框的选中状态显示正常
3. **表单提交正常**：表单提交时能获取到正确的值
4. **默认值设置**：表单重置时能正确设置默认选中项

### 测试场景
- ✅ 新增表单：默认值设置正确
- ✅ 编辑表单：回显值显示正确
- ✅ 表单提交：提交的数据值正确
- ✅ 表单重置：重置后的默认值正确

## 注意事项

### 1. 数据类型一致性
确保 `:value` 属性的值类型与 `v-model` 绑定的数据类型一致：
```vue
<!-- 数字类型 -->
<el-radio :value="0">选项1</el-radio>
<el-radio :value="1">选项2</el-radio>

<!-- 字符串类型 -->
<el-radio :value="'0'">选项1</el-radio>
<el-radio :value="'1'">选项2</el-radio>
```

### 2. 字典数据处理
使用字典数据时，确保字典的 `value` 字段类型正确：
```javascript
// 字典数据示例
const sys_normal_disable = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];
```

### 3. 表单验证
修正后的 `el-radio` 组件不影响表单验证规则：
```javascript
rules: {
  status: [
    { required: true, message: "状态不能为空", trigger: "change" }
  ]
}
```

## 兼容性说明

### Element Plus 版本
- **当前版本**：支持 `label` 和 `value` 两种属性
- **未来版本**：将移除 `label` 属性支持
- **建议**：统一使用 `value` 属性，确保向前兼容

### 升级建议
1. **立即修正**：将所有 `:label` 改为 `:value`
2. **测试验证**：确保修正后功能正常
3. **代码规范**：在代码规范中明确使用 `value` 属性
4. **团队培训**：告知团队成员这一变更

## 总结

通过本次修正：
- ✅ 修正了 7 处 `el-radio` 的 `label` 属性使用
- ✅ 涵盖了课程管理系统和冥想系统的所有相关页面
- ✅ 确保了代码的向前兼容性
- ✅ 避免了未来 Element Plus 升级时的兼容性问题

所有修正都已完成并验证功能正常，项目现在完全符合 Element Plus 的最新规范要求。
