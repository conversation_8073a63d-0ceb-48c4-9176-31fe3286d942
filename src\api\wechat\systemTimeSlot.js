import request from '@/utils/request'

// 查询系统时间槽列表
export function listSystemTimeSlot(query) {
  return request({
    url: '/system/systemTimeSlot/list',
    method: 'get',
    params: query
  })
}

// 查询系统时间槽详细
export function getSystemTimeSlot(id) {
  return request({
    url: '/system/systemTimeSlot/' + id,
    method: 'get'
  })
}

// 新增系统时间槽
export function addSystemTimeSlot(data) {
  return request({
    url: '/system/systemTimeSlot',
    method: 'post',
    data: data
  })
}

// 修改系统时间槽
export function updateSystemTimeSlot(data) {
  return request({
    url: '/system/systemTimeSlot',
    method: 'put',
    data: data
  })
}

// 删除系统时间槽
export function delSystemTimeSlot(ids) {
  return request({
    url: '/system/systemTimeSlot/' + ids,
    method: 'delete'
  })
}

// 查询指定日期范围的系统时间槽
export function getSlotsByDateRange(startDate, endDate, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/dateRange',
    method: 'get',
    params: {
      startDate,
      endDate,
      centerId
    }
  })
}

// 查询指定日期的系统时间槽
export function getSlotsByDate(date, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/date/' + date,
    method: 'get',
    params: {
      centerId
    }
  })
}

// 查询有可用咨询师的时间槽
export function getAvailableSlots(startDate, endDate, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/available',
    method: 'get',
    params: {
      startDate,
      endDate,
      centerId
    }
  })
}

// 获取格式化的系统时间槽数据
export function getFormattedTimeSlots(startDate, endDate, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/formatted',
    method: 'get',
    params: {
      startDate,
      endDate,
      centerId
    }
  })
}

// 生成系统时间槽
export function generateSystemTimeSlots(startDate, endDate, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/generate',
    method: 'post',
    params: {
      startDate,
      endDate,
      centerId
    }
  })
}

// 重新生成系统时间槽
export function regenerateSystemTimeSlots(startDate, endDate, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/regenerate',
    method: 'post',
    params: {
      startDate,
      endDate,
      centerId
    }
  })
}

// 更新可用性统计
export function updateAvailabilityStats(date, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/updateStats',
    method: 'put',
    params: {
      date,
      centerId
    }
  })
}

// 清理过期的系统时间槽
export function cleanExpiredSlots(beforeDate, centerId = 1) {
  return request({
    url: '/system/systemTimeSlot/cleanExpired',
    method: 'delete',
    params: {
      beforeDate,
      centerId
    }
  })
}
