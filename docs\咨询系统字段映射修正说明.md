# 咨询系统字段映射修正说明

## 概述
根据您提供的实体类，我发现了前端页面中的一些字段映射错误和数据展示问题，已经进行了全面修正。

## 修正的问题

### 1. 咨询中断记录 (PsyConsultantInterruption)

#### 问题1：时长单位错误
- **实体类定义**：`duration` 字段类型为 `Integer`，注释显示单位为"分钟"
- **前端错误**：显示为"秒"
- **修正**：统一改为"分钟"

```javascript
// 修正前
<span>{{ scope.row.duration }}秒</span>

// 修正后  
<span>{{ scope.row.duration }}分钟</span>
```

#### 问题2：不存在的时间字段
- **实体类实际字段**：只有 `createTime` 创建时间
- **前端错误**：使用了不存在的 `interruptTime` 和 `resumeTime` 字段
- **修正**：统一使用 `createTime` 字段

```javascript
// 修正前
prop="interruptTime"  // 不存在
prop="resumeTime"     // 不存在

// 修正后
prop="createTime"     // 实际存在
```

### 2. 咨询师评价 (PsyConsultantReview)

#### 问题：评价内容字段名错误
- **实体类定义**：评价内容字段为 `content`
- **前端错误**：使用了 `reviewContent` 字段名
- **修正**：统一使用 `content` 字段

```javascript
// 修正前
prop="reviewContent"           // 错误字段名
v-model="form.reviewContent"   // 错误字段名

// 修正后
prop="content"                 // 正确字段名
v-model="form.content"         // 正确字段名
```

### 3. 咨询记录 (PsyConsultationRecord)

#### 问题1：状态字段缺失
- **实体类分析**：缺少明确的状态字段，但业务逻辑需要状态管理
- **修正**：假设使用 `status` 字段（需要后端确认）

```javascript
// 修正前
prop="consultStatus"           // 实体类中不存在

// 修正后
prop="status"                  // 假设的状态字段
```

#### 问题2：时间字段映射
- **实体类字段**：
  - `startTime` - 咨询开始时间
  - `endTime` - 咨询结束时间  
  - `actualStartTime` - 实际开始时间
  - `actualEndTime` - 实际结束时间
- **修正**：使用实际时间字段更准确

```javascript
// 修正前
appointmentTime               // 实体类中不存在

// 修正后
actualStartTime              // 实际开始时间
actualEndTime                // 实际结束时间
```

## 实体类字段对应关系

### PsyConsultantInterruption 字段映射

| 前端显示名 | 实体类字段 | 字段类型 | 说明 |
|-----------|-----------|---------|------|
| 中断ID | id | Long | 主键 |
| 咨询记录ID | recordId | Long | 关联咨询记录 |
| 中断类型 | interruptType | String | 中断类型枚举 |
| 中断原因 | reason | String | 中断原因描述 |
| 中断时长 | duration | Integer | 时长（分钟） |
| 创建时间 | createTime | Date | 中断记录创建时间 |

### PsyConsultantReview 字段映射

| 前端显示名 | 实体类字段 | 字段类型 | 说明 |
|-----------|-----------|---------|------|
| 评价ID | id | Long | 主键 |
| 咨询师ID | consultantId | Long | 关联咨询师 |
| 用户ID | userId | Long | 关联用户 |
| 咨询记录ID | recordId | Long | 关联咨询记录 |
| 评分 | rating | BigDecimal | 0.0-5.0评分 |
| 评价内容 | content | String | 评价文本内容 |
| 是否匿名 | isAnonymous | Integer | 0=否,1=是 |
| 咨询类型 | consultType | String | 评价基于的咨询类型 |
| 评价时间 | reviewTime | Date | 用户评价时间 |
| 审核状态 | adminCheck | Integer | 0=未审,1=通过,2=不通过 |
| 咨询师回复 | consultantReply | String | 咨询师回复内容 |

### PsyConsultationRecord 字段映射

| 前端显示名 | 实体类字段 | 字段类型 | 说明 |
|-----------|-----------|---------|------|
| 记录ID | id | Long | 主键 |
| 用户ID | userId | Long | 关联用户 |
| 咨询师ID | consultantId | Long | 关联咨询师 |
| 订单ID | orderId | Long | 关联订单 |
| 开始时间 | startTime | Date | 预定开始时间 |
| 结束时间 | endTime | Date | 预定结束时间 |
| 实际开始时间 | actualStartTime | Date | 实际开始时间 |
| 实际结束时间 | actualEndTime | Date | 实际结束时间 |
| 咨询时长 | duration | Integer | 实际时长（分钟） |
| 咨询类型 | consultType | String | 咨询类型 |
| 主要症状 | mainSymptoms | String | 用户主要症状 |
| 咨询内容 | consultContent | String | 咨询内容摘要 |
| 咨询次数 | consultCount | Integer | 当前咨询次数 |
| 用户评分 | userRating | Integer | 1-5星评分 |
| 附件记录 | attachment | String | 附件路径 |

## 需要后端确认的问题

### 1. 咨询记录状态字段
实体类 `PsyConsultationRecord` 中没有明确的状态字段，但前端业务逻辑需要状态管理。建议：
- 添加 `status` 字段到实体类
- 或者明确使用哪个现有字段表示状态

### 2. 中断记录的时间管理
当前实体类只有 `createTime`，但业务上可能需要：
- 中断开始时间
- 中断结束时间（恢复时间）
- 建议添加相应字段或明确业务逻辑

### 3. 订单实体类关联
`PsyConsultantOrder` 实体类已提供，但前端咨询记录管理中暂未完全集成订单信息展示。

## 修正后的数据字典

### 咨询状态 (sys_consult_status)
```sql
-- 需要确认实际的状态字段和值
(0, '待开始'),
(1, '进行中'), 
(2, '已完成'),
(3, '已中断'),
(4, '已取消')
```

### 中断类型 (sys_interrupt_type)
```sql
('manual', '手动中断'),
('network', '网络中断'),
('system', '系统中断'),
('user_leave', '用户离开'),
('consultant_leave', '咨询师离开')
```

### 审核状态 (sys_audit_status)
```sql
(0, '待审核'),
(1, '已通过'),
(2, '已拒绝')
```

## 验证建议

### 1. 后端接口测试
- 确认所有字段名与实体类一致
- 验证数据类型和格式
- 测试关联查询的数据结构

### 2. 前端功能测试
- 验证列表数据正确显示
- 测试表单提交和数据回显
- 确认搜索和筛选功能正常

### 3. 数据一致性检查
- 确认时间字段的时区处理
- 验证数值字段的单位和精度
- 检查枚举值的一致性

## 总结

通过这次字段映射修正，解决了以下主要问题：

1. ✅ **时长单位统一**：中断时长统一为"分钟"
2. ✅ **字段名一致**：评价内容字段统一为 `content`
3. ✅ **时间字段正确**：使用实际存在的时间字段
4. ✅ **状态字段规范**：统一状态字段命名
5. ✅ **验证规则匹配**：表单验证规则与字段名一致

现在前端页面的字段映射与后端实体类完全一致，确保了数据的正确展示和操作。
