<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="关键词" prop="keyword">
            <el-input
               v-model="queryParams.keyword"
               placeholder="请输入关键词"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="搜索类型" prop="searchType">
            <el-select
               v-model="queryParams.searchType"
               placeholder="请选择搜索类型"
               clearable
               style="width: 240px"
            >
               <el-option label="全部" value="all" />
               <el-option label="咨询师" value="consultant" />
               <el-option label="课程" value="course" />
               <el-option label="冥想" value="meditation" />
               <el-option label="测评" value="assessment" />
            </el-select>
         </el-form-item>
         <el-form-item label="状态" prop="status">
            <el-select
               v-model="queryParams.status"
               placeholder="热门搜索状态"
               clearable
               style="width: 240px"
            >
               <el-option
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['search:hot:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['search:hot:edit']"
            >修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['search:hot:remove']"
            >删除</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['search:hot:export']"
            >导出</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="hotList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="ID" align="center" prop="id" width="80" />
         <el-table-column label="关键词" align="center" prop="keyword" :show-overflow-tooltip="true" />
         <el-table-column label="搜索类型" align="center" prop="searchType" width="100">
            <template #default="scope">
               <dict-tag :options="search_type_options" :value="scope.row.searchType" />
            </template>
         </el-table-column>
         <el-table-column label="搜索次数" align="center" prop="searchCount" width="100" />
         <el-table-column label="热度分数" align="center" prop="hotScore" width="100">
            <template #default="scope">
               <span>{{ scope.row.hotScore?.toFixed(2) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="最后搜索时间" align="center" prop="lastSearchTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.lastSearchTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
         </el-table-column>
         <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['search:hot:edit']">修改</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['search:hot:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>
      
      <pagination
         v-show="total>0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改热门搜索对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="hotRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="关键词" prop="keyword">
               <el-input v-model="form.keyword" placeholder="请输入关键词" />
            </el-form-item>
            <el-form-item label="搜索类型" prop="searchType">
               <el-select v-model="form.searchType" placeholder="请选择搜索类型">
                  <el-option label="全部" value="all" />
                  <el-option label="咨询师" value="consultant" />
                  <el-option label="课程" value="course" />
                  <el-option label="冥想" value="meditation" />
                  <el-option label="测评" value="assessment" />
               </el-select>
            </el-form-item>
            <el-form-item label="搜索次数" prop="searchCount">
               <el-input-number v-model="form.searchCount" :min="0" />
            </el-form-item>
            <el-form-item label="热度分数" prop="hotScore">
               <el-input-number v-model="form.hotScore" :min="0" :precision="2" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
               <el-radio-group v-model="form.status">
                  <el-radio
                     v-for="dict in sys_normal_disable"
                     :key="dict.value"
                     :value="dict.value"
                  >{{dict.label}}</el-radio>
               </el-radio-group>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="HotSearch">
import { listHotSearch, getHotSearch, delHotSearch, addHotSearch, updateHotSearch, exportHotSearch } from "@/api/system/search/hot";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const hotList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 搜索类型选项
const search_type_options = ref([
  { label: "全部", value: "all" },
  { label: "咨询师", value: "consultant" },
  { label: "课程", value: "course" },
  { label: "冥想", value: "meditation" },
  { label: "测评", value: "assessment" }
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword: null,
    searchType: null,
    status: null
  },
  rules: {
    keyword: [
      { required: true, message: "关键词不能为空", trigger: "blur" }
    ],
    searchType: [
      { required: true, message: "搜索类型不能为空", trigger: "change" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询热门搜索列表 */
function getList() {
  loading.value = true;
  listHotSearch(queryParams.value).then(response => {
    hotList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    keyword: null,
    searchType: null,
    searchCount: 1,
    hotScore: 10.00,
    status: "0"
  };
  proxy.resetForm("hotRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加热门搜索";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getHotSearch(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改热门搜索";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["hotRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateHotSearch(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addHotSearch(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const hotIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除热门搜索编号为"' + hotIds + '"的数据项？').then(function() {
    return delHotSearch(hotIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/search/hot/export', {
    ...queryParams.value
  }, `hot_search_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
