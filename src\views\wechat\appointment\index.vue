<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="咨询师" prop="counselorId">
        <el-select v-model="queryParams.counselorId" placeholder="请选择咨询师" clearable style="width: 200px">
          <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
            :value="counselor.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="预约日期" prop="appointmentDate">
        <el-date-picker v-model="queryParams.appointmentDate" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px" />
      </el-form-item>
      <el-form-item label="预约状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="预约状态" clearable style="width: 150px">
          <el-option label="待确认" value="0" />
          <el-option label="已确认" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="已取消" value="3" />
          <el-option label="已过期" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户手机号" prop="userPhone">
        <el-input v-model="queryParams.userPhone" placeholder="请输入用户手机号" clearable style="width: 150px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="Check" :disabled="multiple" @click="handleBatchConfirm"
          v-hasPermi="['system:appointment:confirm']">批量确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Close" :disabled="multiple" @click="handleBatchCancel"
          v-hasPermi="['system:appointment:cancel']">批量取消</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:appointment:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="appointmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="预约ID" align="center" prop="id" width="80" />
      <el-table-column label="用户信息" align="center" width="120">
        <template #default="scope">
          <div>{{ scope.row.userName }}</div>
          <div style="color: #666; font-size: 12px;">{{ scope.row.userPhone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="咨询师" align="center" prop="counselorName" width="100" />
      <el-table-column label="预约时间" align="center" width="180">
        <template #default="scope">
          <div>{{ scope.row.appointmentDate }}</div>
          <div style="color: #666; font-size: 12px;">
            {{ scope.row.startTime }} - {{ scope.row.endTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="咨询类型" align="center" prop="consultationType" width="100" />
      <el-table-column label="预约状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="费用" align="center" prop="fee" width="80">
        <template #default="scope">
          ¥{{ scope.row.fee }}
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="paymentStatus" width="100">
        <template #default="scope">
          <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
            {{ getPaymentStatusText(scope.row.paymentStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="预约时间" align="center" prop="createTime" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="viewDetail(scope.row)">详情</el-button>
          <el-button link type="success" icon="Check" @click="confirmAppointment(scope.row)"
            v-if="scope.row.status === 0" v-hasPermi="['system:appointment:confirm']">确认</el-button>
          <el-button link type="danger" icon="Close" @click="cancelAppointment(scope.row)" v-if="scope.row.status <= 1"
            v-hasPermi="['system:appointment:cancel']">取消</el-button>
          <el-button link type="warning" icon="Edit" @click="editAppointment(scope.row)" v-if="scope.row.status <= 1"
            v-hasPermi="['system:appointment:edit']">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 预约详情对话框 -->
    <el-dialog title="预约详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border v-if="currentAppointment">
        <el-descriptions-item label="预约ID">{{ currentAppointment.id }}</el-descriptions-item>
        <el-descriptions-item label="预约状态">
          <el-tag :type="getStatusType(currentAppointment.status)">
            {{ getStatusText(currentAppointment.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用户姓名">{{ currentAppointment.userName }}</el-descriptions-item>
        <el-descriptions-item label="用户手机号">{{ currentAppointment.userPhone }}</el-descriptions-item>
        <el-descriptions-item label="咨询师">{{ currentAppointment.counselorName }}</el-descriptions-item>
        <el-descriptions-item label="咨询类型">{{ currentAppointment.consultationType }}</el-descriptions-item>
        <el-descriptions-item label="预约日期">{{ currentAppointment.appointmentDate }}</el-descriptions-item>
        <el-descriptions-item label="预约时间">
          {{ currentAppointment.startTime }} - {{ currentAppointment.endTime }}
        </el-descriptions-item>
        <el-descriptions-item label="咨询费用">¥{{ currentAppointment.fee }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <el-tag :type="getPaymentStatusType(currentAppointment.paymentStatus)">
            {{ getPaymentStatusText(currentAppointment.paymentStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="预约时间">{{ parseTime(currentAppointment.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="确认时间">
          {{ currentAppointment.confirmTime ? parseTime(currentAppointment.confirmTime) : '未确认' }}
        </el-descriptions-item>
        <el-descriptions-item label="用户需求" :span="2">
          {{ currentAppointment.userRequirement || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ currentAppointment.remark || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="取消原因" :span="2" v-if="currentAppointment.status === 3">
          {{ currentAppointment.cancelReason || '无' }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="success" @click="confirmAppointment(currentAppointment)"
            v-if="currentAppointment.status === 0" v-hasPermi="['system:appointment:confirm']">确认预约</el-button>
          <el-button type="danger" @click="cancelAppointment(currentAppointment)" v-if="currentAppointment.status <= 1"
            v-hasPermi="['system:appointment:cancel']">取消预约</el-button>
          <el-button @click="detailOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 取消预约对话框 -->
    <el-dialog title="取消预约" v-model="cancelOpen" width="500px" append-to-body>
      <el-form ref="cancelRef" :model="cancelForm" :rules="cancelRules" label-width="100px">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input v-model="cancelForm.cancelReason" type="textarea" :rows="4" placeholder="请输入取消原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="danger" @click="handleCancel">确认取消</el-button>
          <el-button @click="cancelOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Appointment">
import {
  listAppointment, getAppointment, confirmAppointment as confirmAppointmentApi,
  cancelAppointment as cancelAppointmentApi, batchConfirmAppointment, batchCancelAppointment
} from "@/api/wechat/appointment";
import { listConsultant } from "@/api/wechat/consultation/consultant";
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 预约表格数据
const appointmentList = ref([]);
// 咨询师列表
const counselorList = ref([]);
// 当前预约
const currentAppointment = ref(null);

// 对话框状态
const detailOpen = ref(false);
const cancelOpen = ref(false);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  counselorId: undefined,
  appointmentDate: [],
  status: undefined,
  userPhone: undefined
});

// 取消表单
const cancelForm = reactive({
  cancelReason: undefined
});

// 表单校验
const cancelRules = reactive({
  cancelReason: [
    { required: true, message: "取消原因不能为空", trigger: "blur" }
  ]
});

/** 获取状态类型 */
function getStatusType(status) {
  const statusMap = {
    0: 'warning',  // 待确认
    1: 'success',  // 已确认
    2: 'info',     // 已完成
    3: 'danger',   // 已取消
    4: 'info'      // 已过期
  };
  return statusMap[status] || 'info';
}

/** 获取状态文本 */
function getStatusText(status) {
  const statusMap = {
    0: '待确认',
    1: '已确认',
    2: '已完成',
    3: '已取消',
    4: '已过期'
  };
  return statusMap[status] || '未知';
}

/** 获取支付状态类型 */
function getPaymentStatusType(status) {
  const statusMap = {
    0: 'warning',  // 未支付
    1: 'success',  // 已支付
    2: 'danger',   // 已退款
    3: 'info'      // 退款中
  };
  return statusMap[status] || 'info';
}

/** 获取支付状态文本 */
function getPaymentStatusText(status) {
  const statusMap = {
    0: '未支付',
    1: '已支付',
    2: '已退款',
    3: '退款中'
  };
  return statusMap[status] || '未知';
}

/** 查询预约列表 */
function getList() {
  loading.value = true;
  listAppointment(queryParams).then(response => {
    appointmentList.value = response.rows || [];
    total.value = response.total || 0;
    loading.value = false;
  });
}

/** 获取咨询师列表 */
function getCounselorList() {
  listConsultant().then(response => {
    counselorList.value = response.rows || response.data || [];
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 查看详情 */
function viewDetail(row) {
  currentAppointment.value = row;
  detailOpen.value = true;
}

/** 确认预约 */
function confirmAppointment(row) {
  ElMessageBox.confirm('确认要确认该预约吗?', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  }).then(() => {
    confirmAppointmentApi(row.id).then(() => {
      ElMessage.success("确认成功");
      getList();
      if (detailOpen.value) {
        detailOpen.value = false;
      }
    });
  });
}

/** 取消预约 */
function cancelAppointment(row) {
  currentAppointment.value = row;
  cancelForm.cancelReason = undefined;
  cancelOpen.value = true;
}

/** 处理取消 */
function handleCancel() {
  proxy.$refs["cancelRef"].validate(valid => {
    if (valid) {
      cancelAppointmentApi(currentAppointment.value.id, cancelForm.cancelReason).then(() => {
        ElMessage.success("取消成功");
        cancelOpen.value = false;
        getList();
        if (detailOpen.value) {
          detailOpen.value = false;
        }
      });
    }
  });
}

/** 修改预约 */
function editAppointment(row) {
  // 这里可以跳转到预约编辑页面
  ElMessage.info("预约修改功能待实现");
}

/** 批量确认 */
function handleBatchConfirm() {
  ElMessageBox.confirm('确认要批量确认选中的预约吗?', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  }).then(() => {
    batchConfirmAppointment(ids.value).then(() => {
      ElMessage.success("批量确认成功");
      getList();
    });
  });
}

/** 批量取消 */
function handleBatchCancel() {
  ElMessageBox.prompt('请输入取消原因', '批量取消预约', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '取消原因不能为空'
  }).then(({ value }) => {
    batchCancelAppointment(ids.value, value).then(() => {
      ElMessage.success("批量取消成功");
      getList();
    });
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/appointment/export", {
    ...queryParams
  }, "appointment_" + new Date().getTime() + ".xlsx");
}

onMounted(() => {
  getCounselorList();
  getList();
});
</script>
