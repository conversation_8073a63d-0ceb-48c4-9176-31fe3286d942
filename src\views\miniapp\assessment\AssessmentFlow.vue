<template>
  <div class="assessment-flow">
    <!-- 测评开始页面 -->
    <div v-if="currentStep === 'start'" class="assessment-start">
      <div class="scale-info">
        <h2>{{ scaleInfo.name }}</h2>
        <p class="description">{{ scaleInfo.description }}</p>
        <div class="meta-info">
          <span>题目数量：{{ scaleInfo.questionCount }}</span>
          <span v-if="scaleInfo.timeLimit">时间限制：{{ formatTime(scaleInfo.timeLimit) }}</span>
        </div>
      </div>
      <div class="instructions">
        <h3>测评说明</h3>
        <div v-html="scaleInfo.instruction"></div>
      </div>
      <div class="actions">
        <el-button type="primary" size="large" @click="startAssessment">开始测评</el-button>
        <el-button @click="$router.back()">返回</el-button>
      </div>
    </div>

    <!-- 测评进行中页面 -->
    <div v-else-if="currentStep === 'assessment'" class="assessment-progress">
      <!-- 进度条 -->
      <div class="progress-bar">
        <el-progress :percentage="progressPercentage" :show-text="false" stroke-width="8" />
        <div class="progress-text">
          {{ currentQuestionNo }} / {{ totalQuestions }}
        </div>
      </div>

      <!-- 题目内容 -->
      <div class="question-content" v-if="currentQuestion">
        <h3 class="question-title">
          {{ currentQuestionNo }}. {{ currentQuestion.content }}
        </h3>

        <!-- 单选题 -->
        <div v-if="currentQuestion.type === 'single_choice'" class="question-options">
          <el-radio-group v-model="currentAnswer" @change="handleAnswerChange">
            <el-radio v-for="option in currentQuestion.options" :key="option.id" :value="option.value"
              class="option-item">
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 多选题 -->
        <div v-else-if="currentQuestion.type === 'multiple_choice'" class="question-options">
          <el-checkbox-group v-model="currentAnswer" @change="handleAnswerChange">
            <el-checkbox v-for="option in currentQuestion.options" :key="option.id" :value="option.value"
              class="option-item">
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 文本题 -->
        <div v-else-if="currentQuestion.type === 'text'" class="question-text">
          <el-input v-model="currentAnswer" type="textarea" :rows="4" placeholder="请输入您的答案"
            @input="handleAnswerChange" />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="question-actions">
        <el-button :disabled="!currentQuestion.hasPrevious" @click="previousQuestion">
          上一题
        </el-button>
        <el-button @click="saveProgress">保存进度</el-button>
        <el-button @click="pauseAssessment">暂停测评</el-button>
        <el-button type="primary" :disabled="!canProceed" @click="nextQuestion">
          {{ currentQuestion.hasNext ? '下一题' : '完成测评' }}
        </el-button>
      </div>

      <!-- 时间显示 -->
      <div class="time-info" v-if="scaleInfo.timeLimit">
        <span>剩余时间：{{ formatTime(remainingTime) }}</span>
      </div>
    </div>

    <!-- 测评完成页面 -->
    <div v-else-if="currentStep === 'completed'" class="assessment-result">
      <div class="result-header">
        <el-icon class="success-icon">
          <SuccessFilled />
        </el-icon>
        <h2>测评完成</h2>
      </div>

      <div class="result-content" v-if="assessmentResult">
        <div class="score-info">
          <h3>总分：{{ assessmentResult.totalScore }}</h3>
          <p class="result-level">{{ assessmentResult.resultLevel }}</p>
        </div>

        <div class="result-description">
          <h4>结果说明</h4>
          <p>{{ assessmentResult.resultDescription }}</p>
        </div>

        <div class="subscale-scores" v-if="assessmentResult.subscaleScores?.length">
          <h4>分量表得分</h4>
          <div class="score-item" v-for="score in assessmentResult.subscaleScores" :key="score.name">
            <span class="score-name">{{ score.name }}</span>
            <span class="score-value">{{ score.score }}</span>
          </div>
        </div>
      </div>

      <div class="result-actions">
        <el-button type="primary" @click="viewReport">查看详细报告</el-button>
        <el-button @click="backToList">返回列表</el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-loading="loading" class="loading-overlay" v-if="loading"></div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SuccessFilled } from '@element-plus/icons-vue'
import {
  getScaleDetail,
  startAssessment,
  getQuestion,
  submitAnswer,
  getNextQuestion,
  getPreviousQuestion,
  saveProgress,
  pauseAssessment,
  completeAssessment,
  getAssessmentProgress,
  getAssessmentStatus
} from '@/api/miniapp/assessment'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const currentStep = ref('start') // start, assessment, completed
const scaleInfo = ref({})
const sessionId = ref('')
const currentQuestion = ref(null)
const currentAnswer = ref('')
const currentQuestionNo = ref(1)
const totalQuestions = ref(0)
const assessmentResult = ref(null)
const remainingTime = ref(0)
const timer = ref(null)
const questionStartTime = ref(Date.now())

// 计算属性
const progressPercentage = computed(() => {
  if (totalQuestions.value === 0) return 0
  return Math.round((currentQuestionNo.value / totalQuestions.value) * 100)
})

const canProceed = computed(() => {
  if (!currentQuestion.value) return false
  if (currentQuestion.value.isRequired && !currentAnswer.value) return false
  return true
})

// 方法
const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

const loadScaleInfo = async () => {
  try {
    loading.value = true
    const response = await getScaleDetail(route.params.scaleId)
    scaleInfo.value = response.data
  } catch (error) {
    ElMessage.error('加载量表信息失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const startAssessment = async () => {
  try {
    loading.value = true
    const response = await startAssessment(route.params.scaleId)
    sessionId.value = response.data.sessionId
    currentQuestion.value = response.data.firstQuestion
    totalQuestions.value = response.data.scaleInfo.questionCount

    if (scaleInfo.value.timeLimit) {
      remainingTime.value = scaleInfo.value.timeLimit
      startTimer()
    }

    currentStep.value = 'assessment'
  } catch (error) {
    ElMessage.error('开始测评失败')
  } finally {
    loading.value = false
  }
}

const handleAnswerChange = () => {
  // 自动保存答案
  if (currentAnswer.value) {
    submitCurrentAnswer()
  }
}

const submitCurrentAnswer = async () => {
  if (!currentAnswer.value) return

  try {
    await submitAnswer(sessionId.value, {
      questionId: currentQuestion.value.questionId,
      answerContent: currentAnswer.value,
      responseTime: Date.now() - questionStartTime.value
    })
  } catch (error) {
    console.error('提交答案失败:', error)
  }
}

const nextQuestion = async () => {
  if (!canProceed.value) return

  await submitCurrentAnswer()

  if (currentQuestion.value.hasNext) {
    await loadNextQuestion()
  } else {
    await finishAssessment()
  }
}

const previousQuestion = async () => {
  if (!currentQuestion.value.hasPrevious) return

  try {
    loading.value = true
    const response = await getPreviousQuestion(sessionId.value)
    currentQuestion.value = response.data
    currentQuestionNo.value--
    currentAnswer.value = response.data.currentAnswer || ''
  } catch (error) {
    ElMessage.error('加载上一题失败')
  } finally {
    loading.value = false
  }
}

const loadNextQuestion = async () => {
  try {
    loading.value = true
    const response = await getNextQuestion(sessionId.value)
    currentQuestion.value = response.data
    currentQuestionNo.value++
    currentAnswer.value = response.data.currentAnswer || ''
  } catch (error) {
    ElMessage.error('加载下一题失败')
  } finally {
    loading.value = false
  }
}

const finishAssessment = async () => {
  try {
    loading.value = true
    const response = await completeAssessment(sessionId.value)
    assessmentResult.value = response.data
    currentStep.value = 'completed'
    stopTimer()
  } catch (error) {
    ElMessage.error('完成测评失败')
  } finally {
    loading.value = false
  }
}

const saveProgress = async () => {
  try {
    await saveProgress(sessionId.value)
    ElMessage.success('进度已保存')
  } catch (error) {
    ElMessage.error('保存进度失败')
  }
}

const pauseAssessment = async () => {
  try {
    await ElMessageBox.confirm('确定要暂停测评吗？您可以稍后继续。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await pauseAssessment(sessionId.value)
    ElMessage.success('测评已暂停')
    router.push('/assessment/records')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('暂停测评失败')
    }
  }
}

const viewReport = () => {
  router.push(`/assessment/report/${assessmentResult.value.recordId}`)
}

const backToList = () => {
  router.push('/assessment/scales')
}

const startTimer = () => {
  timer.value = setInterval(() => {
    remainingTime.value--
    if (remainingTime.value <= 0) {
      ElMessage.warning('时间已到，自动提交测评')
      finishAssessment()
    }
  }, 1000)
}

const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

// 生命周期
onMounted(() => {
  loadScaleInfo()
})

onUnmounted(() => {
  stopTimer()
})

// 页面离开确认
window.addEventListener('beforeunload', (e) => {
  if (currentStep.value === 'assessment') {
    e.preventDefault()
    e.returnValue = '测评正在进行中，确定要离开吗？'
  }
})
</script>

<style scoped>
.assessment-flow {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.assessment-start {
  text-align: center;
}

.scale-info h2 {
  color: #409eff;
  margin-bottom: 16px;
}

.meta-info {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 16px 0;
  color: #666;
}

.instructions {
  text-align: left;
  margin: 30px 0;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.progress-bar {
  position: relative;
  margin-bottom: 30px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-weight: bold;
}

.question-content {
  margin-bottom: 30px;
}

.question-title {
  margin-bottom: 20px;
  line-height: 1.6;
}

.question-options .option-item {
  display: block;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s;
}

.question-options .option-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.question-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.time-info {
  text-align: center;
  color: #e6a23c;
  font-weight: bold;
}

.assessment-result {
  text-align: center;
}

.result-header .success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.score-info {
  margin: 30px 0;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
}

.score-info h3 {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 8px;
}

.result-level {
  font-size: 18px;
  color: #67c23a;
  font-weight: bold;
}

.result-description,
.subscale-scores {
  text-align: left;
  margin: 20px 0;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.score-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.score-item:last-child {
  border-bottom: none;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}
</style>
