import Layout from '@/layout'

const assessmentRouter = {
  path: '/assessment',
  component: Layout,
  redirect: '/assessment/scale',
  name: 'Assessment',
  meta: {
    title: '心理测评',
    icon: 'psychology'
  },
  children: [
    // 后台管理路由
    {
      path: 'scale',
      component: () => import('@/views/system/assessment/scale/index'),
      name: 'AssessmentScale',
      meta: {
        title: '量表管理',
        icon: 'scale',
        roles: ['admin', 'assessment:scale:list']
      }
    },
    {
      path: 'question',
      component: () => import('@/views/system/assessment/question/index'),
      name: 'AssessmentQuestion',
      meta: {
        title: '题目管理',
        icon: 'question',
        roles: ['admin', 'assessment:question:list']
      }
    },
    {
      path: 'record',
      component: () => import('@/views/system/assessment/record/index'),
      name: 'AssessmentRecord',
      meta: {
        title: '测评记录',
        icon: 'record',
        roles: ['admin', 'assessment:record:list']
      }
    },
    {
      path: 'review',
      component: () => import('@/views/system/assessment/review/index'),
      name: 'AssessmentReview',
      meta: {
        title: '测评评价',
        icon: 'review',
        roles: ['admin', 'assessment:review:list']
      }
    },
    {
      path: 'stats',
      component: () => import('@/views/system/assessment/stats/index'),
      name: 'AssessmentStats',
      meta: {
        title: '统计分析',
        icon: 'stats',
        roles: ['admin', 'assessment:stats:view']
      }
    }
  ]
}

// 小程序用户测评路由
const miniappAssessmentRouter = {
  path: '/miniapp-assessment',
  component: Layout,
  redirect: '/miniapp-assessment/scales',
  name: 'MiniappAssessment',
  meta: {
    title: '用户测评',
    icon: 'user-assessment'
  },
  children: [
    {
      path: 'scales',
      component: () => import('@/views/miniapp/assessment/ScaleList'),
      name: 'AssessmentScales',
      meta: {
        title: '量表列表',
        icon: 'list'
      }
    },
    {
      path: 'scale/:id',
      component: () => import('@/views/miniapp/assessment/ScaleDetail'),
      name: 'AssessmentScaleDetail',
      meta: {
        title: '量表详情',
        hidden: true
      }
    },
    {
      path: 'flow/:scaleId',
      component: () => import('@/views/miniapp/assessment/AssessmentFlow'),
      name: 'AssessmentFlow',
      meta: {
        title: '测评流程',
        hidden: true
      }
    },
    {
      path: 'records',
      component: () => import('@/views/miniapp/assessment/RecordList'),
      name: 'AssessmentRecords',
      meta: {
        title: '测评记录',
        icon: 'history'
      }
    },
    {
      path: 'record/:id',
      component: () => import('@/views/miniapp/assessment/RecordDetail'),
      name: 'AssessmentRecordDetail',
      meta: {
        title: '记录详情',
        hidden: true
      }
    },
    {
      path: 'report/:recordId',
      component: () => import('@/views/miniapp/assessment/AssessmentReport'),
      name: 'AssessmentReport',
      meta: {
        title: '测评报告',
        hidden: true
      }
    },
    {
      path: 'favorites',
      component: () => import('@/views/miniapp/assessment/FavoriteScales'),
      name: 'FavoriteScales',
      meta: {
        title: '收藏量表',
        icon: 'star'
      }
    }
  ]
}

// 企业测评路由
const enterpriseAssessmentRouter = {
  path: '/enterprise-assessment',
  component: Layout,
  redirect: '/enterprise-assessment/plans',
  name: 'EnterpriseAssessment',
  meta: {
    title: '企业测评',
    icon: 'enterprise-assessment',
    roles: ['enterprise:admin']
  },
  children: [
    {
      path: 'plans',
      component: () => import('@/views/miniapp/enterprise/PlanList'),
      name: 'AssessmentPlans',
      meta: {
        title: '测评计划',
        icon: 'plan'
      }
    },
    {
      path: 'plan/:id',
      component: () => import('@/views/miniapp/enterprise/PlanDetail'),
      name: 'AssessmentPlanDetail',
      meta: {
        title: '计划详情',
        hidden: true
      }
    },
    {
      path: 'plan/create',
      component: () => import('@/views/miniapp/enterprise/PlanCreate'),
      name: 'CreateAssessmentPlan',
      meta: {
        title: '创建计划',
        hidden: true
      }
    },
    {
      path: 'participants',
      component: () => import('@/views/miniapp/enterprise/ParticipantList'),
      name: 'AssessmentParticipants',
      meta: {
        title: '参与者管理',
        icon: 'participants'
      }
    },
    {
      path: 'enterprise-stats',
      component: () => import('@/views/miniapp/enterprise/EnterpriseStats'),
      name: 'EnterpriseAssessmentStats',
      meta: {
        title: '企业统计',
        icon: 'enterprise-stats'
      }
    },
    {
      path: 'available-scales',
      component: () => import('@/views/miniapp/enterprise/AvailableScales'),
      name: 'AvailableScales',
      meta: {
        title: '可用量表',
        icon: 'available-scales'
      }
    }
  ]
}

export { assessmentRouter, miniappAssessmentRouter, enterpriseAssessmentRouter }
