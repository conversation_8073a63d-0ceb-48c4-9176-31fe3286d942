import request from '@/utils/request'

// 查询时间槽列表
export function listTimeSlot(query) {
  return request({
    url: '/system/timeSlot/list',
    method: 'get',
    params: query
  })
}

// 查询时间槽详细信息
export function getTimeSlot(id) {
  return request({
    url: `/system/timeSlot/${id}`,
    method: 'get'
  })
}

// 查询咨询师在指定日期范围内的时间槽
export function getSlotsByCounselorAndDateRange(counselorId, startDate, endDate) {
  return request({
    url: `/system/timeSlot/counselor/${counselorId}`,
    method: 'get',
    params: {
      startDate: startDate,
      endDate: endDate
    }
  })
}

// 获取格式化的时间槽数据（按日期和时间段分组）
export function getFormattedTimeSlots(counselorId, startDate, endDate) {
  return request({
    url: `/system/timeSlot/formatted/${counselorId}`,
    method: 'get',
    params: {
      startDate: startDate,
      endDate: endDate
    }
  })
}

// 查询指定日期的可用时间槽
export function getAvailableSlots(date, centerId, counselorId) {
  return request({
    url: '/system/timeSlot/available',
    method: 'get',
    params: {
      date: date,
      centerId: centerId,
      counselorId: counselorId
    }
  })
}

// 查询公开时间槽
export function getPublicSlots(startDate, endDate, centerId) {
  return request({
    url: '/system/timeSlot/public',
    method: 'get',
    params: {
      startDate: startDate,
      endDate: endDate,
      centerId: centerId
    }
  })
}

// 新增时间槽
export function addTimeSlot(data) {
  return request({
    url: '/system/timeSlot',
    method: 'post',
    data: data
  })
}

// 修改时间槽
export function updateTimeSlot(data) {
  return request({
    url: '/system/timeSlot',
    method: 'put',
    data: data
  })
}

// 批量更新时间槽状态
export function batchUpdateSlotStatus(slotIds, status) {
  return request({
    url: '/system/timeSlot/batchUpdateStatus',
    method: 'put',
    data: slotIds,
    params: {
      status: status
    }
  })
}

// 删除时间槽
export function delTimeSlot(ids) {
  return request({
    url: `/system/timeSlot/${ids}`,
    method: 'delete'
  })
}

// 为咨询师生成指定日期范围的时间槽
export function generateSlots(counselorId, startDate, endDate) {
  return request({
    url: `/system/timeSlot/generate/${counselorId}`,
    method: 'post',
    params: {
      startDate: startDate,
      endDate: endDate
    }
  })
}

// 为所有咨询师生成指定日期范围的时间槽
export function generateSlotsForAll(startDate, endDate) {
  return request({
    url: '/system/timeSlot/generateAll',
    method: 'post',
    params: {
      startDate: startDate,
      endDate: endDate
    }
  })
}

// 清理过期的时间槽
export function cleanExpiredSlots(beforeDate) {
  return request({
    url: '/system/timeSlot/cleanExpired',
    method: 'delete',
    params: {
      beforeDate: beforeDate
    }
  })
}

// 更新过期时间槽的状态
export function updateExpiredSlotStatus() {
  return request({
    url: '/system/timeSlot/updateExpired',
    method: 'put'
  })
}
