/**
 * 通用css样式布局处理
 * Copyright (c) 2019 ruoyi
 */

 /** 基础通用 **/
.pt5 {
	padding-top: 5px;
}
.pr5 {
	padding-right: 5px;
}
.pb5 {
	padding-bottom: 5px;
}
.mt5 {
	margin-top: 5px;
}
.mr5 {
	margin-right: 5px;
}
.mb5 {
	margin-bottom: 5px;
}
.mb8 {
	margin-bottom: 8px;
}
.ml5 {
	margin-left: 5px;
}
.mt10 {
	margin-top: 10px;
}
.mr10 {
	margin-right: 10px;
}
.mb10 {
	margin-bottom: 10px;
}
.ml10 {
	margin-left: 10px;
}
.mt20 {
	margin-top: 20px;
}
.mr20 {
	margin-right: 20px;
}
.mb20 {
	margin-bottom: 20px;
}
.ml20 {
	margin-left: 20px;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
	font-family: inherit;
	font-weight: 500;
	line-height: 1.1;
	color: inherit;
}

.el-form .el-form-item__label {
	font-weight: 700;
}
.el-dialog:not(.is-fullscreen) {
	margin-top: 6vh !important;
}

.el-dialog.scrollbar .el-dialog__body {
	overflow: auto;
	overflow-x: hidden;
	max-height: 70vh;
	padding: 10px 20px 0;
}

.el-table {
	.el-table__header-wrapper, .el-table__fixed-header-wrapper {
		th {
			word-break: break-word;
			background-color: #f8f8f9 !important;
			color: #515a6e;
			height: 40px !important;
			font-size: 13px;
		}
	}
	.el-table__body-wrapper {
		.el-button [class*="el-icon-"] + span {
			margin-left: 1px;
		}
	}
}

/** 表单布局 **/
.form-header {
    font-size:15px;
	color:#6379bb;
	border-bottom:1px solid #ddd;
	margin:8px 10px 25px 10px;
	padding-bottom:5px
}

/** 表格布局 **/
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  background-color: transparent !important;
}

/* 弹窗中的分页器 */
.el-dialog .pagination-container {
  position: static !important;
  margin: 10px 0 0 0;
  padding: 0 !important;
  
  .el-pagination {
    position: static;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pagination-container {
    .el-pagination {
      > .el-pagination__jump {
        display: none !important;
      }
      > .el-pagination__sizes {
        display: none !important;
      }
    }
  }
}

/* tree border */
.tree-border {
    margin-top: 5px;
    border: 1px solid var(--el-border-color-light, #e5e6e7);
    background: var(--el-bg-color, #FFFFFF) none;
    border-radius:4px;
    width: 100%;
}

.el-table .fixed-width .el-button--small {
	padding-left: 0;
	padding-right: 0;
	width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
	cursor: pointer;
	color: #409EFF;
	margin-left: 10px;
}

.el-table .el-dropdown, .el-icon-arrow-down {
	font-size: 12px;
}

.el-tree-node__content > .el-checkbox {
	margin-right: 8px;
}

.list-group-striped > .list-group-item {
	border-left: 0;
	border-right: 0;
	border-radius: 0;
	padding-left: 0;
	padding-right: 0;
}

.list-group {
	padding-left: 0px;
	list-style: none;
}

.list-group-item {
	border-bottom: 1px solid #e7eaec;
	border-top: 1px solid #e7eaec;
	margin-bottom: -1px;
	padding: 11px 0px;
	font-size: 13px;
}

.pull-right {
	float: right !important;
}

.el-card__header {
	padding: 14px 15px 7px !important;
	min-height: 40px;
}

.el-card__body {
	padding: 15px 20px 20px 20px !important;
}

.card-box {
	padding-right: 15px;
	padding-left: 15px;
	margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: #48D1CC;
  border-color: #48D1CC;
  color: #FFFFFF;
}

.el-button--cyan {
  background-color: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

/* text color */
.text-navy {
	color: #1ab394;
}

.text-primary {
	color: inherit;
}

.text-success {
	color: #1c84c6;
}

.text-info {
	color: #23c6c8;
}

.text-warning {
	color: #f8ac59;
}

.text-danger {
	color: #ed5565;
}

.text-muted {
	color: #888888;
}

/* image */
.img-circle {
	border-radius: 50%;
}

.img-lg {
	width: 120px;
	height: 120px;
}

.avatar-upload-preview {
	position: absolute;
	top: 50%;
	transform: translate(50%, -50%);
	width: 200px;
	height: 200px;
	border-radius: 50%;
	box-shadow: 0 0 4px #ccc;
	overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost{
	opacity: .8;
	color: #fff!important;
	background: #42b983!important;
}

/* 表格右侧工具栏样式 */
.top-right-btn {
	margin-left: auto;
}

/* 分割面板样式 */
.splitpanes.default-theme .splitpanes__pane {
  background-color: var(--splitpanes-default-bg) !important;
}
