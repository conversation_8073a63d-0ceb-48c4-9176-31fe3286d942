<!-- src/views/system/question/index.vue -->
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="题干标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入题干标题" clearable />
      </el-form-item>
      <!-- <el-form-item label="测量维度" prop="dimension">
        <el-select v-model="queryParams.dimension" placeholder="请选择维度">
          <el-option v-for="item in dimensionOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="questionList">
      <el-table-column label="显示顺序" prop="orderNum" width="100" />
      <el-table-column label="题干标题" prop="title" min-width="100" />
      <el-table-column label="题干" prop="scenario" min-width="200" />
      <el-table-column label="测量维度" prop="dimension" width="120" />
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button @click="handleUpdate(scope.row)" link type="primary">修改</el-button>
          <el-button @click="handleDelete(scope.row)" link type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 新增/修改弹窗 -->
    <el-dialog :title="title" v-model="open" width="600px">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="题干标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入题干标题" />
        </el-form-item>
        <el-form-item label="情景描述" prop="scenario">
          <el-input v-model="form.scenario" type="textarea" rows="4" />
        </el-form-item>
        <el-form-item label="测量维度" prop="dimension">
          <el-select v-model="form.dimension" placeholder="请选择维度">
            <el-option v-for="item in dimensionOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="显示顺序" prop="orderNum">
          <el-input-number v-model="form.orderNum" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div>
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </div>
      </template>

    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { listQuestion, getQuestion, addQuestion, updateQuestion, delQuestion, listWithOptions } from '@/api/wechat/question'
import { ElMessage, ElMessageBox } from 'element-plus'

// 数据状态
const loading = ref(true)
const total = ref(0)
const questionList = ref([])
const open = ref(false)
const title = ref('')
const dimensionOptions = ref(['认知维度', '情感维度', '行为维度']) // 示例维度选项

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: undefined,
  dimension: undefined
})

// 表单数据
const form = reactive({
  id: undefined,
  title: '',
  scenario: '',
  dimension: '',
  orderNum: 0
})

// 表单校验规则
const rules = reactive({
  title: [
    { required: true, message: '题干标题不能为空', trigger: 'blur' },
    { max: 200, message: '标题长度不能超过200个字符', trigger: 'blur' }
  ],
  scenario: [
    { required: true, message: '情景描述不能为空', trigger: 'blur' }
  ],
  dimension: [
    { required: true, message: '请选择测量维度', trigger: 'change' }
  ],
  orderNum: [
    { required: true, message: '显示顺序不能为空', trigger: 'blur' },
    { type: 'number', min: 0, message: '显示顺序最小值为0' }
  ]
})

// 生命周期钩子
onMounted(() => {
  getList()
})

/** 查询列表 */
function getList() {
  loading.value = true
  const data = listWithOptions()
  console.log(data, "dsadsa");

  listQuestion(queryParams).then(response => {
    questionList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.pageNum = 1
  queryParams.title = undefined
  queryParams.dimension = undefined
  getList()
}

/** 新增按钮操作 */
function handleAdd() {
  resetForm()
  open.value = true
  title.value = '添加题目'
}

/** 修改按钮操作 */
function handleUpdate(row) {
  resetForm()
  const id = row.id || row.id
  getQuestion(id).then(response => {
    Object.assign(form, response.data)
    open.value = true
    title.value = '修改题目'
  })
}

/** 表单重置 */
function resetForm() {
  form.value = {
    id: undefined,
    title: '',
    scenario: '',
    dimension: '',
    orderNum: 0
  }
}

/** 提交表单 */
function submitForm() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      if (form.id != null) {
        updateQuestion(form).then(response => {
          ElMessage.success('修改成功')
          open.value = false
          getList()
        })
      } else {
        addQuestion(form).then(response => {
          ElMessage.success('新增成功')
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || [row.id]
  ElMessageBox.confirm('确认删除选中题目？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    return delQuestion(ids)
  }).then(() => {
    getList()
    ElMessage.success('删除成功')
  }).catch(() => { })
}

/** 分页操作 */
function handlePaginationChange({ pageNum, pageSize }) {
  queryParams.pageNum = pageNum
  queryParams.pageSize = pageSize
  getList()
}

function cancel() {
  open.value = false
}
</script>