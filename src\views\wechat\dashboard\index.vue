<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon today-appointments">
              <el-icon>
                <Calendar />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ todayStats.appointments }}</div>
              <div class="stat-label">今日预约</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending-appointments">
              <el-icon>
                <Clock />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ todayStats.pending }}</div>
              <div class="stat-label">待确认预约</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon available-slots">
              <el-icon>
                <User />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ todayStats.availableSlots }}</div>
              <div class="stat-label">可用时间槽</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active-counselors">
              <el-icon>
                <UserFilled />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ todayStats.activeCounselors }}</div>
              <div class="stat-label">在线咨询师</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 今日排班和预约 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>今日排班</span>
              <el-button type="text" @click="goToSchedule">查看更多</el-button>
            </div>
          </template>
          <div class="schedule-list">
            <div v-for="slot in todaySchedule" :key="slot.id" class="schedule-item">
              <div class="schedule-time">{{ slot.startTime }} - {{ slot.endTime }}</div>
              <div class="schedule-counselor">{{ slot.counselorName }}</div>
              <div class="schedule-status">
                <el-tag :type="getSlotStatusType(slot.status)" size="small">
                  {{ getSlotStatusText(slot.status) }}
                </el-tag>
              </div>
            </div>
            <div v-if="todaySchedule.length === 0" class="empty-data">
              暂无排班数据
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>今日预约</span>
              <el-button type="text" @click="goToAppointment">查看更多</el-button>
            </div>
          </template>
          <div class="appointment-list">
            <div v-for="appointment in todayAppointments" :key="appointment.id" class="appointment-item">
              <div class="appointment-time">{{ appointment.startTime }} - {{ appointment.endTime }}</div>
              <div class="appointment-user">{{ appointment.userName }}</div>
              <div class="appointment-counselor">{{ appointment.counselorName }}</div>
              <div class="appointment-status">
                <el-tag :type="getAppointmentStatusType(appointment.status)" size="small">
                  {{ getAppointmentStatusText(appointment.status) }}
                </el-tag>
              </div>
            </div>
            <div v-if="todayAppointments.length === 0" class="empty-data">
              暂无预约数据
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>近7天预约趋势</span>
            </div>
          </template>
          <div ref="appointmentChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>咨询师工作量统计</span>
            </div>
          </template>
          <div ref="workloadChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" icon="Plus" @click="goToTimeSlot">管理时间槽</el-button>
            <el-button type="success" icon="Setting" @click="goToTimeRange">时间段设置</el-button>
            <el-button type="info" icon="Timer" @click="goToTimeTask">定时任务</el-button>
            <el-button type="warning" icon="Download" @click="exportTodayData">导出今日数据</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Dashboard">
import { getDashboardStats, getTodaySchedule, getTodayAppointments, getAppointmentTrend, getCounselorWorkload } from "@/api/wechat/dashboard";
import { ElMessage } from 'element-plus'
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue'
import { Calendar, Clock, User, UserFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const { proxy } = getCurrentInstance();

// 今日统计数据
const todayStats = reactive({
  appointments: 0,
  pending: 0,
  availableSlots: 0,
  activeCounselors: 0
});

// 今日排班
const todaySchedule = ref([]);
// 今日预约
const todayAppointments = ref([]);

// 图表引用
const appointmentChart = ref(null);
const workloadChart = ref(null);

/** 获取时间槽状态类型 */
function getSlotStatusType(status) {
  const statusMap = {
    0: 'success',  // 可用
    1: 'warning',  // 已预约
    2: 'danger',   // 不可用
    3: 'info'      // 已过期
  };
  return statusMap[status] || 'info';
}

/** 获取时间槽状态文本 */
function getSlotStatusText(status) {
  const statusMap = {
    0: '可用',
    1: '已预约',
    2: '不可用',
    3: '已过期'
  };
  return statusMap[status] || '未知';
}

/** 获取预约状态类型 */
function getAppointmentStatusType(status) {
  const statusMap = {
    0: 'warning',  // 待确认
    1: 'success',  // 已确认
    2: 'info',     // 已完成
    3: 'danger',   // 已取消
    4: 'info'      // 已过期
  };
  return statusMap[status] || 'info';
}

/** 获取预约状态文本 */
function getAppointmentStatusText(status) {
  const statusMap = {
    0: '待确认',
    1: '已确认',
    2: '已完成',
    3: '已取消',
    4: '已过期'
  };
  return statusMap[status] || '未知';
}

/** 获取仪表板统计数据 */
function getDashboardData() {
  getDashboardStats().then(response => {
    Object.assign(todayStats, response.data);
  });

  getTodaySchedule().then(response => {
    todaySchedule.value = response.data || [];
  });

  getTodayAppointments().then(response => {
    todayAppointments.value = response.data || [];
  });
}

/** 初始化预约趋势图表 */
function initAppointmentChart() {
  getAppointmentTrend().then(response => {
    const chartInstance = echarts.init(appointmentChart.value);
    const data = response.data || [];

    const option = {
      title: {
        text: '近7天预约趋势',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.date)
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: '预约数量',
        type: 'line',
        data: data.map(item => item.count),
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        }
      }]
    };

    chartInstance.setOption(option);
  });
}

/** 初始化工作量统计图表 */
function initWorkloadChart() {
  getCounselorWorkload().then(response => {
    const chartInstance = echarts.init(workloadChart.value);
    const data = response.data || [];

    const option = {
      title: {
        text: '咨询师工作量',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item'
      },
      series: [{
        name: '预约数量',
        type: 'pie',
        radius: '50%',
        data: data.map(item => ({
          name: item.counselorName,
          value: item.appointmentCount
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    chartInstance.setOption(option);
  });
}

/** 跳转到排班管理 */
function goToSchedule() {
  proxy.$router.push('/wechat/schedule');
}

/** 跳转到预约管理 */
function goToAppointment() {
  proxy.$router.push('/wechat/appointment');
}

/** 跳转到时间槽管理 */
function goToTimeSlot() {
  proxy.$router.push('/wechat/timeSlot');
}

/** 跳转到时间段设置 */
function goToTimeRange() {
  proxy.$router.push('/wechat/timeRange');
}

/** 跳转到定时任务 */
function goToTimeTask() {
  proxy.$router.push('/wechat/timeTask');
}

/** 导出今日数据 */
function exportTodayData() {
  const today = new Date().toISOString().split('T')[0];
  proxy.download("system/appointment/export", {
    appointmentDate: [today, today]
  }, `appointment_${today}.xlsx`);
  ElMessage.success("导出成功");
}

onMounted(() => {
  getDashboardData();

  nextTick(() => {
    initAppointmentChart();
    initWorkloadChart();
  });
});
</script>

<style scoped>
.stat-card {
  margin-bottom: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.today-appointments {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pending-appointments {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.available-slots {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.active-counselors {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-list,
.appointment-list {
  max-height: 300px;
  overflow-y: auto;
}

.schedule-item,
.appointment-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.schedule-item:last-child,
.appointment-item:last-child {
  border-bottom: none;
}

.schedule-time,
.appointment-time {
  width: 120px;
  font-weight: bold;
  color: #409EFF;
}

.schedule-counselor,
.appointment-user,
.appointment-counselor {
  flex: 1;
  margin: 0 10px;
}

.schedule-status,
.appointment-status {
  width: 80px;
  text-align: right;
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 40px 0;
}

.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
