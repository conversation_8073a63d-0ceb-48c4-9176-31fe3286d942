<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="咨询师" prop="consultantName">
        <el-input v-model="queryParams.consultantName" placeholder="请输入咨询师名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="评分" prop="rating">
        <el-select v-model="queryParams.rating" placeholder="请选择评分" clearable style="width: 240px">
          <el-option label="1星" value="1" />
          <el-option label="2星" value="2" />
          <el-option label="3星" value="3" />
          <el-option label="4星" value="4" />
          <el-option label="5星" value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="adminCheck">
        <el-select v-model="queryParams.adminCheck" placeholder="请选择审核状态" clearable style="width: 240px">
          <el-option label="待审核" value="0" />
          <el-option label="已通过" value="1" />
          <el-option label="已拒绝" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:consultantReview:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:consultantReview:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:consultantReview:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:consultantReview:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="View" @click="handlePendingReviews"
          v-hasPermi="['system:consultantReview:list']">待审核</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reviewList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="评价ID" align="center" prop="id" width="80" />
      <el-table-column label="咨询师" align="center" prop="consultantName" />
      <el-table-column label="用户名称" align="center" prop="userName" />
      <el-table-column label="评分" align="center" prop="rating" width="120">
        <template #default="scope">
          <el-rate v-model="scope.row.rating" disabled show-score text-color="#ff9900" />
        </template>
      </el-table-column>
      <el-table-column label="评价内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="审核状态" align="center" prop="adminCheck" width="100">
        <template #default="scope">
          <dict-tag :options="sys_audit_status" :value="scope.row.adminCheck" />
        </template>
      </el-table-column>
      <el-table-column label="咨询师回复" align="center" prop="consultantReply" :show-overflow-tooltip="true" />
      <el-table-column label="评价时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:consultantReview:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:consultantReview:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:consultantReview:remove']">删除</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="audit" v-if="scope.row.adminCheck == 0" icon="Check">审核</el-dropdown-item>
                <el-dropdown-item command="reply" v-if="scope.row.adminCheck == 1 && !scope.row.consultantReply"
                  icon="ChatDotRound">回复</el-dropdown-item>
                <el-dropdown-item command="statistics" icon="DataAnalysis">统计</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改评价对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="reviewRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="咨询师ID" prop="consultantId">
              <el-input-number v-model="form.consultantId" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户ID" prop="userId">
              <el-input-number v-model="form.userId" :min="1" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="咨询记录ID" prop="recordId">
              <el-input-number v-model="form.recordId" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评分" prop="rating">
              <el-rate v-model="form.rating" show-score text-color="#ff9900" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="评价内容" prop="content">
              <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入评价内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="审核状态" prop="adminCheck">
              <el-select v-model="form.adminCheck" placeholder="请选择审核状态" style="width: 100%">
                <el-option label="待审核" value="0" />
                <el-option label="已通过" value="1" />
                <el-option label="已拒绝" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否匿名" prop="isAnonymous">
              <el-radio-group v-model="form.isAnonymous">
                <el-radio :value="0">否</el-radio>
                <el-radio :value="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="咨询师回复" prop="consultantReply">
              <el-input v-model="form.consultantReply" type="textarea" :rows="3" placeholder="请输入咨询师回复" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 评价详情对话框 -->
    <el-dialog title="评价详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="评价ID">{{ detailForm.id }}</el-descriptions-item>
        <el-descriptions-item label="咨询记录ID">{{ detailForm.recordId }}</el-descriptions-item>
        <el-descriptions-item label="咨询师">{{ detailForm.consultantName }}</el-descriptions-item>
        <el-descriptions-item label="用户名称">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="评分" :span="2">
          <el-rate v-model="detailForm.rating" disabled show-score text-color="#ff9900" />
        </el-descriptions-item>
        <el-descriptions-item label="评价内容" :span="2">{{ detailForm.content }}</el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <dict-tag :options="sys_audit_status" :value="detailForm.adminCheck" />
        </el-descriptions-item>
        <el-descriptions-item label="是否匿名">
          <dict-tag :options="sys_yes_no" :value="detailForm.isAnonymous" />
        </el-descriptions-item>
        <el-descriptions-item label="咨询师回复" :span="2">{{ detailForm.consultantReply }}</el-descriptions-item>
        <el-descriptions-item label="评价时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ parseTime(detailForm.auditTime) }}</el-descriptions-item>
        <el-descriptions-item label="回复时间">{{ parseTime(detailForm.replyTime) }}</el-descriptions-item>
        <el-descriptions-item label="审核人">{{ detailForm.auditBy }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核评价" v-model="auditOpen" width="400px" append-to-body>
      <el-form ref="auditRef" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="adminCheck">
          <el-radio-group v-model="auditForm.adminCheck">
            <el-radio :value="1">通过</el-radio>
            <el-radio :value="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" :rows="3" placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitAudit">确 定</el-button>
          <el-button @click="auditOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 回复对话框 -->
    <el-dialog title="咨询师回复" v-model="replyOpen" width="500px" append-to-body>
      <el-form ref="replyRef" :model="replyForm" :rules="replyRules" label-width="80px">
        <el-form-item label="回复内容" prop="consultantReply">
          <el-input v-model="replyForm.consultantReply" type="textarea" :rows="4" placeholder="请输入回复内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitReply">确 定</el-button>
          <el-button @click="replyOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 待审核评价列表对话框 -->
    <el-dialog title="待审核评价列表" v-model="pendingOpen" width="1000px" append-to-body>
      <el-table v-loading="pendingLoading" :data="pendingList">
        <el-table-column label="咨询师" align="center" prop="consultantName" />
        <el-table-column label="用户名称" align="center" prop="userName" />
        <el-table-column label="评分" align="center" prop="rating" width="120">
          <template #default="scope">
            <el-rate v-model="scope.row.rating" disabled show-score text-color="#ff9900" />
          </template>
        </el-table-column>
        <el-table-column label="评价内容" align="center" prop="content" :show-overflow-tooltip="true" />
        <el-table-column label="评价时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button link type="primary" @click="handleAudit(scope.row)">审核</el-button>
            <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog title="评价统计信息" v-model="statisticsOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="平均评分">
          <el-rate v-model="statisticsData.avgRating" disabled show-score text-color="#ff9900" />
        </el-descriptions-item>
        <el-descriptions-item label="总评价数">{{ statisticsData.totalCount }}</el-descriptions-item>
        <el-descriptions-item label="5星评价">{{ statisticsData.rating5Count }}</el-descriptions-item>
        <el-descriptions-item label="4星评价">{{ statisticsData.rating4Count }}</el-descriptions-item>
        <el-descriptions-item label="3星评价">{{ statisticsData.rating3Count }}</el-descriptions-item>
        <el-descriptions-item label="2星评价">{{ statisticsData.rating2Count }}</el-descriptions-item>
        <el-descriptions-item label="1星评价">{{ statisticsData.rating1Count }}</el-descriptions-item>
        <el-descriptions-item label="最后更新">{{ parseTime(new Date()) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="ConsultantReview">
import {
  listConsultantReview, getConsultantReview, delConsultantReview, addConsultantReview, updateConsultantReview, exportConsultantReview,
  auditConsultantReview, replyConsultantReview, getPendingConsultantReviews, getApprovedConsultantReviews,
  getConsultantReviewStatistics, updateConsultantRating
} from "@/api/wechat/consultation/review";

const { proxy } = getCurrentInstance();
const { sys_audit_status, sys_yes_no } = proxy.useDict('sys_audit_status', 'sys_yes_no');

const reviewList = ref([]);
const pendingList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const auditOpen = ref(false);
const replyOpen = ref(false);
const pendingOpen = ref(false);
const statisticsOpen = ref(false);
const loading = ref(true);
const pendingLoading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  auditForm: {},
  replyForm: {},
  statisticsData: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    consultantName: null,
    userName: null,
    rating: null,
    adminCheck: null
  },
  rules: {
    consultantId: [
      { required: true, message: "咨询师ID不能为空", trigger: "blur" }
    ],
    userId: [
      { required: true, message: "用户ID不能为空", trigger: "blur" }
    ],
    recordId: [
      { required: true, message: "咨询记录ID不能为空", trigger: "blur" }
    ],
    rating: [
      { required: true, message: "评分不能为空", trigger: "change" }
    ],
    content: [
      { required: true, message: "评价内容不能为空", trigger: "blur" }
    ]
  },
  auditRules: {
    adminCheck: [
      { required: true, message: "审核结果不能为空", trigger: "change" }
    ]
  },
  replyRules: {
    consultantReply: [
      { required: true, message: "回复内容不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, auditForm, replyForm, statisticsData, rules, auditRules, replyRules } = toRefs(data);

/** 查询评价列表 */
function getList() {
  loading.value = true;
  listConsultantReview(queryParams.value).then(response => {
    reviewList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    consultantId: null,
    userId: null,
    recordId: null,
    rating: 5,
    content: null,
    adminCheck: "0",
    isAnonymous: 0,
    consultantReply: null,
    remark: null
  };
  proxy.resetForm("reviewRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加评价";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getConsultantReview(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改评价";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getConsultantReview(_id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["reviewRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateConsultantReview(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addConsultantReview(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除评价编号为"' + _ids + '"的数据项？').then(function () {
    return delConsultantReview(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/consultantReview/export', {
    ...queryParams.value
  }, `consultant_review_${new Date().getTime()}.xlsx`)
}

/** 查看待审核评价 */
function handlePendingReviews() {
  pendingLoading.value = true;
  getPendingConsultantReviews().then(response => {
    pendingList.value = response.data;
    pendingOpen.value = true;
    pendingLoading.value = false;
  });
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'audit':
      handleAudit(row);
      break;
    case 'reply':
      handleReply(row);
      break;
    case 'statistics':
      handleStatistics(row);
      break;
  }
}

/** 审核评价 */
function handleAudit(row) {
  auditForm.value = {
    id: row.id,
    adminCheck: null,
    auditRemark: null
  };
  auditOpen.value = true;
}

/** 提交审核 */
function submitAudit() {
  proxy.$refs["auditRef"].validate(valid => {
    if (valid) {
      auditConsultantReview(auditForm.value.id, auditForm.value.adminCheck).then(response => {
        proxy.$modal.msgSuccess("审核成功");
        auditOpen.value = false;
        getList();
      });
    }
  });
}

/** 咨询师回复 */
function handleReply(row) {
  replyForm.value = {
    id: row.id,
    consultantReply: null
  };
  replyOpen.value = true;
}

/** 提交回复 */
function submitReply() {
  proxy.$refs["replyRef"].validate(valid => {
    if (valid) {
      replyConsultantReview(replyForm.value.id, replyForm.value.consultantReply).then(response => {
        proxy.$modal.msgSuccess("回复成功");
        replyOpen.value = false;
        getList();
      });
    }
  });
}

/** 查看统计信息 */
function handleStatistics(row) {
  getConsultantReviewStatistics(row.consultantId).then(response => {
    statisticsData.value = response.data;
    statisticsOpen.value = true;
  });
}

onMounted(() => {
  getList();
});
</script>
