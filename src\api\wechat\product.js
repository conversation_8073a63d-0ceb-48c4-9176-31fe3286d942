import request from '@/utils/request'

// 查询产品列表
export function listProduct(query) {
  return request({
    url: '/psy/product/list',
    method: 'get',
    params: query
  })
}

// 查询产品详情
export function getProduct(productId) {
  return request({
    url: `/psy/product/${productId}`,
    method: 'get'
  })
}

// 新增产品
export function addProduct(data) {
  return request({
    url: '/psy/product',
    method: 'post',
    data: data
  })
}

// 修改产品
export function updateProduct(data) {
  return request({
    url: '/psy/product',
    method: 'put',
    data: data
  })
}

// 删除产品
export function delProduct(productIds) {
  return request({
    url: '/psy/product/' + productIds,
    method: 'delete'
  })
}

// 获取服务项目选项
export function getServiceOptions() {
  return request({
    url: '/psy/service/options',
    method: 'get'
  })
}