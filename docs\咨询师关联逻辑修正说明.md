# 咨询师关联逻辑修正说明

## 问题描述

在极速匹配问题管理中，咨询师关联功能存在以下问题：

1. **重复调用接口**：在咨询师选择弹框点击确定时就立即调用了 `updateOptionConsultants` 接口
2. **空数组覆盖**：在外层表单提交时又会再次调用关联咨询师接口，可能传递空数组导致关联咨询师被重置为空

## 修正方案

### 1. 延迟接口调用策略

**修正前的逻辑：**
```javascript
// 在咨询师选择弹框确认时立即调用接口
function confirmSelectConsultants() {
  if (currentOption.value) {
    currentOption.value.consultants = selectedConsultantIds.value.map(id => {
      return consultantList.value.find(c => c.id === id);
    }).filter(Boolean);
    
    // ❌ 问题：立即调用接口
    if (currentOption.value.id) {
      updateOptionConsultants(currentOption.value.id, selectedConsultantIds.value).then(() => {
        proxy.$modal.msgSuccess("关联咨询师成功");
      });
    }
  }
  consultantDialogVisible.value = false;
}
```

**修正后的逻辑：**
```javascript
// 只标记变更，不立即调用接口
function confirmSelectConsultants() {
  if (currentOption.value) {
    currentOption.value.consultants = selectedConsultantIds.value.map(id => {
      return consultantList.value.find(c => c.id === id);
    }).filter(Boolean);
    
    // ✅ 修正：只标记变更，延迟到表单提交时处理
    currentOption.value.consultantsChanged = true;
  }
  consultantDialogVisible.value = false;
}
```

### 2. 统一在表单提交时处理

**修正前的逻辑：**
```javascript
// 修改时：所有选项都会调用接口
updateQuestion(form.value).then(response => {
  // ❌ 问题：所有选项都调用接口，包括未修改的
  form.value.options.forEach(option => {
    if (option.id && option.consultants) {
      updateOptionConsultants(option.id, option.consultants.map(c => c.id));
    }
  });
});
```

**修正后的逻辑：**
```javascript
// 修改时：只处理被修改过的选项
updateQuestion(form.value).then(response => {
  // ✅ 修正：只更新被修改过的选项
  form.value.options.forEach(option => {
    if (option.id && option.consultantsChanged) {
      const consultantIds = option.consultants ? option.consultants.map(c => c.id) : [];
      updateOptionConsultants(option.id, consultantIds);
    }
  });
});
```

### 3. 变更标记管理

#### 添加 consultantsChanged 标记

**在添加选项时：**
```javascript
function handleAddOption() {
  form.value.options.push({
    optionText: "",
    valueCode: "",
    tagType: "",
    recommendTag: "",
    sort: form.value.options.length + 1,
    consultants: [],
    consultantsChanged: false  // ✅ 新增：变更标记
  });
}
```

**在修改问题时：**
```javascript
function handleUpdate(row) {
  getQuestion(questionId).then(response => {
    form.value = response.data;
    // ✅ 新增：确保每个选项都有变更标记
    if (form.value.options) {
      form.value.options.forEach(option => {
        if (!option.hasOwnProperty('consultantsChanged')) {
          option.consultantsChanged = false;
        }
      });
    }
  });
}
```

## 修正的核心逻辑

### 1. 变更追踪机制

- **标记变更**：当用户在咨询师选择弹框中修改咨询师时，设置 `consultantsChanged = true`
- **延迟处理**：不立即调用接口，而是等到表单提交时统一处理
- **精确更新**：只对被标记为已变更的选项调用关联接口

### 2. 接口调用时机

| 操作 | 修正前 | 修正后 |
|------|--------|--------|
| 咨询师选择确认 | ❌ 立即调用接口 | ✅ 只标记变更 |
| 表单提交 | ❌ 所有选项都调用 | ✅ 只调用变更的选项 |
| 新增问题 | ❌ 可能传递空数组 | ✅ 只处理有咨询师的选项 |

### 3. 数据一致性保障

```javascript
// 确保数据结构一致性
const consultantIds = option.consultants ? option.consultants.map(c => c.id) : [];
updateOptionConsultants(option.id, consultantIds);
```

## 修正效果

### 1. 避免重复调用
- ✅ 咨询师选择弹框确认时不再立即调用接口
- ✅ 只在表单提交时统一处理关联关系

### 2. 防止数据覆盖
- ✅ 不会因为传递空数组而清空已有的咨询师关联
- ✅ 只更新真正被修改过的选项

### 3. 提升用户体验
- ✅ 减少不必要的网络请求
- ✅ 避免中间状态的数据不一致
- ✅ 操作更加流畅和可预期

## 测试验证

### 1. 新增问题测试
1. 创建新问题
2. 为某些选项添加咨询师
3. 提交表单
4. 验证：只有添加了咨询师的选项会调用关联接口

### 2. 修改问题测试
1. 编辑已有问题
2. 修改某些选项的咨询师
3. 不修改其他选项
4. 提交表单
5. 验证：只有被修改的选项会调用关联接口

### 3. 咨询师选择测试
1. 打开咨询师选择弹框
2. 选择咨询师后点击确定
3. 验证：弹框关闭，但不立即调用接口
4. 提交外层表单
5. 验证：此时才调用关联接口

## 注意事项

### 1. 数据结构兼容性
- 确保所有选项都包含 `consultantsChanged` 字段
- 处理从后端获取的数据可能缺少此字段的情况

### 2. 错误处理
- 关联咨询师接口调用失败时的处理
- 部分选项关联成功，部分失败的情况

### 3. 性能考虑
- 减少不必要的接口调用
- 批量处理多个选项的关联关系

## 总结

通过引入变更追踪机制和延迟处理策略，成功解决了咨询师关联功能中的重复调用和数据覆盖问题：

1. **精确控制**：只在真正需要时调用关联接口
2. **数据安全**：避免意外清空已有的关联关系
3. **用户体验**：操作更加流畅，减少不必要的等待时间

这种修正方案既保证了功能的正确性，又提升了系统的性能和用户体验。
