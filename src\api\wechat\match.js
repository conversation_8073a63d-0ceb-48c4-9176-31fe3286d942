import request from '@/utils/request'

// 查询极速匹配问题列表
export function listQuestion(query) {
  return request({
    url: '/match/question/list',
    method: 'get',
    params: query
  })
}

// 查询极速匹配问题详细
export function getQuestion(questionId) {
  return request({
    url: '/match/question/' + questionId,
    method: 'get'
  })
}

// 新增极速匹配问题
export function addQuestion(data) {
  return request({
    url: '/match/question',
    method: 'post',
    data: data
  })
}

// 修改极速匹配问题
export function updateQuestion(data) {
  return request({
    url: '/match/question',
    method: 'put',
    data: data
  })
}

// 删除极速匹配问题
export function delQuestion(questionId) {
  return request({
    url: '/match/question/' + questionId,
    method: 'delete'
  })
}

// 获取选项关联的咨询师
export function getOptionConsultants(optionId) {
  return request({
    url: '/match/question/option/' + optionId + '/consultants',
    method: 'get'
  })
}

// 更新选项关联的咨询师
export function updateOptionConsultants(optionId, consultantIds) {
  return request({
    url: '/match/question/option/' + optionId + '/consultants',
    method: 'post',
    data: consultantIds
  })
}

